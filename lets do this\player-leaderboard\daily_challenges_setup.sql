-- Create table for daily challenges
CREATE TABLE IF NOT EXISTS koth_daily_challenges (
    id INT AUTO_INCREMENT PRIMARY KEY,
    challenge_date DATE NOT NULL,
    challenge_type VARCHAR(50) NOT NULL,
    challenge_name VARCHAR(100) NOT NULL,
    challenge_description VARCHAR(255) NOT NULL,
    target_value INT NOT NULL,
    reward_xp INT NOT NULL,
    reward_money INT NOT NULL,
    UNIQUE KEY unique_daily_challenge (challenge_date, challenge_type)
);

-- Create table for player challenge progress
CREATE TABLE IF NOT EXISTS koth_player_challenges (
    id INT AUTO_INCREMENT PRIMARY KEY,
    player_id INT NOT NULL,
    challenge_id INT NOT NULL,
    progress INT DEFAULT 0,
    completed BOOLEAN DEFAULT FALSE,
    completed_at TIMESTAMP NULL,
    FOREIGN KEY (player_id) REFERENCES koth_players(id),
    FOREIGN KEY (challenge_id) REFERENCES koth_daily_challenges(id),
    UNIQUE KEY unique_player_challenge (player_id, challenge_id)
);

-- Insert today's challenges (example)
INSERT INTO koth_daily_challenges (challenge_date, challenge_type, challenge_name, challenge_description, target_value, reward_xp, reward_money)
VALUES 
    (CURDATE(), 'zone_kills', 'Zone Dominator', 'Get 5 kills while in the KOTH zone', 5, 1500, 5000),
    (CURDATE(), 'team_wins', 'Team Player', 'Win 3 matches with your team', 3, 1000, 3000),
    (CURDATE(), 'killstreak', 'Survivor', 'Get 10 kills without dying', 10, 2500, 7500)
ON DUPLICATE KEY UPDATE 
    challenge_name = VALUES(challenge_name),
    challenge_description = VALUES(challenge_description);
