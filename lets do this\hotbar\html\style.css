* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    overflow: hidden;
    background: transparent;
}

.hotbar-container {
    position: fixed;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 1000;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.hotbar-container.visible {
    opacity: 1;
}

.hotbar {
    display: flex;
    gap: 8px;
    padding: 0;
}

.hotbar-slot {
    position: relative;
    width: 64px;
    height: 64px;
    background: linear-gradient(145deg, rgba(40, 40, 40, 0.9), rgba(20, 20, 20, 0.9));
    border-radius: 8px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    overflow: hidden;
}

.hotbar-slot:hover {
    border-color: rgba(255, 255, 255, 0.3);
    background: linear-gradient(145deg, rgba(50, 50, 50, 0.9), rgba(30, 30, 30, 0.9));
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3);
}

.hotbar-slot.active {
    border-color: #4CAF50;
    background: linear-gradient(145deg, rgba(76, 175, 80, 0.2), rgba(56, 142, 60, 0.2));
    box-shadow: 0 0 20px rgba(76, 175, 80, 0.4);
}



.slot-number {
    position: absolute;
    top: 2px;
    left: 4px;
    font-size: 10px;
    font-weight: bold;
    color: rgba(255, 255, 255, 0.7);
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
}

.slot-icon {
    width: 40px;
    height: 40px;
    margin-bottom: 2px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    filter: drop-shadow(1px 1px 2px rgba(0, 0, 0, 0.8));
    transition: none; /* Prevent flickering */
}

/* When slot has an image */
.slot-icon.has-image {
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
}

/* Empty slot styling */
.slot-icon:not(.has-image) {
    background-image: none;
}

/* Prevent any flickering or transitions */
.slot-icon * {
    transition: none;
    animation: none;
}

/* Hide any text content when image is present */
.slot-icon.has-image {
    font-size: 0;
    text-indent: -9999px;
}

.slot-count {
    position: absolute;
    bottom: 2px;
    right: 4px;
    font-size: 10px;
    font-weight: bold;
    color: #FFD700;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
    background: rgba(0, 0, 0, 0.6);
    padding: 1px 3px;
    border-radius: 3px;
    min-width: 12px;
    text-align: center;
}

.slot-count:empty {
    display: none;
}

.slot-cooldown {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.3);
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
}



/* Empty slot styling */
.hotbar-slot.empty {
    background: linear-gradient(145deg, rgba(30, 30, 30, 0.6), rgba(15, 15, 15, 0.6));
    border-color: rgba(255, 255, 255, 0.05);
}

.hotbar-slot.empty:hover {
    border-color: rgba(255, 255, 255, 0.1);
    background: linear-gradient(145deg, rgba(35, 35, 35, 0.6), rgba(20, 20, 20, 0.6));
}

/* Responsive design */
@media (max-width: 768px) {
    .hotbar-slot {
        width: 56px;
        height: 56px;
    }
    
    .slot-icon {
        font-size: 20px;
    }
    
    .hotbar {
        gap: 6px;
        padding: 8px;
    }
}
