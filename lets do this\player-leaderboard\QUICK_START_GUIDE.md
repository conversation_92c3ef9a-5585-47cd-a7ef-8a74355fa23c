# Quick Start Guide - Daily Challenges

## ✅ Database Setup Complete!

Your SQL import was successful. The daily challenges system is now ready.

## Next Steps:

### 1. Add Event Triggers to Your KOTH Gamemode

Open `koth_teamsel/server.lua` and add these event triggers:

```lua
-- Find your existing kill event handler and add:
RegisterNetEvent('koth:playerKilled', function(killerSource, victimSource, inZone)
    -- Your existing code...
    
    -- ADD THESE LINES for challenge tracking:
    if killerSource ~= victimSource then -- Not suicide
        TriggerEvent('leaderboard:playerKill') -- Track for killstreak
        
        if inZone then
            TriggerEvent('leaderboard:zoneKill') -- Track zone kills
        end
    end
    
    -- Track death for killstreak reset
    TriggerEvent('leaderboard:playerDied')
end)
```

### 2. Add Team Win Tracking (Optional - for "Team Player" challenge)

If you have a match/round system, add this when a team wins:

```lua
-- When announcing winner
function AnnounceWinner(winningTeam)
    -- Your existing code...
    
    -- ADD THIS LINE:
    TriggerEvent('leaderboard:teamWin', winningTeam)
end
```

### 3. Test It!

1. **Restart your server**
2. **Join the game**
3. **Press F7** - You'll see the 3 daily challenges
4. **Get a kill in the zone** - Watch "Zone Dominator" progress update
5. **Get kills without dying** - Watch "Survivor" progress update

### What Players Will See:

- **In Leaderboard (F7):**
  - 3 daily challenges with progress bars
  - Green checkmark when completed
  
- **When Completing a Challenge:**
  - Green notification popup
  - Shows XP and money earned
  - Plays completion sound
  - Money and XP instantly added

### Daily Reset

Challenges automatically reset at midnight server time. All players get the same 3 challenges each day.

## That's It!

The daily challenges are now live and will track automatically as players play. No additional configuration needed!
