// UI Fix - Consolidated script to handle all UI interactions properly
console.log('[KOTH UI FIX] Loading consolidated UI handler...');

// Global UI state
window.UIState = {
    currentMenu: null,
    isVisible: false,
    playerData: {
        money: 0,
        level: 1,
        xp: 0
    }
};

// Hide all UI elements initially
function hideAllUI() {
    console.log('[KOTH UI FIX] Hiding all UI elements');
    
    // Hide all menu containers
    const elements = [
        '#team-select',
        '#menu-container',
        '#weapons-shop',
        '#vehicles-shop',
        '#classes-selection',
        '#weapon-shop-new',
        '#death-screen',
        '#kill-reward-popup',
        '#levelup-popup',
        '#koth-zone-status'
    ];
    
    elements.forEach(selector => {
        const el = document.querySelector(selector);
        if (el) {
            el.style.display = 'none';
            el.classList.remove('active', 'show');
        }
    });
    
    // Hide overlay
    const overlay = document.getElementById('overlay');
    if (overlay) {
        overlay.style.display = 'none';
    }
    
    window.UIState.currentMenu = null;
    window.UIState.isVisible = false;
}

// Show specific UI element
function showUI(elementId) {
    console.log('[KOTH UI FIX] Showing UI element:', elementId);
    
    hideAllUI();
    
    const element = document.getElementById(elementId);
    const overlay = document.getElementById('overlay');
    
    if (element && overlay) {
        overlay.style.display = 'block';
        element.style.display = 'block';
        element.classList.add('active', 'show');
        
        window.UIState.currentMenu = elementId;
        window.UIState.isVisible = true;
        
        // Ensure the element is visible
        element.style.opacity = '1';
        element.style.visibility = 'visible';
        element.style.pointerEvents = 'auto';
    }
}

// Main message handler
window.addEventListener('message', function(event) {
    const data = event.data;
    
    // Only log non-spam messages
    if (data.action !== 'hideKothZone' && data.action !== 'updateKothZone') {
        console.log('[KOTH UI FIX] Received message:', data.action, data);
    }
    
    switch(data.action) {
        case 'hideAll':
            hideAllUI();
            break;
            
        case 'showTeamSelect':
            showUI('team-select');
            if (data.counts) {
                updateTeamCounts(data.counts);
            }
            break;
            
        case 'showMenu':
            if (data.type === 'vehicles') {
                showVehicleShop(data);
            } else if (data.type === 'classes') {
                showClassSelection(data);
            }
            break;
            
        case 'showClassSelection':
            showClassSelection(data);
            break;
            
        case 'showWeaponSelect':
            showWeaponShop(data);
            break;
            
        case 'updatePlayerData':
            updatePlayerData(data.data);
            break;
            
        case 'showKillReward':
            showKillReward(data);
            break;
            
        case 'showDeathScreen':
            showUI('death-screen');
            updateDeathScreen(data);
            break;
            
        case 'hideDeathScreen':
            const deathScreen = document.getElementById('death-screen');
            if (deathScreen) {
                deathScreen.style.display = 'none';
            }
            break;
            
        case 'hideKothZone':
            const kothZone = document.getElementById('koth-zone-status');
            if (kothZone) {
                kothZone.style.display = 'none';
            }
            break;
            
        case 'showClassShopWithData':
            showClassSelection(data);
            break;
    }
});

// Vehicle Shop Handler
function showVehicleShop(data) {
    console.log('[KOTH UI FIX] Showing vehicle shop with data:', data);
    
    showUI('vehicles-shop');
    
    // Update money display
    const moneyEl = document.getElementById('vehicle-player-money');
    if (moneyEl && data.money !== undefined) {
        moneyEl.textContent = data.money.toLocaleString();
    }
    
    // Populate vehicles
    const grid = document.getElementById('vehicles-grid');
    if (grid && data.items) {
        grid.innerHTML = '';
        
        data.items.forEach(vehicle => {
            const vehicleCard = createVehicleCard(vehicle);
            grid.appendChild(vehicleCard);
        });
    }
}

// Class Selection Handler
function showClassSelection(data) {
    console.log('[KOTH UI FIX] Showing class selection with data:', data);
    
    showUI('classes-selection');
    
    // Update player level for lock status - handle both data structures
    const playerLevel = data.playerLevel || data.level || window.UIState.playerData.level || 1;
    const playerMoney = data.money || window.UIState.playerData.money || 0;
    
    window.UIState.playerData.level = playerLevel;
    window.UIState.playerData.money = playerMoney;
    
    console.log('[KOTH UI FIX] Player level:', playerLevel, 'Money:', playerMoney);
    
    // Update class cards based on level
    const classCards = document.querySelectorAll('.class-card');
    classCards.forEach(card => {
        const className = card.dataset.class;
        const classData = getClassRequirements(className);
        
        if (classData && playerLevel < classData.requiredLevel) {
            card.classList.add('locked');
            const lockOverlay = card.querySelector('.class-lock-overlay');
            if (lockOverlay) {
                lockOverlay.style.display = 'flex';
            }
        } else {
            card.classList.remove('locked');
            const lockOverlay = card.querySelector('.class-lock-overlay');
            if (lockOverlay) {
                lockOverlay.style.display = 'none';
            }
        }
    });
}

// Weapon Shop Handler
function showWeaponShop(data) {
    console.log('[KOTH UI FIX] Showing weapon shop with data:', data);
    
    showUI('weapon-shop-new');
    
    // Update class name
    const classNameEl = document.getElementById('selected-class-name');
    if (classNameEl && data.class) {
        classNameEl.textContent = data.class.charAt(0).toUpperCase() + data.class.slice(1);
    }
    
    // Update money
    const moneyEl = document.getElementById('weapon-shop-money');
    if (moneyEl && data.money !== undefined) {
        moneyEl.textContent = data.money.toLocaleString();
    }
    
    // Populate weapons
    if (data.weapons) {
        populateWeapons(data.weapons);
    }
}

// Helper functions
function createVehicleCard(vehicle) {
    const card = document.createElement('div');
    card.className = 'vehicle-card';
    if (vehicle.owned) {
        card.classList.add('owned');
    }
    
    card.innerHTML = `
        <div class="vehicle-image">
            <img src="${vehicle.img || 'images/vehicles/default.png'}" alt="${vehicle.name}">
            ${vehicle.owned ? '<div class="owned-badge">OWNED</div>' : ''}
        </div>
        <div class="vehicle-info">
            <h3 class="vehicle-name">${vehicle.name}</h3>
            <div class="vehicle-prices">
                ${vehicle.owned ? 
                    '<button class="spawn-btn" data-vehicle="' + vehicle.name + '">SPAWN</button>' :
                    `<button class="buy-btn" data-vehicle="${vehicle.name}" data-price="${vehicle.cost}">BUY $${vehicle.cost.toLocaleString()}</button>
                     <button class="rent-btn" data-vehicle="${vehicle.name}" data-price="${vehicle.rent}">RENT $${vehicle.rent.toLocaleString()}</button>`
                }
            </div>
        </div>
    `;
    
    // Add click handlers
    const buyBtn = card.querySelector('.buy-btn');
    const rentBtn = card.querySelector('.rent-btn');
    const spawnBtn = card.querySelector('.spawn-btn');
    
    if (buyBtn) {
        buyBtn.addEventListener('click', () => {
            fetch(`https://${GetParentResourceName()}/buyVehicle`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    name: vehicle.name,
                    price: vehicle.cost
                })
            });
        });
    }
    
    if (rentBtn) {
        rentBtn.addEventListener('click', () => {
            fetch(`https://${GetParentResourceName()}/rentVehicle`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    name: vehicle.name,
                    price: vehicle.rent
                })
            });
        });
    }
    
    if (spawnBtn) {
        spawnBtn.addEventListener('click', () => {
            fetch(`https://${GetParentResourceName()}/buyVehicle`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    name: vehicle.name,
                    price: 0
                })
            });
        });
    }
    
    return card;
}

function getClassRequirements(className) {
    const requirements = {
        assault: { requiredLevel: 1 },
        medic: { requiredLevel: 5 },
        engineer: { requiredLevel: 15 },
        heavy: { requiredLevel: 25 },
        scout: { requiredLevel: 40 }
    };
    return requirements[className];
}

function populateWeapons(weapons) {
    const grid = document.getElementById('weapon-grid');
    if (!grid) return;
    
    grid.innerHTML = '';
    
    weapons.forEach(weapon => {
        const weaponCard = document.createElement('div');
        weaponCard.className = 'weapon-item';
        
        weaponCard.innerHTML = `
            <div class="weapon-image">
                <img src="${weapon.img || 'images/guns/default.png'}" alt="${weapon.name}">
            </div>
            <div class="weapon-name">${weapon.name}</div>
            <div class="weapon-price">$${weapon.price.toLocaleString()}</div>
        `;
        
        weaponCard.addEventListener('click', () => {
            fetch(`https://${GetParentResourceName()}/selectWeapon`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    weapon: weapon.weapon,
                    class: window.UIState.selectedClass,
                    price: weapon.price
                })
            });
        });
        
        grid.appendChild(weaponCard);
    });
}

function updateTeamCounts(counts) {
    if (counts.red !== undefined) {
        const redCount = document.getElementById('count-red');
        if (redCount) redCount.textContent = counts.red;
    }
    if (counts.blue !== undefined) {
        const blueCount = document.getElementById('count-blue');
        if (blueCount) blueCount.textContent = counts.blue;
    }
    if (counts.green !== undefined) {
        const greenCount = document.getElementById('count-green');
        if (greenCount) greenCount.textContent = counts.green;
    }
}

function updatePlayerData(data) {
    if (!data) return;
    
    // Update stored data
    Object.assign(window.UIState.playerData, data);
    
    // Update HUD elements
    if (data.player_name) {
        const nameEl = document.getElementById('player-name');
        if (nameEl) nameEl.textContent = data.player_name;
    }
    
    if (data.money !== undefined) {
        const moneyEl = document.getElementById('player-money-display');
        if (moneyEl) moneyEl.textContent = '$' + data.money.toLocaleString();
    }
    
    if (data.level !== undefined) {
        const levelEl = document.getElementById('player-level');
        if (levelEl) levelEl.textContent = data.level;
    }
    
    if (data.kills !== undefined) {
        const killsEl = document.getElementById('player-kills');
        if (killsEl) killsEl.textContent = data.kills;
    }
    
    if (data.xp !== undefined) {
        updateXPBar(data.xp, data.level);
    }
}

function updateXPBar(currentXP, level) {
    const xpForNextLevel = getXPForLevel(level + 1);
    const xpForCurrentLevel = getXPForLevel(level);
    const xpProgress = currentXP - xpForCurrentLevel;
    const xpNeeded = xpForNextLevel - xpForCurrentLevel;
    const percentage = (xpProgress / xpNeeded) * 100;
    
    const xpFill = document.getElementById('xp-fill');
    const xpText = document.getElementById('xp-text');
    
    if (xpFill) xpFill.style.width = percentage + '%';
    if (xpText) xpText.textContent = `${currentXP} / ${xpForNextLevel} XP`;
}

function getXPForLevel(level) {
    const levels = [0, 100, 250, 500, 1000, 1750, 2750, 4000, 6000, 8500];
    return levels[level - 1] || (8500 + (level - 10) * 2000);
}

function showKillReward(data) {
    const popup = document.getElementById('kill-reward-popup');
    if (!popup) return;
    
    // Update values
    const moneyEl = document.getElementById('kill-reward-value');
    const xpEl = document.getElementById('kill-xp-value');
    const zoneEl = document.getElementById('kill-reward-zone-indicator');
    
    if (moneyEl) moneyEl.textContent = '$' + (data.money || 50);
    if (xpEl) xpEl.textContent = data.xp || 50;
    if (zoneEl) zoneEl.style.display = data.inZone ? 'block' : 'none';
    
    // Show popup
    popup.style.display = 'block';
    popup.classList.add('show');
    
    // Hide after 3 seconds
    setTimeout(() => {
        popup.classList.remove('show');
        setTimeout(() => {
            popup.style.display = 'none';
        }, 300);
    }, 3000);
}

function updateDeathScreen(data) {
    if (data.killer) {
        const killerNameEl = document.getElementById('killer-name');
        if (killerNameEl) killerNameEl.textContent = data.killer;
    }
    
    if (data.killerId) {
        const killerIdEl = document.getElementById('killer-id');
        if (killerIdEl) killerIdEl.textContent = data.killerId;
    }
}

// Initialize close buttons
document.addEventListener('DOMContentLoaded', function() {
    console.log('[KOTH UI FIX] DOM loaded, initializing handlers...');
    
    // Hide all UI initially
    hideAllUI();
    
    // Close button handlers
    const closeButtons = [
        { selector: '#close-btn', action: 'closeMenu' },
        { selector: '#shop-close', action: 'closeMenu' },
        { selector: '#vehicle-shop-close', action: 'closeMenu' },
        { selector: '#classes-close', action: 'closeMenu' },
        { selector: '#weapon-shop-close', action: 'closeMenu' }
    ];
    
    closeButtons.forEach(btn => {
        const element = document.querySelector(btn.selector);
        if (element) {
            element.addEventListener('click', () => {
                hideAllUI();
                fetch(`https://${GetParentResourceName()}/${btn.action}`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({})
                });
            });
        }
    });
    
    // Team selection handlers
    const teamCards = document.querySelectorAll('.team-card');
    teamCards.forEach(card => {
        card.addEventListener('click', function() {
            const team = this.dataset.team;
            fetch(`https://${GetParentResourceName()}/selectTeam`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ team: team })
            });
        });
    });
    
    // Class selection handlers
    const classCards = document.querySelectorAll('.class-card');
    classCards.forEach(card => {
        card.addEventListener('click', function() {
            if (!this.classList.contains('locked')) {
                const classId = this.dataset.class;
                window.UIState.selectedClass = classId;
                fetch(`https://${GetParentResourceName()}/selectClass`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ id: classId })
                });
            }
        });
    });
    
    // Weapon category handlers
    const categoryButtons = document.querySelectorAll('.weapon-category');
    categoryButtons.forEach(btn => {
        btn.addEventListener('click', function() {
            categoryButtons.forEach(b => b.classList.remove('active'));
            this.classList.add('active');
            // Filter weapons by category (implement if needed)
        });
    });
    
    // Show HUD
    const gameHud = document.getElementById('game-hud');
    if (gameHud) {
        gameHud.style.display = 'block';
    }
});

// ESC key handler
document.addEventListener('keydown', function(event) {
    if (event.key === 'Escape' && window.UIState.isVisible) {
        hideAllUI();
        fetch(`https://${GetParentResourceName()}/closeMenu`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({})
        });
    }
});

console.log('[KOTH UI FIX] UI handler loaded successfully');
