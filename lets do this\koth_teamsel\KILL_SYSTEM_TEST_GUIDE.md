# Kill System Test Guide

## Summary of Fixes Applied

### 1. ✅ Respawn Timer Fixed (5 seconds)
- Changed `respawnHoldDuration` from 3000ms to 5000ms in `client_death.lua`
- The respawn bar will now take exactly 5 seconds to fill when holding E

### 2. ✅ Enhanced Kill Detection System
- Added damage tracking system that monitors who damaged the player
- Multiple fallback methods for killer detection
- Extensive debugging logs to track kill events

### 3. ✅ Kill Reward System Fixed
- Server properly validates kills and awards rewards
- Client shows kill reward popup and updates HUD
- Proper team checking to prevent teamkill rewards

## Testing Commands

### Client-Side Commands:
- `/testkill` - Test kill reward (add "zone" for zone kill)
  - Example: `/testkill` (normal kill - $50/50XP)
  - Example: `/testkill zone` (zone kill - $150/150XP)

- `/testui zone` - Test UI directly with zone kill
- `/forcekillreward` - Force display kill reward UI
- `/testkillpopup` - Test kill popup directly
- `/damageinfo` - Check who last damaged you
- `/deathstatus` - Check if you're dead and your team

### Server-Side Commands:
- `/simulatekill` - Simulate a kill event
- `/checkstats` - Check your current stats
- `/serverstats` - Check server player data (admin)

## How to Test Each Issue:

### 1. Test Respawn Timer (5 seconds)
1. Get killed by another player or use `/kill` command
2. When death screen appears, hold E
3. Watch the respawn bar - it should take exactly 5 seconds to fill
4. Release E to reset the bar, hold again to test

### 2. Test Kill Detection
1. Have two players on opposite teams
2. Player A kills Player B
3. Check console (F8) for these logs:
   - `[KOTH DEATH] Damage detected from player: [name]`
   - `[KOTH DEATH] KILL EVENT DEBUG`
   - `[KOTH SERVER] KILL EVENT RECEIVED`
4. If kill isn't detected, use `/damageinfo` to see last damage source

### 3. Test Kill Rewards
1. Kill a player on the opposite team
2. You should see:
   - Kill reward popup showing +$50 +50XP (or +$150 +150XP in zone)
   - HUD money/XP should update immediately
   - Console logs showing reward details
3. Kill a player in KOTH zone for triple rewards
4. Check `/checkstats` to verify stats updated

## Troubleshooting

### Kill Not Registering?
1. Check teams - must be opposite teams
2. Use `/damageinfo` to see if damage was tracked
3. Check server console for `[KOTH SERVER] KILL EVENT RECEIVED`
4. Ensure both players have data loaded (`/checkdata`)

### Kill Reward UI Not Showing?
1. Test with `/testkill` command first
2. Check browser console (F12) for JavaScript errors
3. Verify HTML elements exist in ui_simple.html
4. Try `/forcekillreward` to force display

### Respawn Bar Too Fast?
1. Verify client_death.lua has `respawnHoldDuration = 5000`
2. Check you're holding E continuously
3. The bar resets if you release E

## Expected Behavior

### Normal Kill (Opposite Team, Outside Zone):
- Killer receives: +$50 and +50 XP
- Kill popup shows: "Kill confirmed +$50 +50 xp"
- HUD updates with new money/XP
- Victim's death count increases

### Zone Kill (Opposite Team, Inside KOTH Zone):
- Killer receives: +$150 and +150 XP
- Kill popup shows: "Kill confirmed +$150 +150 xp ZONE KILL BONUS!"
- Zone kill indicator appears in orange
- Zone kills stat increases

### Invalid Kills (No Reward):
- Teamkills (same team)
- Self-kills
- NPC kills

## Debug Information

The system now includes extensive logging:
1. Client death system logs all killer detection attempts
2. Server logs show player names, IDs, and teams
3. Reward calculations are logged step by step
4. All client events are logged when received

Check both client console (F8) and server console for debug info.
