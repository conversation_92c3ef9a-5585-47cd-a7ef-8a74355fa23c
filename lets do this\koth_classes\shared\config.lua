Config = {}

-- Class definitions
Config.Classes = {
    assault = {
        name = "Assault",
        description = "Standard soldier class with balanced stats",
        requiredLevel = 1,
        abilities = {
            {
                name = "Ammo Bag",
                slot = 5,
                icon = "images/ammo_allammo.png",
                cooldown = 60, -- 60 seconds
                resupplyRadius = 5.0,
                duration = 30, -- Ammo bag lasts 30 seconds
                prop = "prop_box_ammo03a" -- Ammo box prop
            }
        }
    },
    medic = {
        name = "Medic",
        description = "Support class that can deploy healing stations",
        requiredLevel = 5,
        abilities = {
            {
                name = "Med Bag",
                slot = 5,
                icon = "images/ifak.png",
                cooldown = 60, -- 60 seconds
                healRadius = 5.0,
                healAmount = 5, -- HP per second
                duration = 30, -- Healing zone lasts 30 seconds
                prop = "prop_ld_health_pack" -- Health pack prop
            }
        }
    },
    engineer = {
        name = "Engineer",
        description = "Technical class with deployable equipment",
        requiredLevel = 15,
        abilities = {}
    },
    heavy = {
        name = "Heavy",
        description = "Tank class with increased armor",
        requiredLevel = 25,
        abilities = {
            {
                name = "Armor Kit",
                slot = 5,
                icon = "images/lightarmour.png",
                cooldown = 300, -- 5 minutes (300 seconds)
                armorAmount = 100 -- 100% armor
            }
        }
    },
    scout = {
        name = "Scout",
        description = "Precision marksman with stealth capabilities",
        requiredLevel = 35,
        abilities = {}
    }
}

-- Visual settings
Config.HealingZone = {
    color = { r = 0, g = 255, b = 0, a = 100 }, -- Green color
    markerType = 25, -- MarkerTypeHorizontalCircleSkinny
    bobUpAndDown = false,
    rotate = false
}

Config.AmmoZone = {
    color = { r = 255, g = 165, b = 0, a = 100 }, -- Orange color
    markerType = 25, -- MarkerTypeHorizontalCircleSkinny
    bobUpAndDown = false,
    rotate = false
}

-- Debug mode
Config.Debug = true
