-- Database integration for player leaderboard
local playerStats = {}

-- Function to get all online players with their teams and stats
local function getOnlinePlayersData()
    local players = GetPlayers()
    local onlineData = {
        red = {},
        blue = {},
        green = {}
    }
    
    for _, playerId in ipairs(players) do
        local txid = GetPlayerIdentifierByType(playerId, 'license')
        if txid then
            -- Get player data from database
            local result = exports.oxmysql:executeSync('SELECT * FROM koth_players WHERE txid = ?', {txid})
            if result and result[1] then
                local playerData = result[1]
                local team = playerData.current_team
                
                -- Normalize team value
                if team then
                    team = string.lower(string.match(tostring(team), "^%s*(.-)%s*$") or "")
                end
                
                if team and (team == 'red' or team == 'blue' or team == 'green') and onlineData[team] then
                    -- Calculate K/D ratio
                    local kd = 0
                    if playerData.deaths > 0 then
                        kd = math.floor((playerData.kills / playerData.deaths) * 100) / 100
                    else
                        kd = playerData.kills
                    end
                    
                    -- Calculate score (kills * 100 + zone_kills * 200 - deaths * 50)
                    local score = (playerData.kills * 100) + (playerData.zone_kills * 200) - (playerData.deaths * 50)
                    
                    -- Extract TX ID from txid 
                    -- Use database ID as TX ID for consistency
                    local txId = tostring(playerData.id)
                    
                    table.insert(onlineData[team], {
                        uid = txId,  -- Use TX ID instead of database ID
                        name = playerData.player_name,
                        score = score,
                        kills = playerData.kills,
                        deaths = playerData.deaths,
                        assists = 0, -- Not tracked in current database
                        kd = kd,
                        level = playerData.level,
                        money = playerData.money,
                        zone_kills = playerData.zone_kills,
                        xp = playerData.xp,
                        online = true
                    })
                end
            end
        end
    end
    
    -- Sort each team by score
    for team, players in pairs(onlineData) do
        table.sort(players, function(a, b) return a.score > b.score end)
    end
    
    return onlineData
end

-- Function to get top players from database (including offline)
local function getTopPlayersFromDatabase()
    local topPlayers = {
        red = {},
        blue = {},
        green = {}
    }
    
    -- First, let's get all players and filter by team in Lua
    local query = 'SELECT * FROM koth_players ORDER BY (kills * 100 + zone_kills * 200 - deaths * 50) DESC'
    
    print('[LEADERBOARD] Fetching all players from database...')
    local success, result = pcall(function()
        return exports.oxmysql:executeSync(query, {})
    end)
    
    if not success then
        print('[LEADERBOARD] Database error: ' .. tostring(result))
        return topPlayers
    end
    
    if result then
        print('[LEADERBOARD] Found ' .. #result .. ' players in database')
        
        -- Debug: Show all players and their teams
        for _, player in ipairs(result) do
            print('[LEADERBOARD] Player: ' .. player.player_name .. ' | Team: ' .. tostring(player.current_team) .. ' | Kills: ' .. player.kills)
        end
        
        -- Group players by team
        for _, player in ipairs(result) do
            local team = player.current_team
            
            -- Normalize team value
            if team then
                team = string.lower(string.match(tostring(team), "^%s*(.-)%s*$") or "")
            end
            
            -- Only process if team is valid and we haven't reached limit
            if team and (team == 'red' or team == 'blue' or team == 'green') and topPlayers[team] and #topPlayers[team] < 10 then
                -- Calculate K/D ratio
                local kd = 0
                if player.deaths > 0 then
                    kd = math.floor((player.kills / player.deaths) * 100) / 100
                else
                    kd = player.kills
                end
                
                -- Calculate score
                local score = (player.kills * 100) + (player.zone_kills * 200) - (player.deaths * 50)
                
                -- Extract TX ID from txid 
                -- Handle different formats: could be just numbers or full license string
                local txId = player.txid
                
                -- If it's a full license string, try to extract just the TX ID part
                if txId:match("license:") then
                    -- Try to find a pattern like "license:xxxxxx" where x is alphanumeric
                    local extracted = txId:match("license:([a-fA-F0-9]+)")
                    if extracted then
                        -- If it's a hex string, it might be a different format
                        -- For now, use the player's database ID as TX ID
                        txId = tostring(player.id)
                    end
                else
                    -- If it's already just numbers or another format, use as is
                    txId = tostring(player.id)
                end
                
                table.insert(topPlayers[team], {
                    uid = txId,  -- Use TX ID instead of database ID
                    name = player.player_name,
                    score = score,
                    kills = player.kills,
                    deaths = player.deaths,
                    assists = 0, -- Not tracked in current database
                    kd = kd,
                    level = player.level,
                    money = player.money,
                    zone_kills = player.zone_kills,
                    xp = player.xp,
                    online = false -- Will be updated if player is online
                })
            end
        end
        
        -- Debug: Show team counts
        for team, players in pairs(topPlayers) do
            print('[LEADERBOARD] Team ' .. team .. ' has ' .. #players .. ' players')
        end
    else
        print('[LEADERBOARD] No players found in database')
    end
    
    return topPlayers
end

-- Function to ensure daily challenges exist for today
local function ensureDailyChallenges()
    local currentDate = os.date('%Y-%m-%d')
    
    -- Check if today's challenges exist
    local existing = exports.oxmysql:executeSync('SELECT COUNT(*) as count FROM koth_daily_challenges WHERE challenge_date = ?', {currentDate})
    
    if existing[1].count == 0 then
        -- Create today's challenges
        exports.oxmysql:execute([[
            INSERT INTO koth_daily_challenges (challenge_date, challenge_type, challenge_name, challenge_description, target_value, reward_xp, reward_money)
            VALUES 
                (?, 'zone_kills', 'Zone Dominator', 'Get 5 kills while in the KOTH zone', 5, 1500, 5000),
                (?, 'team_wins', 'Team Player', 'Win 3 matches with your team', 3, 1000, 3000),
                (?, 'killstreak', 'Survivor', 'Get 10 kills without dying', 10, 2500, 7500)
        ]], {currentDate, currentDate, currentDate})
        
        print('[LEADERBOARD] Created daily challenges for ' .. currentDate)
    end
end

-- Function to get daily challenges with player progress
local function getDailyChallengesForPlayer(playerId)
    local currentDate = os.date('%Y-%m-%d')
    local challenges = {}
    
    -- Get player's database ID
    local playerData = exports.oxmysql:executeSync('SELECT id FROM koth_players WHERE txid = ?', {playerId})
    if not playerData or not playerData[1] then
        return challenges
    end
    
    local playerDbId = playerData[1].id
    
    -- Get today's challenges with player progress
    local result = exports.oxmysql:executeSync([[
        SELECT 
            dc.id,
            dc.challenge_type,
            dc.challenge_name,
            dc.challenge_description,
            dc.target_value,
            dc.reward_xp,
            dc.reward_money,
            COALESCE(pc.progress, 0) as progress,
            COALESCE(pc.completed, 0) as completed
        FROM koth_daily_challenges dc
        LEFT JOIN koth_player_challenges pc ON dc.id = pc.challenge_id AND pc.player_id = ?
        WHERE dc.challenge_date = ?
    ]], {playerDbId, currentDate})
    
    for _, challenge in ipairs(result) do
        table.insert(challenges, {
            id = challenge.id,
            type = challenge.challenge_type,
            name = challenge.challenge_name,
            description = challenge.challenge_description,
            progress = challenge.progress,
            target = challenge.target_value,
            completed = challenge.completed == 1,
            reward = {
                xp = challenge.reward_xp,
                cash = challenge.reward_money
            }
        })
    end
    
    return challenges
end

-- Function to update challenge progress
local function updateChallengeProgress(playerId, challengeType, increment)
    local currentDate = os.date('%Y-%m-%d')
    
    -- Get player's database ID
    local playerData = exports.oxmysql:executeSync('SELECT id FROM koth_players WHERE txid = ?', {playerId})
    if not playerData or not playerData[1] then
        return
    end
    
    local playerDbId = playerData[1].id
    
    -- Get the challenge for today
    local challenge = exports.oxmysql:executeSync([[
        SELECT id, target_value, reward_xp, reward_money 
        FROM koth_daily_challenges 
        WHERE challenge_date = ? AND challenge_type = ?
    ]], {currentDate, challengeType})
    
    if not challenge or not challenge[1] then
        return
    end
    
    local challengeId = challenge[1].id
    local targetValue = challenge[1].target_value
    local rewardXp = challenge[1].reward_xp
    local rewardMoney = challenge[1].reward_money
    
    -- Get current progress
    local currentProgress = exports.oxmysql:executeSync([[
        SELECT progress, completed 
        FROM koth_player_challenges 
        WHERE player_id = ? AND challenge_id = ?
    ]], {playerDbId, challengeId})
    
    local progress = 0
    local completed = false
    
    if currentProgress and currentProgress[1] then
        progress = currentProgress[1].progress
        completed = currentProgress[1].completed == 1
    end
    
    -- Don't update if already completed
    if completed then
        return
    end
    
    -- Update progress
    progress = progress + increment
    
    -- Check if challenge is now completed
    if progress >= targetValue then
        progress = targetValue
        completed = true
        
        -- Award rewards
        exports.oxmysql:execute([[
            UPDATE koth_players 
            SET xp = xp + ?, money = money + ? 
            WHERE id = ?
        ]], {rewardXp, rewardMoney, playerDbId})
        
        -- Notify player
        TriggerClientEvent('koth:challengeCompleted', playerId, {
            name = challenge[1].challenge_name or challengeType,
            xp = rewardXp,
            money = rewardMoney
        })
        
        print(('[LEADERBOARD] Player %s completed challenge %s - Awarded %d XP and $%d'):format(
            playerId, challengeType, rewardXp, rewardMoney))
    end
    
    -- Insert or update progress
    exports.oxmysql:execute([[
        INSERT INTO koth_player_challenges (player_id, challenge_id, progress, completed, completed_at)
        VALUES (?, ?, ?, ?, ?)
        ON DUPLICATE KEY UPDATE 
            progress = VALUES(progress),
            completed = VALUES(completed),
            completed_at = VALUES(completed_at)
    ]], {playerDbId, challengeId, progress, completed, completed and os.date('%Y-%m-%d %H:%M:%S') or nil})
end

-- Handle client request for data
RegisterNetEvent('leaderboard:requestData')
AddEventHandler('leaderboard:requestData', function()
    local source = source
    local txid = GetPlayerIdentifierByType(source, 'license')
    
    if not txid then
        print('[LEADERBOARD] Could not get player identifier')
        return
    end
    
    print('[LEADERBOARD] Player requesting data: ' .. txid)
    
    -- Get current player's data from database
    local playerResult = exports.oxmysql:executeSync('SELECT * FROM koth_players WHERE txid = ?', {txid})
    local currentPlayerData = nil
    
    if playerResult and playerResult[1] then
        local player = playerResult[1]
        
        -- Calculate K/D ratio
        local kd = 0
        if player.deaths > 0 then
            kd = math.floor((player.kills / player.deaths) * 100) / 100
        else
            kd = player.kills
        end
        
        -- Extract TX ID from txid 
        -- Use database ID as TX ID for consistency
        local txId = tostring(player.id)
        
        currentPlayerData = {
            uid = txId,  -- Use TX ID instead of database ID
            name = player.player_name,
            level = player.level,
            prestige = 0, -- Not implemented yet
            kills = player.kills,
            deaths = player.deaths,
            assists = 0, -- Not tracked in current database
            captures = player.zone_kills, -- Using zone kills as captures
            score = (player.kills * 100) + (player.zone_kills * 200) - (player.deaths * 50),
            money = player.money,
            xp = player.xp,
            team = player.current_team,
            kd = kd
        }
        
        print('[LEADERBOARD] Found player data for: ' .. player.player_name .. ' (Team: ' .. tostring(player.current_team) .. ')')
    else
        -- Default data if player not found
        currentPlayerData = {
            uid = 0,
            name = GetPlayerName(source),
            level = 1,
            prestige = 0,
            kills = 0,
            deaths = 0,
            assists = 0,
            captures = 0,
            score = 0,
            money = 1000,
            xp = 0,
            team = 'none',
            kd = 0
        }
        
        print('[LEADERBOARD] Player not found in database, using default data')
    end
    
    -- Get ONLY online players data
    local onlinePlayers = getOnlinePlayersData()
    
    -- Use only online players for the leaderboard
    local topPlayers = onlinePlayers
    
    -- Ensure daily challenges exist
    ensureDailyChallenges()
    
    -- Get daily challenges with player progress
    local challenges = getDailyChallengesForPlayer(txid)
    
    -- Prepare final data structure
    local leaderboardData = {
        currentPlayer = currentPlayerData,
        challenges = challenges,
        leaderboard = topPlayers
    }
    
    -- Debug: Log what we're sending
    print('[LEADERBOARD] Sending data to client:')
    print('  - Current player: ' .. currentPlayerData.name .. ' (Team: ' .. tostring(currentPlayerData.team) .. ')')
    for team, players in pairs(topPlayers) do
        print('  - Team ' .. team .. ': ' .. #players .. ' players')
    end
    
    -- Send data to client
    TriggerClientEvent('leaderboard:receiveData', source, leaderboardData)
end)

-- Function to update player stats (can be called by other resources)
function UpdatePlayerStats(playerId, statType, value)
    local txid = GetPlayerIdentifierByType(playerId, 'license')
    if not txid then return end
    
    -- Update specific stat in database
    if statType == 'kills' or statType == 'deaths' or statType == 'zone_kills' then
        exports.oxmysql:execute('UPDATE koth_players SET ' .. statType .. ' = ' .. statType .. ' + ? WHERE txid = ?', {value, txid})
    end
end

-- Export the function so other resources can use it
exports('UpdatePlayerStats', UpdatePlayerStats)

-- Removed auto-refresh to prevent conflicts - data is now fetched fresh each time the menu is opened

-- Add command to manually refresh leaderboard
RegisterCommand('refreshleaderboard', function(source, args, rawCommand)
    if source > 0 then
        TriggerEvent('leaderboard:requestData', source)
        TriggerClientEvent('chat:addMessage', source, {
            color = {0, 255, 0},
            multiline = true,
            args = {"[LEADERBOARD]", "Leaderboard data refreshed!"}
        })
    end
end, false)

-- Debug command to check database columns
RegisterCommand('checkleaderboarddb', function(source, args, rawCommand)
    if source == 0 then -- Server console only
        print('[LEADERBOARD] Checking database structure...')
        local result = exports.oxmysql:executeSync('SELECT * FROM koth_players LIMIT 1', {})
        if result and result[1] then
            print('[LEADERBOARD] Available columns:')
            for k, v in pairs(result[1]) do
                print('  - ' .. k .. ' (' .. type(v) .. '): ' .. tostring(v))
            end
        else
            print('[LEADERBOARD] No data found in koth_players table')
        end
    end
end, true)

-- Debug command to check team assignments
RegisterCommand('checkteams', function(source, args, rawCommand)
    if source == 0 then -- Server console only
        print('[LEADERBOARD] Checking team assignments...')
        local result = exports.oxmysql:executeSync('SELECT player_name, current_team, kills, deaths FROM koth_players WHERE current_team IS NOT NULL', {})
        if result then
            print('[LEADERBOARD] Players with teams:')
            for _, player in ipairs(result) do
                print(string.format('  - %s | Team: %s | Kills: %d | Deaths: %d', 
                    player.player_name, 
                    tostring(player.current_team), 
                    player.kills, 
                    player.deaths
                ))
            end
            
            -- Count by team
            local teamCounts = exports.oxmysql:executeSync('SELECT current_team, COUNT(*) as count FROM koth_players WHERE current_team IS NOT NULL GROUP BY current_team', {})
            if teamCounts then
                print('[LEADERBOARD] Team distribution:')
                for _, team in ipairs(teamCounts) do
                    print('  - Team ' .. tostring(team.current_team) .. ': ' .. team.count .. ' players')
                end
            end
        else
            print('[LEADERBOARD] No players with teams found')
        end
    end
end, true)

-- DAILY CHALLENGE EVENTS - Called by KOTH gamemode

-- Track killstreaks for Survivor challenge
local playerKillstreaks = {}

-- Event: Player got a zone kill
RegisterNetEvent('leaderboard:zoneKill')
AddEventHandler('leaderboard:zoneKill', function(playerId)
    -- Use provided playerId or fall back to source
    local targetPlayer = playerId or source
    local txid = GetPlayerIdentifierByType(targetPlayer, 'license')
    if not txid then 
        print('[LEADERBOARD] Could not get txid for player ' .. targetPlayer)
        return 
    end
    
    print('[LEADERBOARD] Zone kill tracked for player ' .. targetPlayer .. ' (txid: ' .. txid .. ')')
    
    -- Update zone kills challenge
    updateChallengeProgress(txid, 'zone_kills', 1)
    
    -- Also update killstreak
    playerKillstreaks[targetPlayer] = (playerKillstreaks[targetPlayer] or 0) + 1
    
    -- Check if they reached 10 kills without dying
    if playerKillstreaks[targetPlayer] >= 10 then
        updateChallengeProgress(txid, 'killstreak', 10)
        playerKillstreaks[targetPlayer] = 0 -- Reset after completing
    end
end)

-- Event: Player died (reset killstreak)
RegisterNetEvent('leaderboard:playerDied')
AddEventHandler('leaderboard:playerDied', function(playerId)
    -- Use provided playerId or fall back to source
    local targetPlayer = playerId or source
    playerKillstreaks[targetPlayer] = 0
    print('[LEADERBOARD] Killstreak reset for player ' .. targetPlayer)
end)

-- Event: Team won a match
RegisterNetEvent('leaderboard:teamWin')
AddEventHandler('leaderboard:teamWin', function(winningTeam)
    -- Get all players on the winning team
    local players = GetPlayers()
    
    for _, playerId in ipairs(players) do
        local txid = GetPlayerIdentifierByType(playerId, 'license')
        if txid then
            -- Get player's team
            local playerData = exports.oxmysql:executeSync('SELECT current_team FROM koth_players WHERE txid = ?', {txid})
            if playerData and playerData[1] and playerData[1].current_team == winningTeam then
                -- Update team wins challenge
                updateChallengeProgress(txid, 'team_wins', 1)
            end
        end
    end
end)

-- Event: Regular kill (for killstreak tracking)
RegisterNetEvent('leaderboard:playerKill')
AddEventHandler('leaderboard:playerKill', function(playerId)
    -- Use provided playerId or fall back to source
    local targetPlayer = playerId or source
    playerKillstreaks[targetPlayer] = (playerKillstreaks[targetPlayer] or 0) + 1
    
    local txid = GetPlayerIdentifierByType(targetPlayer, 'license')
    if not txid then 
        print('[LEADERBOARD] Could not get txid for player ' .. targetPlayer)
        return 
    end
    
    print('[LEADERBOARD] Kill tracked for player ' .. targetPlayer .. ' - Killstreak: ' .. playerKillstreaks[targetPlayer])
    
    -- Check if they reached 10 kills without dying
    if playerKillstreaks[targetPlayer] >= 10 then
        updateChallengeProgress(txid, 'killstreak', 10)
        playerKillstreaks[targetPlayer] = 0 -- Reset after completing
    end
end)

-- Clean up when player disconnects
AddEventHandler('playerDropped', function()
    local source = source
    playerKillstreaks[source] = nil
end)

-- Initialize challenges on resource start
AddEventHandler('onResourceStart', function(resourceName)
    if GetCurrentResourceName() ~= resourceName then
        return
    end
    
    -- Ensure daily challenges exist
    ensureDailyChallenges()
    print('[LEADERBOARD] Daily challenges system initialized')
end)

-- Export challenge progress update function for external use
exports('updateChallengeProgress', updateChallengeProgress)
