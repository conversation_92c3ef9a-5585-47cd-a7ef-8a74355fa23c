# Remove Ambient Peds Resource (Enhanced Version 2.0)

This FiveM resource completely removes all ambient peds, vehicles, police, EMS, and other NPCs from your server using multiple aggressive removal methods.

## Features

- **Removes all ambient peds** - No random pedestrians walking around
- **Removes all ambient vehicles** - No random cars driving or parked
- **Removes police** - No police spawning or responding to crimes
- **Removes EMS/Fire** - No ambulances or fire trucks
- **Removes dispatch services** - All emergency services disabled
- **Removes gang members** - No random gang spawns
- **Removes scenarios** - Disabled all world scenarios that spawn peds/vehicles
- **Cleans up existing entities** - Aggressively removes already spawned peds and vehicles
- **OneSync support** - Special handling for OneSync servers
- **Model suppression** - Prevents specific ped/vehicle models from spawning
- **Zone control** - Disables population in all game zones
- **Preserves important peds** - Keeps mechanic and army peds for interaction

## Installation

1. Copy the `remove-ambient-peds` folder to your server's `resources` directory
2. Add the following line to your `server.cfg` (preferably near the top):
   ```
   ensure remove-ambient-peds
   ```
3. Restart your server

## How It Works

The enhanced version uses multiple aggressive methods:

### Primary Methods
- **Density Control** - Sets all density multipliers to 0.0 every frame
- **Area Clearing** - Clears 10,000 unit radius around players every 2 seconds
- **Entity Deletion** - Scans and deletes all non-player entities every 250ms
- **Dispatch Disabling** - Disables all 15 dispatch services continuously

### Secondary Methods
- **Model Suppression** - Suppresses specific ped and vehicle models
- **Scenario Disabling** - Disables all world scenarios and scenario groups
- **Zone Population** - Sets population budget to 0 for all zones
- **IPL Removal** - Removes police station and hospital interiors
- **Non-Creation Areas** - Sets the entire map as a ped non-creation area

### Additional Features
- **OneSync Compatibility** - Disables population for all routing buckets
- **Event Handling** - Reapplies settings on player spawn and map start
- **Audio Management** - Disables police scanners and sirens
- **Relationship Control** - Sets all AI relationships to neutral

## Performance

The resource uses optimized update intervals:
- Density control: Every frame (0ms)
- Entity cleanup: Every 250ms (4 times per second)
- Area clearing: Every 2 seconds
- Scenario updates: Every 10 seconds
- Other checks: Various intervals from 100ms to 5 seconds

## Troubleshooting

If you STILL see ambient peds or vehicles after installing v2.0:

### 1. Check Load Order
Ensure `remove-ambient-peds` is one of the first resources loaded:
```cfg
ensure mapmanager
ensure remove-ambient-peds  # Should be here, early in the list
ensure other-resources
```

### 2. Check for Conflicts
Look for other resources that might be:
- Setting density multipliers
- Spawning peds/vehicles
- Using population control
- Common conflicts: vMenu, Lambda Menu, trainer resources

### 3. Server Configuration
For OneSync servers, ensure you have:
```cfg
set onesync_enableLegacy true
# or
set onesync true
```

### 4. Console Commands (for testing)
Try these commands in the F8 console:
```
setpedpopulation 0
setvehiclepopulation 0
```

### 5. Other Resources
Some resources that might interfere:
- AI traffic resources
- Population control scripts
- Custom spawn systems
- Some MLOs with built-in peds

### 6. Verify Installation
Check server console for these messages:
- `[Remove-Ambient-Peds] Enhanced removal system activated.`
- `[Remove-Ambient-Peds] Config loaded with additional removal methods.`
- `[Remove-Ambient-Peds] OneSync detected...` (if using OneSync)

## Preserved Peds

The following ped models are **NOT** removed to allow for interaction:

### Mechanic Models
- `s_m_y_xmech_01` - Mechanic 1
- `s_m_y_xmech_02` - Mechanic 2
- `s_m_y_xmech_02_mp` - Mechanic 2 MP
- `s_m_m_autoshop_01` - Auto Shop Worker 1
- `s_m_m_autoshop_02` - Auto Shop Worker 2

### Army/Military Models
- `s_m_y_marine_01` - Marine 1
- `s_m_y_marine_02` - Marine 2
- `s_m_y_marine_03` - Marine 3
- `s_m_m_marine_01` - Marine 1 (Male)
- `s_m_m_marine_02` - Marine 2 (Male)
- `s_m_y_armymech_01` - Army Mechanic
- `s_m_y_blackops_01` - Black Ops 1
- `s_m_y_blackops_02` - Black Ops 2
- `s_m_y_blackops_03` - Black Ops 3

### Mission Peds
Any ped with relationship groups `MISSION1` through `MISSION8` will also be preserved.

## Advanced Configuration

### Adding More Preserved Models
To keep additional ped models, edit `client/main.lua` and add them to the `keepPedModels` table:
```lua
local keepPedModels = {
    -- Existing models...
    GetHashKey("your_model_name_here"),
}
```

### Excluding Specific Models from Suppression
To keep certain peds/vehicles, edit `client/config.lua` and comment out specific models from the suppression lists.

### Adjusting Cleanup Radius
In `client/main.lua`, find the cleanup threads and adjust the radius (default: 10000.0):
```lua
ClearAreaOfPeds(pos.x, pos.y, pos.z, 10000.0, 1)  -- Change 10000.0 to your desired radius
```

### Performance Tuning
If experiencing performance issues, increase the `Citizen.Wait()` values in the cleanup threads.

## Version History

- **v2.0.0** - Enhanced version with aggressive removal methods
  - Added config.lua with additional removal methods
  - Improved OneSync compatibility
  - Added model suppression
  - Added zone population control
  - More aggressive cleanup cycles

- **v1.0.0** - Initial release
  - Basic density control
  - Standard cleanup methods

## Support

For persistent issues:
1. Enable debug mode by checking console output
2. Verify no errors in server or client console
3. Test with ONLY this resource enabled
4. Check FiveM version compatibility

## Notes

- This resource is very aggressive and may impact performance on lower-end systems
- Some custom MLOs or ymaps might have built-in peds that cannot be removed
- The resource only affects ambient/AI entities, not player-spawned ones
- May conflict with resources that require AI peds/vehicles
