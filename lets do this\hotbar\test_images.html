<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Hotbar Image Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #222;
            color: white;
            padding: 20px;
        }
        .image-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .image-item {
            text-align: center;
            background-color: #333;
            padding: 10px;
            border-radius: 8px;
        }
        .image-item img {
            width: 64px;
            height: 64px;
            object-fit: contain;
            background-color: #444;
            padding: 8px;
            border-radius: 4px;
        }
        .image-item p {
            margin-top: 10px;
            font-size: 12px;
            word-break: break-all;
        }
        .status {
            padding: 10px;
            background-color: #444;
            border-radius: 4px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <h1>Hotbar Image Test</h1>
    <div class="status">
        <p>This page tests if the weapon images are properly accessible from the HTML folder.</p>
        <p>Each image should display below with its filename.</p>
    </div>
    
    <div class="image-grid">
        <div class="image-item">
            <img src="images/weapon_pistol50.png" alt="Pistol 50">
            <p>images/weapon_pistol50.png</p>
        </div>
        <div class="image-item">
            <img src="images/weapon_assaultrifle.png" alt="Assault Rifle">
            <p>images/weapon_assaultrifle.png</p>
        </div>
        <div class="image-item">
            <img src="images/weapon_smg.png" alt="SMG">
            <p>images/weapon_smg.png</p>
        </div>
        <div class="image-item">
            <img src="images/weapon_combatmg.png" alt="Combat MG">
            <p>images/weapon_combatmg.png</p>
        </div>
        <div class="image-item">
            <img src="images/weapon_microsmg.png" alt="Micro SMG">
            <p>images/weapon_microsmg.png</p>
        </div>
        <div class="image-item">
            <img src="images/weapon_appistol.png" alt="AP Pistol">
            <p>images/weapon_appistol.png</p>
        </div>
        <div class="image-item">
            <img src="images/weapon_assaultrifle_mk2.png" alt="Assault Rifle Mk2">
            <p>images/weapon_assaultrifle_mk2.png</p>
        </div>
        <div class="image-item">
            <img src="images/weapon_bullpuprifle.png" alt="Bullpup Rifle">
            <p>images/weapon_bullpuprifle.png</p>
        </div>
        <div class="image-item">
            <img src="images/weapon_combatpdw.png" alt="Combat PDW">
            <p>images/weapon_combatpdw.png</p>
        </div>
        <div class="image-item">
            <img src="images/weapon_specialcarbine.png" alt="Special Carbine">
            <p>images/weapon_specialcarbine.png</p>
        </div>
    </div>

    <script>
        // Check if images load successfully
        document.querySelectorAll('img').forEach(img => {
            img.onerror = function() {
                this.style.backgroundColor = '#800';
                this.alt = 'Failed to load';
                console.error('Failed to load:', this.src);
            };
            img.onload = function() {
                console.log('Successfully loaded:', this.src);
            };
        });
    </script>
</body>
</html>
