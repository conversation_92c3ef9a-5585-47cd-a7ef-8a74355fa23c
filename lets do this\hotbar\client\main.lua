local hotbarVisible = true
local hotbarItems = {
    [1] = { name = "", icon = "", count = 0, weaponHash = nil },
    [2] = { name = "", icon = "", count = 0, weaponHash = nil },
    [3] = { name = "", icon = "", count = 0, weaponHash = nil },
    [4] = { name = "", icon = "", count = 0, weaponHash = nil },
    [5] = { name = "", icon = "", count = 0, weaponHash = nil }
}

-- Initialize hotbar on resource start
CreateThread(function()
    Wait(1000) -- Wait for game to load
    SendNUIMessage({
        type = 'show',
        visible = hotbarVisible
    })
    
    -- Update hotbar with initial data
    SendNUIMessage({
        type = 'updateHotbar',
        items = hotbarItems
    })
end)

-- Key mappings for hotbar slots (with higher priority)
for i = 1, 5 do
    RegisterKeyMapping('hotbar_slot_' .. i, 'Use Hotbar Slot ' .. i, 'keyboard', tostring(i))
    RegisterCommand('hotbar_slot_' .. i, function()
        print("Hotbar slot " .. i .. " pressed")
        UseHotbarSlot(i)
    end, false)
end

-- Additional thread to ensure our number keys work
CreateThread(function()
    while true do
        Wait(0)

        -- Use raw key detection for number keys
        if IsControlJustPressed(1, 157) then -- 1 key (INPUT_SELECT_WEAPON_UNARMED)
            print("Raw key detection: 1")
            UseHotbarSlot(1)
        end
        if IsControlJustPressed(1, 158) then -- 2 key (INPUT_SELECT_WEAPON_MELEE)
            print("Raw key detection: 2")
            UseHotbarSlot(2)
        end
        if IsControlJustPressed(1, 159) then -- 3 key (INPUT_SELECT_WEAPON_HANDGUN)
            print("Raw key detection: 3")
            UseHotbarSlot(3)
        end
        if IsControlJustPressed(1, 160) then -- 4 key (INPUT_SELECT_WEAPON_SHOTGUN)
            print("Raw key detection: 4")
            UseHotbarSlot(4)
        end
        if IsControlJustPressed(1, 161) then -- 5 key (INPUT_SELECT_WEAPON_SMG)
            print("Raw key detection: 5")
            UseHotbarSlot(5)
        end
    end
end)

-- Toggle hotbar visibility
RegisterKeyMapping('toggle_hotbar', 'Toggle Hotbar', 'keyboard', 'H')
RegisterCommand('toggle_hotbar', function()
    ToggleHotbar()
end, false)

-- Function to use hotbar slot
function UseHotbarSlot(slot)
    local item = hotbarItems[slot]
    if item and item.name ~= "" then
        print("Using item: " .. item.name .. " from slot " .. slot)

        -- Check if it's a weapon
        if item.weaponHash then
            local ped = PlayerPedId()
            if HasPedGotWeapon(ped, item.weaponHash, false) then
                local currentWeapon = GetSelectedPedWeapon(ped)

                -- If already using this weapon, go unarmed
                if currentWeapon == item.weaponHash then
                    SetCurrentPedWeapon(ped, `WEAPON_UNARMED`, true)
                    print("Unequipped " .. item.name .. " - now unarmed")
                else
                    -- Switch to this weapon
                    SetCurrentPedWeapon(ped, item.weaponHash, true)
                    print("Switched to " .. item.name)
                end
            else
                print("You no longer have " .. item.name)
                -- Remove from hotbar if weapon is no longer available
                ClearHotbarSlot(slot)
            end
        else
            -- Handle non-weapon items
            TriggerServerEvent('hotbar:useItem', slot, item.name)
        end

        -- Visual feedback
        SendNUIMessage({
            type = 'useSlot',
            slot = slot
        })
    else
        print("Slot " .. slot .. " is empty")
    end
end

-- Function to toggle hotbar visibility
function ToggleHotbar()
    hotbarVisible = not hotbarVisible
    SendNUIMessage({
        type = 'show',
        visible = hotbarVisible
    })
end

-- Function to set item in hotbar slot
function SetHotbarItem(slot, itemName, itemIcon, itemCount, weaponHash)
    if slot >= 1 and slot <= 5 then
        hotbarItems[slot] = {
            name = itemName or "",
            icon = itemIcon or "",
            count = itemCount or 0,
            weaponHash = weaponHash or nil
        }

        SendNUIMessage({
            type = 'updateSlot',
            slot = slot,
            item = hotbarItems[slot]
        })
    end
end

-- Function to clear hotbar slot
function ClearHotbarSlot(slot)
    if slot >= 1 and slot <= 5 then
        hotbarItems[slot] = { name = "", icon = "", count = 0, weaponHash = nil }

        SendNUIMessage({
            type = 'updateSlot',
            slot = slot,
            item = hotbarItems[slot]
        })
    end
end

-- Weapon monitoring for vMenu integration
local lastWeapons = {}
local weaponData = {
    -- Pistols
    [`WEAPON_PISTOL`] = {name = "Pistol", icon = "🔫"},
    [`WEAPON_COMBATPISTOL`] = {name = "Combat Pistol", icon = "🔫"},
    [`WEAPON_APPISTOL`] = {name = "AP Pistol", icon = "🔫"},
    [`WEAPON_PISTOL50`] = {name = "Pistol .50", icon = "🔫"},

    -- SMGs
    [`WEAPON_MICROSMG`] = {name = "Micro SMG", icon = "🔫"},
    [`WEAPON_SMG`] = {name = "SMG", icon = "🔫"},
    [`WEAPON_ASSAULTSMG`] = {name = "Assault SMG", icon = "🔫"},
    [`WEAPON_COMBATPDW`] = {name = "Combat PDW", icon = "🔫"},
    [`WEAPON_MACHINEPISTOL`] = {name = "Machine Pistol", icon = "🔫"},
    [`WEAPON_MINISMG`] = {name = "Mini SMG", icon = "🔫"},

    -- Assault Rifles
    [`WEAPON_ASSAULTRIFLE`] = {name = "Assault Rifle", icon = "🔫"},
    [`WEAPON_CARBINERIFLE`] = {name = "Carbine Rifle", icon = "🔫"},
    [`WEAPON_ADVANCEDRIFLE`] = {name = "Advanced Rifle", icon = "🔫"},
    [`WEAPON_ASSAULTRIFLE_MK2`] = {name = "Assault Rifle Mk2", icon = "🔫"},
    [`WEAPON_BULLPUPRIFLE`] = {name = "Bullpup Rifle", icon = "🔫"},
    [`WEAPON_BULLPUPRIFLE_MK2`] = {name = "Bullpup Rifle Mk2", icon = "🔫"},
    [`WEAPON_SPECIALCARBINE`] = {name = "Special Carbine", icon = "🔫"},
    [`WEAPON_SPECIALCARBINE_MK2`] = {name = "Special Carbine Mk2", icon = "🔫"},

    -- Machine Guns
    [`WEAPON_MG`] = {name = "MG", icon = "🔫"},
    [`WEAPON_COMBATMG`] = {name = "Combat MG", icon = "🔫"},

    -- Shotguns
    [`WEAPON_PUMPSHOTGUN`] = {name = "Pump Shotgun", icon = "🔫"},
    [`WEAPON_SAWNOFFSHOTGUN`] = {name = "Sawed-Off Shotgun", icon = "🔫"},
    [`WEAPON_ASSAULTSHOTGUN`] = {name = "Assault Shotgun", icon = "🔫"},
    [`WEAPON_BULLPUPSHOTGUN`] = {name = "Bullpup Shotgun", icon = "🔫"},

    -- Sniper Rifles
    [`WEAPON_SNIPERRIFLE`] = {name = "Sniper Rifle", icon = "🎯"},
    [`WEAPON_HEAVYSNIPER`] = {name = "Heavy Sniper", icon = "🎯"},
    [`WEAPON_MARKSMANRIFLE`] = {name = "Marksman Rifle", icon = "🎯"},

    -- Heavy Weapons
    [`WEAPON_GRENADELAUNCHER`] = {name = "Grenade Launcher", icon = "💥"},
    [`WEAPON_RPG`] = {name = "RPG", icon = "🚀"},
    [`WEAPON_MINIGUN`] = {name = "Minigun", icon = "🔫"},

    -- Throwables
    [`WEAPON_GRENADE`] = {name = "Grenade", icon = "💣"},
    [`WEAPON_STICKYBOMB`] = {name = "Sticky Bomb", icon = "💣"},
    [`WEAPON_SMOKEGRENADE`] = {name = "Smoke Grenade", icon = "💨"},
    [`WEAPON_BZGAS`] = {name = "BZ Gas", icon = "💨"},
    [`WEAPON_MOLOTOV`] = {name = "Molotov", icon = "🔥"},

    -- Special
    [`WEAPON_STUNGUN`] = {name = "Stun Gun", icon = "⚡"},
    [`WEAPON_FIREEXTINGUISHER`] = {name = "Fire Extinguisher", icon = "🧯"},
    [`WEAPON_PETROLCAN`] = {name = "Jerry Can", icon = "⛽"},

    -- Melee
    [`WEAPON_KNIFE`] = {name = "Knife", icon = "🔪"},
    [`WEAPON_NIGHTSTICK`] = {name = "Nightstick", icon = "🏏"},
    [`WEAPON_HAMMER`] = {name = "Hammer", icon = "🔨"},
    [`WEAPON_BAT`] = {name = "Baseball Bat", icon = "🏏"},
    [`WEAPON_GOLFCLUB`] = {name = "Golf Club", icon = "🏌️"},
    [`WEAPON_CROWBAR`] = {name = "Crowbar", icon = "🔧"},
    [`WEAPON_BOTTLE`] = {name = "Bottle", icon = "🍾"},
    [`WEAPON_DAGGER`] = {name = "Antique Cavalry Dagger", icon = "🗡️"},
    [`WEAPON_HATCHET`] = {name = "Hatchet", icon = "🪓"},
    [`WEAPON_KNUCKLE`] = {name = "Knuckle Duster", icon = "👊"}
}

-- Weapon image mapping
local weaponImages = {
    [`WEAPON_PISTOL`] = "weapon_pistol50.png", -- Using pistol50 as default pistol image
    [`WEAPON_COMBATPISTOL`] = "weapon_pistol50.png",
    [`WEAPON_APPISTOL`] = "weapon_appistol.png",
    [`WEAPON_PISTOL50`] = "weapon_pistol50.png",
    [`WEAPON_MICROSMG`] = "weapon_microsmg.png",
    [`WEAPON_SMG`] = "weapon_smg.png",
    [`WEAPON_ASSAULTSMG`] = "weapon_assaultsmg.png",
    [`WEAPON_ASSAULTRIFLE`] = "weapon_assaultrifle.png",
    [`WEAPON_ASSAULTRIFLE_MK2`] = "weapon_assaultrifle_mk2.png",
    [`WEAPON_CARBINERIFLE`] = "weapon_m4a1.png", -- Using M4A1 for carbine rifle
    [`WEAPON_ADVANCEDRIFLE`] = "weapon_advancedrifle.png",
    [`WEAPON_COMBATMG`] = "weapon_combatmg.png",
    [`WEAPON_BULLPUPRIFLE`] = "weapon_bullpuprifle.png",
    [`WEAPON_BULLPUPRIFLE_MK2`] = "weapon_bullpuprifle_mk2.png",
    [`WEAPON_COMBATPDW`] = "weapon_combatpdw.png",
    [`WEAPON_MACHINEPISTOL`] = "weapon_machinepistol.png",
    [`WEAPON_MARKSMANRIFLE`] = "weapon_marksmanrifle.png",
    [`WEAPON_MINISMG`] = "weapon_minismg.png",
    [`WEAPON_SPECIALCARBINE`] = "weapon_specialcarbine.png",
    [`WEAPON_SPECIALCARBINE_MK2`] = "weapon_specialcarbine_mk2.png",
    -- Additional weapons from available images
    [`WEAPON_FAMAS`] = "weapon_famas.png",
    [`WEAPON_GROZA`] = "weapon_groza.png",
    [`WEAPON_L85A2`] = "weapon_l85a2.png",
    [`WEAPON_M4A1`] = "weapon_m4a1.png",
    [`WEAPON_MM4`] = "weapon_mm4.png",
    [`WEAPON_MP7`] = "weapon_mp7.png",
    [`WEAPON_MP9A`] = "weapon_mp9a.png",
    [`WEAPON_P90`] = "weapon_p90.png",
    [`WEAPON_SCAR`] = "weapon_scar.png",
    [`WEAPON_SCAR_MK2`] = "weapon_scar_mk2.png",
    [`WEAPON_SIG516`] = "weapon_sig516.png",
    [`WEAPON_UMBRELLAPISTOL`] = "weapon_umbrellapistol.png"
}

-- Function to get weapon image path
function GetWeaponImage(weaponHash)
    local imageName = weaponImages[weaponHash]
    if imageName then
        return "images/" .. imageName
    end
    return "images/weapon_pistol50.png" -- Default fallback image
end

-- Override SetHotbarItem to add debug print for icon path
local originalSetHotbarItem = SetHotbarItem
function SetHotbarItem(slot, itemName, itemIcon, itemCount, weaponHash)
    print(string.format("SetHotbarItem called: slot=%d, itemName=%s, itemIcon=%s, itemCount=%d, weaponHash=%s", slot, itemName, itemIcon, itemCount, tostring(weaponHash)))
    originalSetHotbarItem(slot, itemName, itemIcon, itemCount, weaponHash)
end

-- Disable weapon wheel and default weapon keys thread
CreateThread(function()
    while true do
        Wait(0)

        -- Disable weapon wheel controls
        DisableControlAction(0, 37, true)  -- INPUT_SELECT_WEAPON (TAB key)
        DisableControlAction(0, 157, true) -- INPUT_SELECT_WEAPON_UNARMED
        DisableControlAction(0, 158, true) -- INPUT_SELECT_WEAPON_MELEE
        DisableControlAction(0, 159, true) -- INPUT_SELECT_WEAPON_HANDGUN
        DisableControlAction(0, 160, true) -- INPUT_SELECT_WEAPON_SHOTGUN
        DisableControlAction(0, 161, true) -- INPUT_SELECT_WEAPON_SMG
        DisableControlAction(0, 162, true) -- INPUT_SELECT_WEAPON_AUTO_RIFLE
        DisableControlAction(0, 163, true) -- INPUT_SELECT_WEAPON_SNIPER
        DisableControlAction(0, 164, true) -- INPUT_SELECT_WEAPON_HEAVY
        DisableControlAction(0, 165, true) -- INPUT_SELECT_WEAPON_SPECIAL

        -- Disable default number key weapon selection
        DisableControlAction(0, 200, true) -- INPUT_FRONTEND_PAUSE_ALTERNATE (1 key)
        DisableControlAction(0, 201, true) -- INPUT_FRONTEND_PAUSE (2 key)
        DisableControlAction(0, 202, true) -- INPUT_FRONTEND_CANCEL_MULTIPLAYER (3 key)
        DisableControlAction(0, 203, true) -- INPUT_FRONTEND_ENDSCREEN_ACCEPT (4 key)
        DisableControlAction(0, 204, true) -- INPUT_FRONTEND_ENDSCREEN_EXPAND (5 key)

        -- Additional weapon selection controls to disable
        DisableControlAction(0, 12, true)  -- INPUT_WEAPON_WHEEL_UP_DOWN
        DisableControlAction(0, 13, true)  -- INPUT_WEAPON_WHEEL_LEFT_RIGHT
        DisableControlAction(0, 16, true)  -- INPUT_SELECT_NEXT_WEAPON
        DisableControlAction(0, 17, true)  -- INPUT_SELECT_PREV_WEAPON

        -- Hide weapon wheel HUD components
        HideHudComponentThisFrame(19) -- Weapon wheel
        HideHudComponentThisFrame(20) -- Weapon wheel stats

        -- Also disable mouse wheel weapon switching
        DisableControlAction(0, 14, true) -- INPUT_WEAPON_WHEEL_NEXT
        DisableControlAction(0, 15, true) -- INPUT_WEAPON_WHEEL_PREV
    end
end)

-- Function to scan and update all weapons
function ScanAndUpdateWeapons()
    local ped = PlayerPedId()
    local currentWeapons = {}
    local weaponCount = 0

    print("=== SCANNING WEAPONS ===")

    -- Get all weapons the player currently has
    for weaponHash, weaponInfo in pairs(weaponData) do
        if HasPedGotWeapon(ped, weaponHash, false) then
            local ammo = GetAmmoInPedWeapon(ped, weaponHash)
            currentWeapons[weaponHash] = {
                name = weaponInfo.name,
                icon = weaponInfo.icon,
                ammo = ammo
            }
            weaponCount = weaponCount + 1
            print("Found weapon: " .. weaponInfo.name .. " (Ammo: " .. ammo .. ")")
        end
    end

    print("Total weapons found: " .. weaponCount)

    -- Process new weapons (sort by hash for consistent ordering)
    local newWeapons = {}
    for weaponHash, weaponData in pairs(currentWeapons) do
        if not lastWeapons[weaponHash] then
            table.insert(newWeapons, {hash = weaponHash, data = weaponData})
            print("New weapon detected: " .. weaponData.name .. " (Hash: " .. weaponHash .. ")")
        end
    end

    -- Sort new weapons by hash to ensure consistent slot assignment
    table.sort(newWeapons, function(a, b) return a.hash < b.hash end)

    -- Add new weapons to hotbar
    for _, weapon in ipairs(newWeapons) do
        print("Adding weapon to hotbar: " .. weapon.data.name)
        local weaponImagePath = GetWeaponImage(weapon.hash)
        print("Using image path: " .. weaponImagePath)
        AddWeaponToHotbar(weapon.data.name, weapon.hash, weapon.data.ammo, weaponImagePath)
    end

    -- Update ammo counts for existing weapons
    for weaponHash, weaponData in pairs(currentWeapons) do
        if lastWeapons[weaponHash] then
            UpdateWeaponAmmo(weaponHash, weaponData.ammo)
        end
    end

    -- Check for removed weapons
    for weaponHash, weaponData in pairs(lastWeapons) do
        if not currentWeapons[weaponHash] then
            -- Weapon removed, remove from hotbar
            print("Weapon removed: " .. weaponData.name)
            RemoveWeaponFromHotbar(weaponHash)
        end
    end

    lastWeapons = currentWeapons
    print("=== SCAN COMPLETE ===")
end

-- Monitor weapons thread
CreateThread(function()
    while true do
        Wait(500) -- Check every 500ms
        ScanAndUpdateWeapons()
    end
end)

-- Also scan when player spawns
AddEventHandler('playerSpawned', function()
    Wait(2000) -- Wait 2 seconds after spawn
    print("Player spawned, scanning weapons...")
    ScanAndUpdateWeapons()
end)

-- Scan when resource starts
CreateThread(function()
    Wait(3000) -- Wait 3 seconds after resource start
    print("Resource started, initial weapon scan...")
    ScanAndUpdateWeapons()
end)

-- Function to add weapon to hotbar
function AddWeaponToHotbar(weaponName, weaponHash, ammo, weaponIcon)
    print("AddWeaponToHotbar called: " .. weaponName .. " (Hash: " .. weaponHash .. ")")
    weaponIcon = weaponIcon or "🔫" -- Default icon if none provided

    -- Check if weapon is already in hotbar
    for i = 1, 5 do
        if hotbarItems[i].weaponHash == weaponHash then
            -- Weapon already exists, just update ammo
            print("Weapon " .. weaponName .. " already in slot " .. i .. ", updating ammo")
            hotbarItems[i].count = ammo > 0 and ammo or 1
            SendNUIMessage({
                type = 'updateSlot',
                slot = i,
                item = hotbarItems[i]
            })
            return
        end
    end

    -- Find first empty slot
    for i = 1, 5 do
        if hotbarItems[i].name == "" then
            print("Adding " .. weaponName .. " to empty slot " .. i)
            local weaponIcon = GetWeaponImage(weaponHash)
            SetHotbarItem(i, weaponName, weaponIcon, ammo > 0 and ammo or 1, weaponHash)
            print("Successfully added " .. weaponName .. " to hotbar slot " .. i)
            return
        end
    end

    -- If no empty slots, replace the last slot
    print("Hotbar full, replacing slot 5 with " .. weaponName)
    local weaponIcon = GetWeaponImage(weaponHash)
    SetHotbarItem(5, weaponName, weaponIcon, ammo > 0 and ammo or 1, weaponHash)
end

-- Function to update weapon ammo
function UpdateWeaponAmmo(weaponHash, ammo)
    for i = 1, 5 do
        if hotbarItems[i].weaponHash == weaponHash then
            if hotbarItems[i].count ~= ammo then
                hotbarItems[i].count = ammo > 0 and ammo or 1
                SendNUIMessage({
                    type = 'updateSlot',
                    slot = i,
                    item = hotbarItems[i]
                })
            end
            break
        end
    end
end

-- Function to remove weapon from hotbar
function RemoveWeaponFromHotbar(weaponHash)
    for i = 1, 5 do
        if hotbarItems[i].weaponHash == weaponHash then
            ClearHotbarSlot(i)
            print("Removed weapon from hotbar slot " .. i)
            break
        end
    end
end

-- Debug command to refresh hotbar
RegisterCommand('refreshhotbar', function()
    print("=== REFRESHING HOTBAR ===")
    local ped = PlayerPedId()
    local weaponCount = 0

    print("Current hotbar state:")
    for i = 1, 5 do
        local item = hotbarItems[i]
        if item.name ~= "" then
            print("Slot " .. i .. ": " .. item.name .. " (Hash: " .. (item.weaponHash or "none") .. ")")
        else
            print("Slot " .. i .. ": Empty")
        end
    end

    print("Weapons in inventory:")
    for weaponHash, weaponInfo in pairs(weaponData) do
        if HasPedGotWeapon(ped, weaponHash, false) then
            local ammo = GetAmmoInPedWeapon(ped, weaponHash)
            print("- " .. weaponInfo.name .. " (Hash: " .. weaponHash .. ") - Ammo: " .. ammo)
            weaponCount = weaponCount + 1
        end
    end

    print("Total weapons found: " .. weaponCount)
    print("=== END REFRESH ===")
end, false)

-- Manual scan command
RegisterCommand('scanweapons', function()
    print("Manual weapon scan triggered...")
    ScanAndUpdateWeapons()
end, false)

-- Debug command to force add all weapons to hotbar
RegisterCommand('forceaddweapons', function()
    print("=== FORCE ADDING ALL WEAPONS ===")
    local ped = PlayerPedId()

    -- Clear hotbar first
    for i = 1, 5 do
        ClearHotbarSlot(i)
    end

    -- Add all weapons using direct method
    local weaponList = {}
    for weaponHash, weaponInfo in pairs(weaponData) do
        if HasPedGotWeapon(ped, weaponHash, false) then
            local ammo = GetAmmoInPedWeapon(ped, weaponHash)
            table.insert(weaponList, {
                hash = weaponHash,
                name = weaponInfo.name,
                icon = weaponInfo.icon,
                ammo = ammo
            })
            print("Found weapon for force add: " .. weaponInfo.name .. " (Hash: " .. weaponHash .. ", Ammo: " .. ammo .. ")")
        end
    end

    print("Total weapons to add: " .. #weaponList)

    -- Sort by hash for consistent ordering
    table.sort(weaponList, function(a, b) return a.hash < b.hash end)

    -- Add to hotbar directly
    for i, weapon in ipairs(weaponList) do
        if i <= 5 then
            print("Force adding: " .. weapon.name .. " to slot " .. i)
            local weaponIcon = GetWeaponImage(weapon.hash)
            print("Image path for " .. weapon.name .. ": " .. weaponIcon)

            -- Set item directly
            hotbarItems[i] = {
                name = weapon.name,
                icon = weaponIcon,
                count = weapon.ammo > 0 and weapon.ammo or 1,
                weaponHash = weapon.hash
            }

            -- Update UI
            SendNUIMessage({
                type = 'updateSlot',
                slot = i,
                item = hotbarItems[i]
            })

            print("Successfully force added " .. weapon.name .. " to slot " .. i .. " with icon: " .. weaponIcon)
        end
    end

    print("=== FORCE ADD COMPLETE ===")
end, false)

-- Debug command to test image paths
RegisterCommand('testimages', function()
    print("=== TESTING IMAGE PATHS ===")
    
    -- Test a few common weapons
    local testWeapons = {
        [`WEAPON_PISTOL50`] = "Pistol .50",
        [`WEAPON_ASSAULTRIFLE`] = "Assault Rifle",
        [`WEAPON_SMG`] = "SMG",
        [`WEAPON_COMBATMG`] = "Combat MG"
    }
    
    for weaponHash, weaponName in pairs(testWeapons) do
        local imagePath = GetWeaponImage(weaponHash)
        print("Weapon: " .. weaponName .. " -> Image: " .. imagePath)
        
        -- Test if we can set it in hotbar
        SetHotbarItem(1, weaponName, imagePath, 1, weaponHash)
        Wait(1000) -- Wait 1 second between tests
    end
    
    print("=== IMAGE TEST COMPLETE ===")
end, false)

-- Simple test command to verify hotbar is working
RegisterCommand('testhotbar', function()
    print("=== HOTBAR TEST ===")
    print("Hotbar resource is loaded and working!")
    print("Current hotbar items:")
    for i = 1, 5 do
        local item = hotbarItems[i]
        if item.name ~= "" then
            print("Slot " .. i .. ": " .. item.name .. " (Icon: " .. item.icon .. ")")
        else
            print("Slot " .. i .. ": Empty")
        end
    end
    
    -- Test adding a sample weapon image
    print("Testing sample weapon image...")
    SetHotbarItem(1, "Test Pistol", "images/weapon_pistol50.png", 1, `WEAPON_PISTOL50`)
    print("Added test pistol to slot 1 - check if image appears!")
    
    print("=== TEST COMPLETE ===")
end, false)

-- Command to test all weapon images
RegisterCommand('testallimages', function()
    print("=== TESTING ALL WEAPON IMAGES ===")
    
    -- Clear all slots first
    for i = 1, 5 do
        ClearHotbarSlot(i)
    end
    
    -- Test weapons that we have images for
    local testWeapons = {
        {slot = 1, name = "Pistol .50", icon = "images/weapon_pistol50.png", hash = `WEAPON_PISTOL50`},
        {slot = 2, name = "Assault Rifle", icon = "images/weapon_assaultrifle.png", hash = `WEAPON_ASSAULTRIFLE`},
        {slot = 3, name = "SMG", icon = "images/weapon_smg.png", hash = `WEAPON_SMG`},
        {slot = 4, name = "Combat MG", icon = "images/weapon_combatmg.png", hash = `WEAPON_COMBATMG`},
        {slot = 5, name = "Micro SMG", icon = "images/weapon_microsmg.png", hash = `WEAPON_MICROSMG`}
    }
    
    for _, weapon in ipairs(testWeapons) do
        SetHotbarItem(weapon.slot, weapon.name, weapon.icon, 100, weapon.hash)
        print("Added " .. weapon.name .. " to slot " .. weapon.slot .. " with icon: " .. weapon.icon)
    end
    
    print("=== ALL IMAGES TEST COMPLETE ===")
    print("Check your hotbar - all 5 slots should now show weapon images!")
end, false)

-- Command to open debug page
RegisterCommand('hotbardebug', function()
    print("Opening hotbar debug page...")
    SendNUIMessage({
        type = 'openDebug'
    })
end, false)

-- Direct NUI test command
RegisterCommand('testdirectimages', function()
    print("=== DIRECT IMAGE TEST ===")
    
    -- Make sure hotbar is visible
    if not hotbarVisible then
        ToggleHotbar()
    end
    
    -- Wait a moment
    Wait(100)
    
    -- Send direct NUI messages with different path formats
    print("Testing direct NUI messages with images...")
    
    -- Test different path formats
    local pathTests = {
        {slot = 1, path = "images/weapon_pistol50.png", name = "Path: images/"},
        {slot = 2, path = "./images/weapon_assaultrifle.png", name = "Path: ./images/"},
        {slot = 3, path = "weapon_smg.png", name = "Path: filename only"},
        {slot = 4, path = "html/images/weapon_combatmg.png", name = "Path: html/images/"},
        {slot = 5, path = "../html/images/weapon_microsmg.png", name = "Path: ../html/images/"}
    }
    
    for _, test in ipairs(pathTests) do
        SendNUIMessage({
            type = 'updateSlot',
            slot = test.slot,
            item = {
                name = test.name,
                icon = test.path,
                count = 1,
                weaponHash = nil
            }
        })
        print("Sent slot " .. test.slot .. ": " .. test.name .. " -> " .. test.path)
    end
    
    print("=== DIRECT TEST COMPLETE ===")
    print("Check console (F8) for image loading messages")
end, false)

-- Alternative weapon detection method
RegisterCommand('altweaponscan', function()
    print("=== ALTERNATIVE WEAPON SCAN ===")
    local ped = PlayerPedId()

    -- Method 1: Check all possible weapon hashes
    local foundWeapons = {}
    for weaponHash, weaponInfo in pairs(weaponData) do
        if HasPedGotWeapon(ped, weaponHash, false) then
            local ammo = GetAmmoInPedWeapon(ped, weaponHash)
            foundWeapons[weaponHash] = {name = weaponInfo.name, ammo = ammo}
            print("Method 1 found: " .. weaponInfo.name)
        end
    end

    -- Method 2: Get current weapon
    local currentWeapon = GetSelectedPedWeapon(ped)
    if currentWeapon and currentWeapon ~= `WEAPON_UNARMED` then
        local weaponInfo = weaponData[currentWeapon]
        local weaponName = weaponInfo and weaponInfo.name or "Unknown Weapon"
        print("Currently equipped: " .. weaponName .. " (Hash: " .. currentWeapon .. ")")
    end

    print("=== ALT SCAN COMPLETE ===")
end, false)

-- Test image loading command
RegisterCommand('testimages', function()
    print("=== TESTING IMAGE PATHS ===")

    -- Test a few weapons with known images
    local testWeapons = {
        [`WEAPON_PISTOL50`] = "Pistol .50",
        [`WEAPON_ASSAULTRIFLE`] = "Assault Rifle",
        [`WEAPON_SMG`] = "SMG",
        [`WEAPON_MICROSMG`] = "Micro SMG"
    }

    for weaponHash, weaponName in pairs(testWeapons) do
        local imagePath = GetWeaponImage(weaponHash)
        print("Weapon: " .. weaponName .. " -> Image: " .. imagePath)

        -- Test setting it in slot 1
        SetHotbarItem(1, weaponName, imagePath, 100, weaponHash)
        break -- Only test one for now
    end

    print("=== IMAGE TEST COMPLETE ===")
end, false)

-- Test NUI image loading with multiple weapons
RegisterCommand('testnui', function()
    print("=== TESTING NUI IMAGE LOADING ===")

    -- Test multiple weapon images in different slots
    local testWeapons = {
        {slot = 1, name = "Pistol .50", icon = "images/weapon_pistol50.png", hash = `WEAPON_PISTOL50`},
        {slot = 2, name = "Assault Rifle", icon = "images/weapon_assaultrifle.png", hash = `WEAPON_ASSAULTRIFLE`},
        {slot = 3, name = "SMG", icon = "images/weapon_smg.png", hash = `WEAPON_SMG`},
        {slot = 4, name = "Combat MG", icon = "images/weapon_combatmg.png", hash = `WEAPON_COMBATMG`},
        {slot = 5, name = "AP Pistol", icon = "images/weapon_appistol.png", hash = `WEAPON_APPISTOL`}
    }

    for _, weapon in ipairs(testWeapons) do
        print("Testing slot " .. weapon.slot .. ": " .. weapon.name .. " -> " .. weapon.icon)

        SendNUIMessage({
            type = 'updateSlot',
            slot = weapon.slot,
            item = {
                name = weapon.name,
                icon = weapon.icon,
                count = 100,
                weaponHash = weapon.hash
            }
        })

        Wait(100) -- Small delay between updates
    end

    print("=== NUI TEST COMPLETE - Check all 5 slots for weapon images ===")
end, false)

-- Command to clear all hotbar slots for testing
RegisterCommand('clearhotbar', function()
    print("=== CLEARING HOTBAR ===")
    for i = 1, 5 do
        ClearHotbarSlot(i)
    end
    print("=== HOTBAR CLEARED ===")
end, false)

-- Export functions for other resources
RegisterNetEvent('vmenu:weaponSpawned')
AddEventHandler('vmenu:weaponSpawned', function(weaponHash, ammo)
    print("Received vMenu weapon spawn event for: " .. weaponHash)
    local weaponInfo = weaponData[weaponHash]
    if weaponInfo then
        AddWeaponToHotbar(weaponInfo.name, weaponHash, ammo, weaponInfo.icon)
    else
        AddWeaponToHotbar("Unknown Weapon", weaponHash, ammo, "🔫")
    end
    ScanAndUpdateWeapons() -- Force full hotbar refresh
end)

exports('SetHotbarItem', SetHotbarItem)
exports('ClearHotbarSlot', ClearHotbarSlot)
exports('GetHotbarItems', function() return hotbarItems end)

-- Server events
RegisterNetEvent('hotbar:setItem')
AddEventHandler('hotbar:setItem', function(slot, itemName, itemIcon, itemCount, weaponHash)
    SetHotbarItem(slot, itemName, itemIcon, itemCount, weaponHash)
end)

RegisterNetEvent('hotbar:clearSlot')
AddEventHandler('hotbar:clearSlot', function(slot)
    ClearHotbarSlot(slot)
end)

RegisterNetEvent('hotbar:updateItems')
AddEventHandler('hotbar:updateItems', function(items)
    hotbarItems = items
    SendNUIMessage({
        type = 'updateHotbar',
        items = hotbarItems
    })
end)

-- NUI Callbacks
RegisterNUICallback('useSlot', function(data, cb)
    UseHotbarSlot(data.slot)
    cb('ok')
end)

RegisterNUICallback('removeSlot', function(data, cb)
    local slot = data.slot
    if slot >= 1 and slot <= 5 then
        local item = hotbarItems[slot]
        if item and item.name ~= "" then
            print("Removing " .. item.name .. " from slot " .. slot)
            ClearHotbarSlot(slot)

            -- If it was a weapon, go unarmed
            if item.weaponHash then
                local ped = PlayerPedId()
                local currentWeapon = GetSelectedPedWeapon(ped)
                if currentWeapon == item.weaponHash then
                    SetCurrentPedWeapon(ped, `WEAPON_UNARMED`, true)
                    print("Unequipped weapon - now unarmed")
                end
            end
        end
    end
    cb('ok')
end)
