-- Additional configuration for aggressive ped/vehicle removal
-- This file contains extra measures to ensure complete removal

-- Extra thread for OneSync servers
Citizen.CreateThread(function()
    -- Wait for game to load
    while not NetworkIsPlayerActive(PlayerId()) do
        Citizen.Wait(100)
    end
    
    -- Additional OneSync specific settings
    if GetConvar("onesync", "off") ~= "off" then
        print("^2[Remove-Ambient-Peds] ^7OneSync detected, applying additional removal methods...")
        
        -- OneSync population settings
        SetRoutingBucketPopulationEnabled(0, false)
        
        -- Disable population for all routing buckets (0-63)
        for i = 0, 63 do
            SetRoutingBucketPopulationEnabled(i, false)
        end
    end
end)

-- IPL and interior cleanup
Citizen.CreateThread(function()
    -- Remove hospital interiors that spawn medical staff
    RemoveIpl("rc12b_hospitalinterior")
    RemoveIpl("rc12b_hospitalinterior_lod")
    
    -- Remove police station interiors
    RemoveIpl("rc12b_default")
    RemoveIpl("rc12b_destroyed")
    RemoveIpl("rc12b_fixed")
    
    -- Disable interior ambient peds
    local interiors = {
        GetInteriorAtCoords(440.84, -983.14, 30.69), -- Mission Row Police Station
        GetInteriorAtCoords(360.0, -1584.0, 29.0), -- Davis Police Station
        GetInteriorAtCoords(-1096.0, -850.0, 4.0), -- Vespucci Police Station
        GetInteriorAtCoords(1851.0, 3683.0, 34.0), -- Sandy Shores Police Station
        GetInteriorAtCoords(-448.0, 6012.0, 31.0), -- Paleto Bay Police Station
        GetInteriorAtCoords(307.0, -1433.0, 29.0), -- Central LS Medical Center
        GetInteriorAtCoords(1151.0, -1529.0, 35.0), -- St Fiacre Hospital
        GetInteriorAtCoords(-874.0, -307.0, 39.0), -- Portola Trinity Medical Center
        GetInteriorAtCoords(1839.0, 3672.0, 34.0), -- Sandy Shores Medical Center
        GetInteriorAtCoords(-247.0, 6331.0, 32.0), -- Paleto Bay Medical Center
    }
    
    for _, interior in ipairs(interiors) do
        if interior ~= 0 then
            DisableInteriorProp(interior, "hospital_int")
            DisableInteriorProp(interior, "police_int")
            RefreshInterior(interior)
        end
    end
end)

-- Aggressive model suppression
Citizen.CreateThread(function()
    -- List of all ped models to suppress (excluding mechanic and army models we want to keep)
    local pedModels = {
        -- Police
        "s_m_y_cop_01", "s_f_y_cop_01", "s_m_y_sheriff_01", "s_f_y_sheriff_01",
        "s_m_y_ranger_01", "s_f_y_ranger_01", "s_m_m_snowcop_01",
        "s_m_y_hwaycop_01", "csb_cop", "s_m_m_fbi_01", "s_m_m_fbi_02",
        "s_m_m_ciasec_01", "s_m_y_swat_01", "s_m_m_fiboffice_01", "s_m_m_fiboffice_02",
        
        -- Medical
        "s_m_m_paramedic_01", "s_m_m_doctor_01", "s_f_y_scrubs_01",
        
        -- Fire
        "s_m_y_fireman_01",
        
        -- Security
        "s_m_m_security_01", "s_m_y_doorman_01", "s_m_m_armoured_01", "s_m_m_armoured_02",
        "mp_m_securoguard_01", "mp_s_m_armoured_01",
        
        -- Gang members
        "g_m_y_famca_01", "g_m_y_famdnf_01", "g_m_y_famfor_01", "g_f_y_families_01",
        "g_m_y_ballaeast_01", "g_m_y_ballaorig_01", "g_m_y_ballasout_01", "g_f_y_ballas_01",
        "g_m_y_salvaboss_01", "g_m_y_salvagoon_01", "g_m_y_salvagoon_02", "g_m_y_salvagoon_03",
        "g_m_y_mexgang_01", "g_m_y_mexgoon_01", "g_m_y_mexgoon_02", "g_m_y_mexgoon_03",
        "g_m_y_lost_01", "g_m_y_lost_02", "g_m_y_lost_03", "g_f_y_lost_01",
        "g_m_y_korean_01", "g_m_y_korean_02", "g_m_y_korlieut_01",
        "g_m_m_armboss_01", "g_m_m_armgoon_01", "g_m_m_armlieut_01",
        "g_m_y_azteca_01", "g_m_m_mexboss_01", "g_m_m_mexboss_02",
        
        -- Common civilians
        "a_m_y_hipster_01", "a_m_y_hipster_02", "a_m_y_hipster_03",
        "a_m_y_business_01", "a_m_y_business_02", "a_m_y_business_03",
        "a_m_y_downtown_01", "a_m_y_eastsa_01", "a_m_y_eastsa_02",
        "a_f_y_tourist_01", "a_f_y_tourist_02", "a_m_y_tourist_01",
        "a_f_y_bevhills_01", "a_f_y_bevhills_02", "a_f_y_bevhills_03", "a_f_y_bevhills_04",
        "a_m_y_bevhills_01", "a_m_y_bevhills_02", "a_m_m_bevhills_01", "a_m_m_bevhills_02",
        
        -- NOTE: Mechanic and Army models are NOT suppressed to allow interaction
        -- Excluded: s_m_y_xmech_01, s_m_y_xmech_02, s_m_m_autoshop_01, s_m_m_autoshop_02
        -- Excluded: s_m_y_marine_01, s_m_y_marine_02, s_m_y_marine_03, s_m_m_marine_01, s_m_m_marine_02
        -- Excluded: s_m_y_armymech_01, s_m_y_blackops_01, s_m_y_blackops_02, s_m_y_blackops_03
    }
    
    -- Suppress all ped models except the ones we want to keep
    for _, model in ipairs(pedModels) do
        SetPedModelIsSuppressed(GetHashKey(model), true)
    end
    
    -- Also suppress any ped model that gets loaded
    Citizen.Wait(5000) -- Wait for initial load
    
    -- Get all loaded ped models and suppress them
    for i = 1, 1000 do
        local model = i
        if IsModelAPed(model) and not IsModelAVehicle(model) then
            SetPedModelIsSuppressed(model, true)
        end
    end
end)

-- Zone population control
Citizen.CreateThread(function()
    -- List of zones to disable population in
    local zones = {
        "AIRP", "ALAMO", "ALTA", "ARMYB", "BANHAMC", "BANNING", "BEACH", "BHAMCA",
        "BRADP", "BRADT", "BURTON", "CALAFB", "CANNY", "CCREAK", "CHAMH", "CHIL",
        "CHU", "CMSW", "CYPRE", "DAVIS", "DELBE", "DELPE", "DELSOL", "DESRT",
        "DOWNT", "DTVINE", "EAST_V", "EBURO", "ELGORL", "ELYSIAN", "GALFISH", "GOLF",
        "GRAPES", "GREATC", "HARMO", "HAWICK", "HORS", "HUMLAB", "JAIL", "KOREAT",
        "LACT", "LAGO", "LDAM", "LEGSQU", "LMESA", "LOSPUER", "MIRR", "MORN",
        "MOVIE", "MTCHIL", "MTGORDO", "MTJOSE", "MURRI", "NCHU", "NOOSE", "OCEANA",
        "PALCOV", "PALETO", "PALFOR", "PALHIGH", "PALMPOW", "PBLUFF", "PBOX", "PROCOB",
        "RANCHO", "RGLEN", "RICHM", "ROCKF", "RTRAK", "SANAND", "SANCHIA", "SANDY",
        "SKID", "SLAB", "STAD", "STRAW", "TATAMO", "TERMINA", "TEXTI", "TONGVAH",
        "TONGVAV", "VCANA", "VESP", "VINE", "WINDF", "WVINE", "ZANCUDO", "ZP_ORT",
        "ZQ_UAR"
    }
    
    -- Disable population for all zones
    for _, zone in ipairs(zones) do
        SetPopulationBudget(0)
        SetPedPopulationBudget(0)
        SetVehiclePopulationBudget(0)
    end
end)

-- Force cleanup on player spawn
AddEventHandler('playerSpawned', function()
    print("^2[Remove-Ambient-Peds] ^7Player spawned, forcing cleanup...")
    
    local playerCoords = GetEntityCoords(PlayerPedId())
    
    -- Massive cleanup radius
    ClearAreaOfCops(playerCoords.x, playerCoords.y, playerCoords.z, 10000.0, 0)
    ClearAreaOfPeds(playerCoords.x, playerCoords.y, playerCoords.z, 10000.0, 1)
    ClearAreaOfVehicles(playerCoords.x, playerCoords.y, playerCoords.z, 10000.0, false, false, false, false, false)
    
    -- Re-apply all settings
    SetMaxWantedLevel(0)
    SetWantedLevelMultiplier(0.0)
    SetPoliceIgnorePlayer(PlayerId(), true)
    SetDispatchCopsForPlayer(PlayerId(), false)
    SetPlayerWantedLevel(PlayerId(), 0, false)
    SetPlayerWantedLevelNow(PlayerId(), false)
    
    -- Disable all dispatch services again
    for i = 1, 15 do
        EnableDispatchService(i, false)
    end
end)

-- Network event to ensure settings persist
RegisterNetEvent('onClientMapStart')
AddEventHandler('onClientMapStart', function()
    print("^2[Remove-Ambient-Peds] ^7Map started, applying removal settings...")
    
    -- Apply all removal settings
    SetMaxWantedLevel(0)
    SetWantedLevelMultiplier(0.0)
    
    for i = 1, 15 do
        EnableDispatchService(i, false)
    end
    
    -- Disable all scenarios
    for i = 1, 255 do
        SetScenarioTypeEnabled(i, false)
    end
end)

print("^2[Remove-Ambient-Peds] ^7Config loaded with additional removal methods.")
