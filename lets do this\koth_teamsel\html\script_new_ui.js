// NEW CLASS SELECTION AND WEAPON SHOP UI SYSTEM

// Class requirements
const classRequirements = {
    assault: { level: 1, name: 'Assault' },
    medic: { level: 5, name: 'Medic' },
    engineer: { level: 15, name: 'Engineer' },
    heavy: { level: 25, name: 'Heavy' },
    scout: { level: 40, name: 'Scout' }
};

// Weapon categories and data
const weaponCategories = {
    primary: [
        { id: 'WEAPON_BULLPUPRIFLE', name: 'Bullpup Rifle', level: 1, price: 0, icon: 'bullpup_rifle.png' },
        { id: 'WEAPON_ADVANCEDRIFLE', name: 'Advanced Rifle', level: 45, price: 0, icon: 'advanced_rifle.png' },
        { id: 'WEAPON_MILITARYRIFLE', name: 'Military Rifle', level: 7, price: 0, icon: 'military_rifle.png', prestige: 7 },
        { id: 'WEAPON_TACTICALRIFLE', name: 'Battlerifle', level: 12, price: 0, icon: 'tactical_rifle.png', prestige: 12 },
        { id: 'WEAPON_CARBINERIFLE', name: 'Carbine Rifle', level: 20, price: 0, icon: 'carbine_rifle.png' },
        { id: 'WEAPON_SERVICERIFLE', name: 'Service Rifle', level: 6, price: 0, icon: 'service_rifle.png', prestige: 6 },
        { id: 'WEAPON_ASSAULTRIFLE', name: 'Assault Rifle', level: 55, price: 0, icon: 'assault_rifle.png' },
        { id: 'WEAPON_BULLPUPRIFLE_MK2', name: 'Bullpup Rifle Mk2', level: 75, price: 0, icon: 'bullpup_rifle_mk2.png' }
    ],
    secondary: [
        { id: 'WEAPON_PISTOL', name: 'Pistol', level: 1, price: 0, icon: 'pistol.png' },
        { id: 'WEAPON_COMBATPISTOL', name: 'Combat Pistol', level: 10, price: 0, icon: 'combat_pistol.png' },
        { id: 'WEAPON_APPISTOL', name: 'AP Pistol', level: 20, price: 0, icon: 'ap_pistol.png' },
        { id: 'WEAPON_PISTOL50', name: 'Pistol .50', level: 30, price: 0, icon: 'pistol_50.png' },
        { id: 'WEAPON_REVOLVER', name: 'Revolver', level: 40, price: 0, icon: 'revolver.png' },
        { id: 'WEAPON_REVOLVER_MK2', name: 'Revolver Mk2', level: 50, price: 0, icon: 'revolver_mk2.png' }
    ],
    special: [
        { id: 'WEAPON_SPECIALRIFLE', name: 'Special Rifle', level: 35, price: 0, icon: 'special_rifle.png' },
        { id: 'WEAPON_CARBINERIFLE_MK2', name: 'Carbine Rifle Mk2', level: 55, price: 0, icon: 'carbine_rifle_mk2.png' },
        { id: 'WEAPON_SPECIALCARBINE_MK2', name: 'Special Carbine Mk2', level: 70, price: 0, icon: 'special_carbine_mk2.png' },
        { id: 'WEAPON_ASSAULTRIFLE_MK2', name: 'Assault Rifle Mk2', level: 80, price: 0, icon: 'assault_rifle_mk2.png' }
    ],
    throwable: [
        { id: 'WEAPON_GRENADE', name: 'Grenade', level: 5, price: 0, icon: 'grenade.png' },
        { id: 'WEAPON_SMOKEGRENADE', name: 'Smoke Grenade', level: 15, price: 0, icon: 'smoke_grenade.png' },
        { id: 'WEAPON_MOLOTOV', name: 'Molotov', level: 25, price: 0, icon: 'molotov.png' },
        { id: 'WEAPON_STICKYBOMB', name: 'Sticky Bomb', level: 35, price: 0, icon: 'sticky_bomb.png' },
        { id: 'WEAPON_PROXMINE', name: 'Proximity Mine', level: 45, price: 0, icon: 'proximity_mine.png' }
    ]
};

let currentPlayerLevel = 1;
let currentPlayerMoney = 0;
let selectedClass = null;
let currentCategory = 'primary';
let spawnedWeapon = null;

// Initialize class selection
function initializeClassSelection() {
    const classCards = document.querySelectorAll('.class-card');
    
    classCards.forEach(card => {
        card.addEventListener('click', function() {
            const className = this.dataset.class;
            const requirement = classRequirements[className];
            
            // Check if player meets level requirement
            if (currentPlayerLevel >= requirement.level) {
                selectedClass = className;
                openWeaponShop(className);
            } else {
                // Show locked notification
                showNotification(`${requirement.name} class requires level ${requirement.level}`, 'error');
            }
        });
    });
}

// Update class cards based on player level
function updateClassCards(playerLevel) {
    currentPlayerLevel = playerLevel;
    const classCards = document.querySelectorAll('.class-card');
    
    classCards.forEach(card => {
        const className = card.dataset.class;
        const requirement = classRequirements[className];
        const lockOverlay = card.querySelector('.class-lock-overlay');
        const unlockText = card.querySelector('.class-unlock');
        
        if (playerLevel >= requirement.level) {
            // Unlocked
            card.classList.remove('locked');
            if (lockOverlay) lockOverlay.style.display = 'none';
            if (unlockText) unlockText.textContent = 'Unlocked';
        } else {
            // Locked
            card.classList.add('locked');
            if (lockOverlay) lockOverlay.style.display = 'flex';
            if (unlockText) unlockText.textContent = `Unlock at level ${requirement.level}`;
        }
    });
}

// Open weapon shop for selected class
function openWeaponShop(className) {
    const weaponShop = document.getElementById('weapon-shop-new');
    const classNameSpan = document.getElementById('selected-class-name');
    
    // Update class name in header
    classNameSpan.textContent = classRequirements[className].name;
    
    // Show weapon shop
    weaponShop.style.display = 'block';
    
    // Initialize with primary weapons
    showWeaponCategory('primary');
    
    // Send to client
    fetch(`https://${GetParentResourceName()}/selectClass`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ id: className })
    });
}

// Show weapons for selected category
function showWeaponCategory(category) {
    currentCategory = category;
    const weaponGrid = document.getElementById('weapon-grid');
    const weapons = weaponCategories[category] || [];
    
    // Update active category button
    document.querySelectorAll('.weapon-category').forEach(btn => {
        btn.classList.toggle('active', btn.dataset.category === category);
    });
    
    // Clear grid
    weaponGrid.innerHTML = '';
    
    // Populate weapons
    weapons.forEach(weapon => {
        const isLocked = currentPlayerLevel < weapon.level;
        const isSpawned = spawnedWeapon === weapon.id;
        
        const weaponItem = document.createElement('div');
        weaponItem.className = `weapon-item ${isLocked ? 'locked' : ''} ${isSpawned ? 'spawned' : ''}`;
        
        weaponItem.innerHTML = `
            <img src="images/guns/${weapon.icon}" alt="${weapon.name}" class="weapon-icon">
            <div class="weapon-details">
                <h3 class="weapon-name">${weapon.name}</h3>
                <p class="weapon-unlock">
                    ${isLocked ? `Unlocked at ${weapon.prestige ? 'Prestige ' : 'Level '}${weapon.level}` : 'Unlocked'}
                </p>
            </div>
            <div class="weapon-action">
                ${isLocked ? '<span class="weapon-lock-icon">🔒</span>' : 
                  isSpawned ? '<button class="weapon-spawn-btn">Spawned</button>' :
                  '<button class="weapon-spawn-btn" onclick="spawnWeapon(\'' + weapon.id + '\')">Spawn</button>'}
            </div>
        `;
        
        weaponGrid.appendChild(weaponItem);
    });
}

// Spawn weapon
function spawnWeapon(weaponId) {
    // Find weapon data
    let weaponData = null;
    for (const category in weaponCategories) {
        const weapon = weaponCategories[category].find(w => w.id === weaponId);
        if (weapon) {
            weaponData = weapon;
            break;
        }
    }
    
    if (!weaponData) return;
    
    // Check if player can afford it (if it has a price)
    if (weaponData.price > 0 && currentPlayerMoney < weaponData.price) {
        showNotification('Not enough money!', 'error');
        return;
    }
    
    // Update spawned weapon
    spawnedWeapon = weaponId;
    
    // Refresh the current category to update UI
    showWeaponCategory(currentCategory);
    
    // Send to client
    fetch(`https://${GetParentResourceName()}/selectWeapon`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
            weapon: weaponId,
            class: selectedClass,
            price: weaponData.price
        })
    });
}

// Initialize category buttons
document.addEventListener('DOMContentLoaded', function() {
    // Category buttons
    document.querySelectorAll('.weapon-category').forEach(btn => {
        btn.addEventListener('click', function() {
            showWeaponCategory(this.dataset.category);
        });
    });
    
    // Close buttons
    document.getElementById('classes-close').addEventListener('click', function() {
        document.getElementById('classes-selection').style.display = 'none';
        fetch(`https://${GetParentResourceName()}/closeMenu`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({})
        });
    });
    
    document.getElementById('weapon-shop-close').addEventListener('click', function() {
        document.getElementById('weapon-shop-new').style.display = 'none';
        spawnedWeapon = null; // Reset spawned weapon
        fetch(`https://${GetParentResourceName()}/closeMenu`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({})
        });
    });
    
    // Initialize class selection
    initializeClassSelection();
});

// Handle NUI messages for new UI
window.addEventListener('message', function(event) {
    const data = event.data;
    
    switch(data.action) {
        case 'showClassSelection':
            // Update player data
            if (data.level) currentPlayerLevel = data.level;
            if (data.money) currentPlayerMoney = data.money;
            
            // Update class cards
            updateClassCards(currentPlayerLevel);
            
            // Show UI
            document.getElementById('classes-selection').style.display = 'block';
            break;
            
        case 'updateWeaponShopMoney':
            currentPlayerMoney = data.money;
            document.getElementById('weapon-shop-money').textContent = formatMoney(data.money);
            break;
            
        case 'hideClassSelection':
            document.getElementById('classes-selection').style.display = 'none';
            document.getElementById('weapon-shop-new').style.display = 'none';
            break;
    }
});

// Utility functions
function formatMoney(amount) {
    return amount.toLocaleString();
}

function showNotification(message, type = 'info') {
    // You can implement a custom notification system here
    console.log(`[${type.toUpperCase()}] ${message}`);
}

// Export functions for external use
window.spawnWeapon = spawnWeapon;
