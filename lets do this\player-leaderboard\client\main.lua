local isMenuOpen = false

-- Key mapping for F7
RegisterKeyMapping('openLeaderboard', 'Open Player Leaderboard', 'keyboard', 'F7')

-- Command handler for the key mapping
RegisterCommand('openLeaderboard', function()
    if not isMenuOpen then
        openLeaderboard()
    else
        closeLeaderboard()
    end
end, false)

-- Function to open the leaderboard
function openLeaderboard()
    if isMenuOpen then return end

    isMenuOpen = true
    
    -- Always request fresh data when opening
    print('[LEADERBOARD CLIENT] Requesting fresh data from server...')
    TriggerServerEvent('leaderboard:requestData')

    -- Send open message to NUI
    SendNUIMessage({
        type = 'open'
    })

    -- Set NUI focus after a small delay to ensure menu is ready
    SetTimeout(100, function()
        SetNuiFocus(true, true)
    end)
end

-- Function to close the leaderboard
function closeLeaderboard()
    if not isMenuOpen then return end

    isMenuOpen = false

    -- Send close message to NUI first
    SendNUIMessage({
        type = 'close'
    })

    -- Remove NUI focus
    SetNuiFocus(false, false)

    -- Additional safety check to ensure focus is released
    SetTimeout(100, function()
        SetNuiFocus(false, false)
    end)
end

-- Handle server response with player data
RegisterNetEvent('leaderboard:receiveData')
AddEventHandler('leaderboard:receiveData', function(data)
    print('[LEADERBOARD CLIENT] Received data from server')
    if data and data.leaderboard then
        print('[LEADERBOARD CLIENT] Teams data:')
        for team, players in pairs(data.leaderboard) do
            print('  ' .. team .. ': ' .. #players .. ' players')
        end
    end
    
    -- Always send data to NUI, even if menu is closed (it might be opening)
    SendNUIMessage({
        type = 'updateData',
        data = data
    })
end)

-- Handle NUI callbacks
RegisterNUICallback('close', function(data, cb)
    closeLeaderboard()
    cb('ok')
end)

-- Handle escape key to close menu
CreateThread(function()
    while true do
        Wait(100) -- Reduced frequency to improve performance
        if isMenuOpen then
            if IsControlJustPressed(0, 322) then -- ESC key
                closeLeaderboard()
            end
            -- Disable some controls while menu is open
            DisableControlAction(0, 1, true) -- LookLeftRight
            DisableControlAction(0, 2, true) -- LookUpDown
            DisableControlAction(0, 24, true) -- Attack
            DisableControlAction(0, 25, true) -- Aim
        end
    end
end)

-- Safety command to force close menu if it gets stuck
RegisterCommand('closeleaderboard', function()
    if isMenuOpen then
        print('Force closing leaderboard menu...')
        closeLeaderboard()
    else
        print('Leaderboard menu is not open.')
    end
end, false)

-- Additional safety check on resource stop
AddEventHandler('onResourceStop', function(resourceName)
    if GetCurrentResourceName() == resourceName then
        if isMenuOpen then
            SetNuiFocus(false, false)
        end
    end
end)

-- Handle challenge completion notification
RegisterNetEvent('koth:challengeCompleted')
AddEventHandler('koth:challengeCompleted', function(data)
    -- Show notification in game
    BeginTextCommandThefeedPost("STRING")
    AddTextComponentSubstringPlayerName(string.format("~g~Challenge Completed!~s~\n%s\n~y~+%d XP ~g~+$%d", 
        data.name, data.xp, data.money))
    EndTextCommandThefeedPostTicker(true, true)
    
    -- Play sound
    PlaySoundFrontend(-1, "CHALLENGE_UNLOCKED", "HUD_AWARDS", true)
    
    -- Update HUD if it exists
    TriggerEvent('koth:updatePlayerStats')
end)
