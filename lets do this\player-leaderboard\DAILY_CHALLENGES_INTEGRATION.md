# Daily Challenges Integration Guide

## Overview
The daily challenges system is now fully integrated with the player leaderboard. Players get 3 daily challenges that reset every 24 hours, and completing them awards XP and money.

## Daily Challenges (Reset at Midnight)

1. **Zone Dominator** - Get 5 kills while in the KOTH zone
   - Reward: 1500 XP + $5000

2. **Team Player** - Win 3 matches with your team  
   - Reward: 1000 XP + $3000

3. **Survivor** - Get 10 kills without dying
   - Reward: 2500 XP + $7500

## How It Works

### 1. Database Setup
First, run the SQL to create the challenge tables:
```sql
mysql -u root -p your_database < daily_challenges_setup.sql
```

### 2. Automatic Features
- Challenges reset daily at midnight
- Progress is tracked per player
- Rewards are automatically given when completed
- Players see a notification when they complete a challenge
- XP and money are added to their account instantly

### 3. Integration with KOTH Gamemode

You need to trigger these events from your KOTH gamemode:

#### When a player gets a kill in the zone:
```lua
-- In your kill detection system
if inKothZone then
    TriggerEvent('leaderboard:zoneKill')
end
```

#### When a player gets any kill:
```lua
-- For killstreak tracking
TriggerEvent('leaderboard:playerKill')
```

#### When a player dies:
```lua
-- Resets their killstreak
TriggerEvent('leaderboard:playerDied')
```

#### When a team wins a match:
```lua
-- Awards all players on winning team
TriggerEvent('leaderboard:teamWin', 'red') -- or 'blue' or 'green'
```

## Example Integration in koth_teamsel

Add this to your `server.lua`:

```lua
-- In your existing kill event handler
RegisterNetEvent('koth:playerKilled', function(killerSource, victimSource, inZone)
    -- Your existing code...
    
    -- Add challenge tracking
    if killerSource ~= victimSource then -- Not suicide
        TriggerEvent('leaderboard:playerKill')
        
        if inZone then
            TriggerEvent('leaderboard:zoneKill')
        end
    end
    
    -- Track death for killstreak reset
    TriggerEvent('leaderboard:playerDied')
end)

-- Add team win tracking (call this when a match ends)
function AnnounceWinner(winningTeam)
    -- Your existing code...
    
    -- Track for challenges
    TriggerEvent('leaderboard:teamWin', winningTeam)
end
```

## Testing Commands

1. **Test zone kill challenge:**
   ```
   /testzonekill
   ```

2. **Check challenge progress:**
   - Press F7 to open leaderboard
   - Daily challenges show at the top with progress bars

## Features

### For Players:
- See daily challenges in the leaderboard (F7)
- Progress bars show completion status
- Green checkmark when completed
- Instant rewards on completion
- Notification popup with sound

### For Server Owners:
- Challenges reset automatically at midnight
- All progress saved in database
- Can customize challenges by editing the SQL
- Export available for custom challenge updates

## Customizing Challenges

To change the daily challenges, modify the SQL in `ensureDailyChallenges()` function in `server/main.lua`:

```lua
-- Example: Add a headshot challenge
INSERT INTO koth_daily_challenges 
VALUES (CURDATE(), 'headshots', 'Sharpshooter', 'Get 10 headshot kills', 10, 2000, 6000)
```

Then add the event trigger in your gamemode when a headshot occurs.

## Troubleshooting

### Challenges not showing:
1. Check database tables exist
2. Restart the resource
3. Check server console for errors

### Progress not updating:
1. Ensure events are being triggered
2. Check player has a team assigned
3. Verify player exists in database

### Rewards not given:
1. Check console for completion messages
2. Verify XP/money columns exist in database
3. Check for SQL errors in console

## Notes

- Challenges are the same for all players each day
- Progress is individual per player
- Completed challenges stay completed for that day
- New challenges generate at midnight server time
- All data persists through server restarts
