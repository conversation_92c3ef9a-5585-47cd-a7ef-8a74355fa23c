# Team-Based Ped Model and Clothing System Implementation

## Overview
This implementation makes all players spawn with the same ped model (`mp_m_freemode_01`) and applies team-specific clothing variations based on the configurations found in the `jqsus_vantage` clothing files.

## Features Implemented

### 1. **Unified Ped Model**
- All players use the `mp_m_freemode_01` ped model
- Consistent appearance across all teams with only clothing variations

### 2. **Team-Based Clothing**
Based on the clothing configuration from `jqsus_vantage/clothes_dump.json`:

#### Red Team:
- **Militia Vest** - Red variant (Component 9, Drawable 62, Texture 0)
- **Cargo Shorts** - Red variant (Component 4, Drawable 203, Texture 0)
- **Top** - Red variant (Component 11, Drawable 544, Texture 0)

#### Green Team:
- **Militia Vest** - Green variant (Component 9, Drawable 62, Texture 1)
- **Cargo Shorts** - Green variant (Component 4, Drawable 203, Texture 2)
- **Top** - Green variant (Component 11, Drawable 544, Texture 1)

#### Blue Team:
- **Militia Vest** - Blue variant (Component 9, Drawable 62, Texture 2)
- **Cargo Shorts** - Blue variant (Component 4, Drawable 203, Texture 1)
- **Top** - Blue variant (Component 11, Drawable 544, Texture 2)

### 3. **Persistence Through Death**
- Team appearance is automatically reapplied when players respawn
- Death system integration ensures clothing persists

### 4. **Additional Features**
- Consistent face and hair across all teams (buzz cut style)
- Removal of all props (hats, glasses, etc.) for uniform appearance
- Command `/teamoutfit` to manually reapply team appearance if needed

## Files Modified

1. **`client.lua`**:
   - Added `teamClothing` configuration table
   - Added `ApplyTeamAppearance()` function
   - Modified spawn handler to apply appearance
   - Added event handler for death system integration
   - Added respawn appearance reapplication

2. **`client_death.lua`**:
   - Modified respawn function to trigger team appearance reapplication
   - Added 500ms delay to ensure proper application after respawn

## Usage

### Automatic Application:
- Team appearance is automatically applied when:
  - Player selects a team and spawns
  - Player respawns after death
  - Player uses the `/teamoutfit` command

### Manual Commands:
- `/teamoutfit` - Manually reapply your team's appearance
- `/resetteam` - Reset team selection (for testing)

## Technical Details

### Component IDs Used:
- 0: Face
- 2: Hair
- 3: Torso/Arms
- 4: Legs
- 6: Shoes
- 8: Undershirt
- 9: Body Armor (Vest)
- 11: Jacket/Top

### Model Loading:
- Proper model request and loading with timeout handling
- Model cleanup after application to prevent memory issues

### Network Sync:
- Appearance changes are automatically synced to other players
- Uses native GTA V/FiveM networking for ped components

## Testing

To test the implementation:
1. Join the server and select a team (red, green, or blue)
2. Observe that your character spawns with the team-specific clothing
3. Get killed and respawn to verify appearance persistence
4. Use `/teamoutfit` to manually reapply appearance
5. Switch teams using `/resetteam` and `/showteamselect` to test different variations

## Notes

- The clothing drawable and texture IDs are from the custom `jqsus_vantage` clothing pack
- Ensure the clothing resource is properly loaded before the KOTH resource
- All players will have the same base appearance with only color variations
- The system is designed to work with the existing team selection and spawn system

## Future Enhancements

- Add female ped model support with corresponding clothing
- Add more clothing variations or accessories
- Add team-specific helmets or other props
- Add rank-based clothing unlocks
