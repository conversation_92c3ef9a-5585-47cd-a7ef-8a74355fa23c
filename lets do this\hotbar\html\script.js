let hotbarVisible = false;
let hotbarItems = {};

// Initialize when page loads
document.addEventListener('DOMContentLoaded', function() {
    console.log('🎮 Hotbar UI loaded');
    console.log('📁 Resource name:', GetParentResourceName());

    // Test if we can access images
    testImageAccess();

    // Add click handlers to slots
    document.querySelectorAll('.hotbar-slot').forEach(slot => {
        slot.addEventListener('click', function() {
            const slotNumber = parseInt(this.dataset.slot);
            useSlot(slotNumber);
        });

        // Add right-click handler to remove items
        slot.addEventListener('contextmenu', function(event) {
            event.preventDefault();
            const slotNumber = parseInt(this.dataset.slot);
            removeFromSlot(slotNumber);
        });
    });

    // Add debug toggle (press F12 to toggle debug overlay)
    document.addEventListener('keydown', function(event) {
        if (event.key === 'F12') {
            event.preventDefault();
            toggleDebugOverlay();
        }
    });
});

// Test image access
function testImageAccess() {
    const testImagePath = 'images/weapon_pistol50.png';

    console.log('🧪 Testing image access:', testImagePath);

    const testImg = new Image();
    testImg.onload = function() {
        console.log('✅ Image access test PASSED - Images should load correctly');
        updateDebugInfo('Image Access: ✅ WORKING');
    };
    testImg.onerror = function() {
        console.error('❌ Image access test FAILED - Check file paths and manifest');
        updateDebugInfo('Image Access: ❌ FAILED');
    };
    testImg.src = testImagePath;
}

// Debug overlay functions
function toggleDebugOverlay() {
    const overlay = document.getElementById('debug-overlay');
    overlay.style.display = overlay.style.display === 'none' ? 'block' : 'none';
}

function updateDebugInfo(info) {
    const debugInfo = document.getElementById('debug-info');
    if (debugInfo) {
        debugInfo.innerHTML += '<br>' + info;
    }
}

// Listen for messages from the game
window.addEventListener('message', function(event) {
    const data = event.data;
    
    switch(data.type) {
        case 'show':
            showHotbar(data.visible);
            break;
        case 'updateHotbar':
            updateHotbar(data.items);
            break;
        case 'updateSlot':
            updateSlot(data.slot, data.item);
            break;
        case 'useSlot':
            animateSlotUse(data.slot);
            break;
    }
});

// Show/hide hotbar
function showHotbar(visible) {
    hotbarVisible = visible;
    const container = document.getElementById('hotbar-container');
    
    if (visible) {
        container.classList.add('visible');
    } else {
        container.classList.remove('visible');
    }
}

// Update entire hotbar
function updateHotbar(items) {
    hotbarItems = items;
    
    for (let i = 1; i <= 5; i++) {
        updateSlot(i, items[i]);
    }
}

// Update specific slot
function updateSlot(slotNumber, item) {
    const slot = document.querySelector(`[data-slot="${slotNumber}"]`);
    if (!slot) return;
    
    const icon = slot.querySelector('.slot-icon');
    const count = slot.querySelector('.slot-count');
    
    if (item && item.name && item.name !== "") {
        // Slot has an item
        slot.classList.remove('empty');

        // Handle weapon images - use simple relative paths for FiveM NUI
        if (item.icon && item.icon.includes('.png')) {
            // Use simple relative path from the HTML file
            const imagePath = item.icon; // Should be "images/weapon_name.png"
            console.log(`Setting weapon image: ${imagePath} for ${item.name}`);

            // Use CSS background-image instead of img element to prevent flickering
            icon.innerHTML = '';
            icon.style.backgroundImage = `url('${imagePath}')`;
            icon.style.backgroundSize = 'contain';
            icon.style.backgroundRepeat = 'no-repeat';
            icon.style.backgroundPosition = 'center';
            icon.classList.add('has-image');

            // Test if image loads by creating a hidden test image
            const testImg = new Image();
            testImg.onload = function() {
                console.log(`✅ Image verified: ${imagePath}`);
            };
            testImg.onerror = function() {
                console.error(`❌ Image failed: ${imagePath}, using fallback`);
                icon.style.backgroundImage = `url('images/weapon_pistol50.png')`;
            };
            testImg.src = imagePath;

        } else {
            // No image provided, use default
            console.log(`No image for ${item.name}, using default`);
            icon.innerHTML = '';
            icon.style.backgroundImage = `url('images/weapon_pistol50.png')`;
            icon.style.backgroundSize = 'contain';
            icon.style.backgroundRepeat = 'no-repeat';
            icon.style.backgroundPosition = 'center';
            icon.classList.add('has-image');
        }

        if (item.count > 1) {
            count.textContent = item.count;
            count.style.display = 'block';
        } else if (item.count === 1) {
            count.textContent = '';
            count.style.display = 'none';
        } else {
            count.textContent = '';
            count.style.display = 'none';
        }

        // Add tooltip
        slot.title = item.name + (item.count > 1 ? ` (${item.count})` : '');
    } else {
        // Empty slot - clear everything
        slot.classList.add('empty');
        icon.innerHTML = '';
        icon.textContent = '';
        icon.style.backgroundImage = 'none';
        icon.classList.remove('has-image');
        count.textContent = '';
        count.style.display = 'none';
        slot.title = `Slot ${slotNumber} - Empty`;
    }
    
    // Store item data
    hotbarItems[slotNumber] = item;
}

// Use slot function
function useSlot(slotNumber) {
    const item = hotbarItems[slotNumber];

    if (item && item.name && item.name !== "") {
        // Send message to game to use item
        fetch(`https://${GetParentResourceName()}/useSlot`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json; charset=UTF-8',
            },
            body: JSON.stringify({
                slot: slotNumber,
                item: item.name
            })
        }).catch(err => {
            console.log('Error using slot:', err);
        });
    }
}

// Remove item from slot function
function removeFromSlot(slotNumber) {
    const item = hotbarItems[slotNumber];

    if (item && item.name && item.name !== "") {
        // Send message to game to remove item from slot
        fetch(`https://${GetParentResourceName()}/removeSlot`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json; charset=UTF-8',
            },
            body: JSON.stringify({
                slot: slotNumber
            })
        }).catch(err => {
            console.log('Error removing from slot:', err);
        });

        console.log(`Removed ${item.name} from slot ${slotNumber}`);
    }
}

// Animate slot use
function animateSlotUse(slotNumber) {
    // No animations - function kept for compatibility
}

// Get resource name helper
function GetParentResourceName() {
    return window.location.hostname;
}

// Keyboard event handling
document.addEventListener('keydown', function(event) {
    if (!hotbarVisible) return;
    
    // Handle number keys 1-5
    if (event.key >= '1' && event.key <= '5') {
        const slotNumber = parseInt(event.key);
        useSlot(slotNumber);
        event.preventDefault();
    }
});


