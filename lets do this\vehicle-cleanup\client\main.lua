-- Table to track player vehicles and their timers
local vehicleTimers = {}
local playerVehicles = {}

-- Function to show notification
local function ShowNotification(message)
    if Config.Notifications.enabled then
        BeginTextCommandThefeedPost("STRING")
        AddTextComponentSubstringPlayerName(message)
        EndTextCommandThefeedPostTicker(false, true)
    end
end

-- Function to start deletion timer for a vehicle
local function StartDeletionTimer(vehicle)
    if not DoesEntityExist(vehicle) then return end
    
    local vehicleId = NetworkGetNetworkIdFromEntity(vehicle)
    
    if Config.Debug then
        print(('[Vehicle Cleanup] Starting deletion timer for vehicle %s (Net ID: %s)'):format(vehicle, vehicleId))
    end
    
    -- Clear any existing timer
    if vehicleTimers[vehicle] then
        vehicleTimers[vehicle].active = false
    end
    
    -- Create new timer
    vehicleTimers[vehicle] = {
        active = true,
        startTime = GetGameTimer(),
        warned = false
    }
    
    -- Timer thread
    Citizen.CreateThread(function()
        local timer = vehicleTimers[vehicle]
        
        while timer and timer.active and DoesEntityExist(vehicle) do
            local elapsed = (GetGameTimer() - timer.startTime) / 1000 -- Convert to seconds
            local remaining = Config.DeleteTime - elapsed
            
            -- Check if player re-entered the vehicle
            local playerPed = PlayerPedId()
            if IsPedInVehicle(playerPed, vehicle, false) then
                if Config.Debug then
                    print('[Vehicle Cleanup] Player re-entered vehicle, cancelling timer')
                end
                timer.active = false
                break
            end
            
            -- Warning notification
            if remaining <= Config.Notifications.warningTime and not timer.warned then
                timer.warned = true
                ShowNotification(Config.Notifications.warningMessage)
            end
            
            -- Delete vehicle
            if remaining <= 0 then
                if Config.Debug then
                    print('[Vehicle Cleanup] Deleting vehicle due to inactivity')
                end
                
                -- Delete the vehicle
                if NetworkHasControlOfEntity(vehicle) then
                    SetEntityAsMissionEntity(vehicle, true, true)
                    DeleteVehicle(vehicle)
                else
                    -- Request control and try again
                    NetworkRequestControlOfEntity(vehicle)
                    local timeout = GetGameTimer() + 2000
                    while not NetworkHasControlOfEntity(vehicle) and GetGameTimer() < timeout do
                        Citizen.Wait(0)
                    end
                    
                    if NetworkHasControlOfEntity(vehicle) then
                        SetEntityAsMissionEntity(vehicle, true, true)
                        DeleteVehicle(vehicle)
                    else
                        -- If we can't get control, ask server to delete it
                        TriggerServerEvent('vehicleCleanup:requestDelete', vehicleId)
                    end
                end
                
                ShowNotification(Config.Notifications.deletedMessage)
                
                -- Clean up
                vehicleTimers[vehicle] = nil
                for i, veh in ipairs(playerVehicles) do
                    if veh == vehicle then
                        table.remove(playerVehicles, i)
                        break
                    end
                end
                
                break
            end
            
            Citizen.Wait(Config.CheckInterval)
        end
        
        -- Clean up if vehicle no longer exists
        if not DoesEntityExist(vehicle) then
            vehicleTimers[vehicle] = nil
        end
    end)
end

-- Function to cancel deletion timer
local function CancelDeletionTimer(vehicle)
    if vehicleTimers[vehicle] then
        vehicleTimers[vehicle].active = false
        vehicleTimers[vehicle] = nil
        
        if Config.Debug then
            print('[Vehicle Cleanup] Cancelled deletion timer for vehicle')
        end
    end
end

-- Main thread to monitor player entering/exiting vehicles
Citizen.CreateThread(function()
    local lastVehicle = nil
    
    while true do
        local playerPed = PlayerPedId()
        local vehicle = GetVehiclePedIsIn(playerPed, false)
        
        -- Player entered a vehicle
        if vehicle ~= 0 and vehicle ~= lastVehicle then
            lastVehicle = vehicle
            
            -- Cancel any deletion timer for this vehicle
            CancelDeletionTimer(vehicle)
            
            -- Track this as a player vehicle
            local found = false
            for _, veh in ipairs(playerVehicles) do
                if veh == vehicle then
                    found = true
                    break
                end
            end
            
            if not found then
                table.insert(playerVehicles, vehicle)
            end
            
            if Config.Debug then
                print('[Vehicle Cleanup] Player entered vehicle')
            end
        
        -- Player exited a vehicle
        elseif vehicle == 0 and lastVehicle ~= nil then
            -- Check if vehicle should be excluded
            local vehicleClass = GetVehicleClass(lastVehicle)
            local excluded = false
            
            for _, class in ipairs(Config.ExcludedClasses) do
                if vehicleClass == class then
                    excluded = true
                    break
                end
            end
            
            -- Start deletion timer if not excluded
            if not excluded and DoesEntityExist(lastVehicle) then
                StartDeletionTimer(lastVehicle)
            end
            
            lastVehicle = nil
            
            if Config.Debug then
                print('[Vehicle Cleanup] Player exited vehicle')
            end
        end
        
        Citizen.Wait(250) -- Check 4 times per second
    end
end)

-- Clean up timers when resource stops
AddEventHandler('onResourceStop', function(resourceName)
    if GetCurrentResourceName() ~= resourceName then return end
    
    -- Cancel all active timers
    for vehicle, timer in pairs(vehicleTimers) do
        if timer.active then
            timer.active = false
        end
    end
end)

-- Command to check vehicle timers (debug)
if Config.Debug then
    RegisterCommand('vehicletimers', function()
        print('[Vehicle Cleanup] Active timers:')
        local count = 0
        for vehicle, timer in pairs(vehicleTimers) do
            if timer.active and DoesEntityExist(vehicle) then
                local elapsed = (GetGameTimer() - timer.startTime) / 1000
                local remaining = Config.DeleteTime - elapsed
                print(('  Vehicle %s: %d seconds remaining'):format(vehicle, math.floor(remaining)))
                count = count + 1
            end
        end
        print(('Total active timers: %d'):format(count))
    end, false)
end

print('[Vehicle Cleanup] Client script loaded')
