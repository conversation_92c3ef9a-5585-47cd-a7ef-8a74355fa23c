-- Vantage Weapon Images Handler - Server Side
-- Provides server-side image management functions

local ImageServer = {}

-- Server-side export functions (if needed for future expansion)
function ImageServer.GetWeaponImagePath(weaponName)
    -- This could be used for server-side image validation or logging
    return exports['vantage-weapon-images-handler']:GetWeaponImage(weaponName)
end

-- Export for other server resources
exports('GetWeaponImagePath', ImageServer.GetWeaponImagePath)

print('[VANTAGE-IMAGES] Server-side image handler loaded')
