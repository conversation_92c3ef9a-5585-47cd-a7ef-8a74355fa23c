let isMenuOpen = false;
let currentData = null;

// Listen for messages from the game
window.addEventListener('message', function(event) {
    const data = event.data;
    
    switch(data.type) {
        case 'open':
            openMenu();
            break;
        case 'close':
            closeMenu();
            break;
        case 'updateData':
            updateMenuData(data.data);
            break;
    }
});

// Open the menu
function openMenu() {
    isMenuOpen = true;
    document.getElementById('leaderboard-container').classList.remove('hidden');
    startTimer();
}

// Close the menu
function closeMenu() {
    isMenuOpen = false;
    document.getElementById('leaderboard-container').classList.add('hidden');
    stopTimer();
}

// Update menu with new data
function updateMenuData(data) {
    currentData = data;
    updatePlayerInfo(data.currentPlayer);
    updateChallenges(data.challenges);
    updateLeaderboard(data.leaderboard);
}

// Update player information
function updatePlayerInfo(player) {
    document.getElementById('player-uid').textContent = player.uid;
    document.getElementById('player-name').textContent = player.name;
    document.getElementById('player-level').textContent = player.level;
    document.getElementById('player-prestige').textContent = player.prestige;
    document.getElementById('player-kills').textContent = player.kills;
    document.getElementById('player-deaths').textContent = player.deaths;
    document.getElementById('player-assists').textContent = player.assists;
    document.getElementById('player-captures').textContent = player.captures;
    
    // Update the player ID display in the header
    const playerIdElement = document.querySelector('.player-id');
    if (playerIdElement) {
        playerIdElement.innerHTML = `[<span id="player-uid">${player.uid}</span>] <span id="player-name">${player.name}</span>`;
    }
}

// Update challenges
function updateChallenges(challenges) {
    const challengesList = document.getElementById('challenges-list');
    challengesList.innerHTML = '';
    
    challenges.forEach(challenge => {
        const challengeElement = document.createElement('div');
        challengeElement.className = 'challenge-item';
        
        challengeElement.innerHTML = `
            <div class="challenge-name">${challenge.name}</div>
            <div class="challenge-desc">${challenge.description}</div>
            <div class="challenge-progress">
                <span>${challenge.progress}/${challenge.target}</span>
                <span class="challenge-reward">💰${challenge.reward.cash} 🎯${challenge.reward.xp}</span>
            </div>
        `;
        
        challengesList.appendChild(challengeElement);
    });
}

// Update leaderboard
function updateLeaderboard(leaderboard) {
    console.log('[LEADERBOARD UI] Updating leaderboard with data:', leaderboard);
    
    // Ensure we have arrays for each team
    const blueTeam = leaderboard.blue || [];
    const redTeam = leaderboard.red || [];
    const greenTeam = leaderboard.green || [];
    
    console.log('[LEADERBOARD UI] Blue team:', blueTeam.length, 'players');
    console.log('[LEADERBOARD UI] Red team:', redTeam.length, 'players');
    console.log('[LEADERBOARD UI] Green team:', greenTeam.length, 'players');
    
    updateTeam('blue-team-list', blueTeam);
    updateTeam('red-team-list', redTeam);
    updateTeam('green-team-list', greenTeam);
}

// Update individual team
function updateTeam(teamId, players) {
    const teamList = document.getElementById(teamId);
    if (!teamList) {
        console.error('[LEADERBOARD UI] Team list not found:', teamId);
        return;
    }
    
    teamList.innerHTML = '';
    
    // If no players, show empty message
    if (!players || players.length === 0) {
        const emptyRow = document.createElement('div');
        emptyRow.className = 'player-row empty-row';
        emptyRow.style.textAlign = 'center';
        emptyRow.style.padding = '20px';
        emptyRow.style.opacity = '0.5';
        emptyRow.innerHTML = 'No players in this team';
        teamList.appendChild(emptyRow);
        return;
    }
    
    players.forEach((player, index) => {
        const playerRow = document.createElement('div');
        playerRow.className = 'player-row';
        
        // Add online status class if player is online
        if (player.online) {
            playerRow.classList.add('online');
        }
        
        // Add rank class for top 3
        if (index === 0) playerRow.classList.add('rank-1');
        else if (index === 1) playerRow.classList.add('rank-2');
        else if (index === 2) playerRow.classList.add('rank-3');
        
        // Check if this is the current player
        if (currentData && currentData.currentPlayer && player.uid === currentData.currentPlayer.uid) {
            playerRow.classList.add('current-player');
        }
        
        playerRow.innerHTML = `
            <div class="col-uid">[${player.uid}]</div>
            <div class="col-player">${player.name}${player.online ? ' <span class="online-indicator">●</span>' : ''}</div>
            <div class="col-score">${player.score.toLocaleString()}</div>
            <div class="col-kills">${player.kills}</div>
            <div class="col-deaths">${player.deaths}</div>
            <div class="col-assists">${player.assists}</div>
            <div class="col-kd">${player.kd.toFixed(2)}</div>
        `;
        
        teamList.appendChild(playerRow);
    });
}

// Timer functionality
let timerInterval;

function startTimer() {
    updateTimer();
    timerInterval = setInterval(updateTimer, 1000);
}

function stopTimer() {
    if (timerInterval) {
        clearInterval(timerInterval);
    }
}

function updateTimer() {
    // Calculate time until next reset (example: reset at midnight)
    const now = new Date();
    const tomorrow = new Date(now);
    tomorrow.setDate(tomorrow.getDate() + 1);
    tomorrow.setHours(0, 0, 0, 0);
    
    const timeLeft = tomorrow - now;
    const hours = Math.floor(timeLeft / (1000 * 60 * 60));
    const minutes = Math.floor((timeLeft % (1000 * 60 * 60)) / (1000 * 60));
    const seconds = Math.floor((timeLeft % (1000 * 60)) / 1000);
    
    const timerText = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    document.getElementById('reset-timer').textContent = timerText;
}

// Handle escape key and clicks outside menu
document.addEventListener('keydown', function(event) {
    if (event.key === 'Escape' && isMenuOpen) {
        closeMenuAndNotify();
    }
});

document.getElementById('leaderboard-container').addEventListener('click', function(event) {
    if (event.target === this) {
        closeMenuAndNotify();
    }
});

// Close menu and notify game
function closeMenuAndNotify() {
    if (!isMenuOpen) return;

    closeMenu();

    // Use a more reliable method to communicate with the game
    if (window.invokeNative) {
        // We're in the game environment
        fetch(`https://${getResourceName()}/close`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json; charset=UTF-8',
            },
            body: JSON.stringify({})
        }).catch(err => {
            console.log('Error closing menu:', err);
        });
    }
}

// Get resource name more reliably
function getResourceName() {
    if (window.GetParentResourceName) {
        return window.GetParentResourceName();
    }
    // Fallback methods
    return window.location.hostname || 'player-leaderboard';
}

// Ensure menu starts closed
document.addEventListener('DOMContentLoaded', function() {
    closeMenu();
});
