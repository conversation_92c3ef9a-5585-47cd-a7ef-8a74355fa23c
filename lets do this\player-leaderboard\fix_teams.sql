-- Fix team assignments - remove spaces and ensure lowercase
UPDATE koth_players 
SET current_team = LOWER(TRIM(current_team))
WHERE current_team IS NOT NULL;

-- Check for any non-standard team values
SELECT DISTINCT current_team, COUNT(*) as player_count 
FROM koth_players 
GROUP BY current_team;

-- Fix any team values that aren't exactly 'red', 'blue', or 'green'
UPDATE koth_players 
SET current_team = CASE 
    WHEN LOWER(TRIM(current_team)) LIKE '%red%' THEN 'red'
    WHEN LOWER(TRIM(current_team)) LIKE '%blue%' THEN 'blue'
    WHEN LOWER(TRIM(current_team)) LIKE '%green%' THEN 'green'
    ELSE NULL
END
WHERE current_team IS NOT NULL;

-- Assign teams to players without valid teams
UPDATE koth_players 
SET current_team = CASE 
    WHEN (id % 3) = 0 THEN 'red'
    WHEN (id % 3) = 1 THEN 'blue'
    ELSE 'green'
END
WHERE current_team IS NULL OR current_team NOT IN ('red', 'blue', 'green');

-- Final check
SELECT current_team, COUNT(*) as player_count 
FROM koth_players 
WHERE current_team IN ('red', 'blue', 'green')
GROUP BY current_team;

-- Show some sample players
SELECT id, player_name, current_team, kills, deaths, zone_kills 
FROM koth_players 
WHERE current_team IS NOT NULL 
LIMIT 10;
