# Hotbar System

A FiveM resource that provides a customizable hotbar with 5 quick-access slots for items, weapons, or any other game elements.

## Features

- **5 Numbered Slots**: Quick access slots numbered 1-5
- **Weapon Integration**: Automatically detects weapons spawned from vMenu
- **Quick Weapon Switching**: Use number keys to switch between weapons
- **Weapon Images**: Shows actual weapon images from the images folder
- **Weapon Wheel Disabled**: GTA's default weapon wheel and number key binds are completely disabled
- **Item Management**: Add, remove, and use items from slots
- **Keyboard Controls**: Use number keys 1-5 to activate slots
- **Toggle Visibility**: Press H to show/hide the hotbar
- **Responsive Design**: Works on different screen sizes
- **Export Functions**: Easy integration with other resources

## Installation

1. Copy the `hotbar` folder to your FiveM resources directory
2. Add `ensure hotbar` to your `server.cfg`
3. Restart your server

## Controls

- **Number Keys 1-5**: Use items/switch to weapons in corresponding slots (press same key again to unequip)
- **H Key**: Toggle hotbar visibility
- **Left Click**: Click on slots to use items/switch weapons (click same slot again to unequip)
- **Right Click**: Remove items/weapons from hotbar slots

## vMenu Integration

The hotbar automatically integrates with vMenu:

1. **Automatic Detection**: When you spawn weapons using vMenu, they automatically appear in the hotbar
2. **Multi-Weapon Support**: Spawn multiple weapons and they'll fill all 5 hotbar slots automatically
3. **Quick Switching**: Press number keys 1-5 to quickly switch between weapons
4. **Real-time Updates**: Weapons are automatically removed from hotbar when you no longer have them
5. **Ammo Display**: Shows current ammo count for weapons with live updates
6. **Smart Slot Management**: If hotbar is full, new weapons replace the last slot

## Weapon Wheel Disabled

The hotbar completely replaces GTA's default weapon wheel:

- **TAB Key Disabled**: The weapon wheel will no longer open
- **Mouse Wheel Disabled**: Scrolling to change weapons is blocked
- **Number Keys Overridden**: Keys 1-5 now control hotbar instead of default weapon categories
- **Weapon Categories Disabled**: All weapon category shortcuts are disabled
- **Clean Interface**: Weapon wheel HUD elements are hidden

This completely overrides GTA's default weapon system, forcing players to use the hotbar for all weapon switching.

## Weapon Management

**Toggle Weapon Equipping:**
- **Press same slot again**: If you're already using a weapon, pressing its slot number will unequip it (go unarmed)
- **Press different slot**: Switch to that weapon
- **Right-click slots**: Remove weapons from hotbar slots
- **Auto-unequip**: Automatically goes unarmed when removing the currently equipped weapon

**Smart Weapon Handling:**
- Weapons are automatically removed from hotbar when you no longer have them
- Right-clicking a weapon slot removes it from the hotbar but keeps the weapon in inventory
- If you remove the currently equipped weapon, you automatically become unarmed
- Toggle behavior: Same slot = equip/unequip, different slot = switch weapon

## Usage for Developers

### Client-side Functions

```lua
-- Set an item in a hotbar slot
exports['hotbar']:SetHotbarItem(slot, itemName, itemIcon, itemCount)

-- Clear a hotbar slot
exports['hotbar']:ClearHotbarSlot(slot)

-- Get all hotbar items
local items = exports['hotbar']:GetHotbarItems()
```

### Server-side Functions

```lua
-- Set an item in a player's hotbar
exports['hotbar']:SetPlayerHotbarItem(playerId, slot, itemName, itemIcon, itemCount)

-- Clear a player's hotbar slot
exports['hotbar']:ClearPlayerHotbarSlot(playerId, slot)

-- Get a player's hotbar items
local items = exports['hotbar']:GetPlayerHotbarItems(playerId)
```

### Events

```lua
-- Listen for item usage (server-side)
AddEventHandler('hotbar:itemUsed', function(playerId, itemName, slot)
    print("Player " .. playerId .. " used " .. itemName .. " from slot " .. slot)
end)
```

## Commands (Testing)

- `/sethotbar <slot> <itemname> <count> [icon]` - Set an item in a slot
- `/clearhotbar <slot>` - Clear a specific slot

## Examples

```lua
-- Add a weapon to slot 1
exports['hotbar']:SetHotbarItem(1, "Pistol", "🔫", 1)

-- Add health kits to slot 2
exports['hotbar']:SetHotbarItem(2, "Health Kit", "❤️", 3)

-- Clear slot 3
exports['hotbar']:ClearHotbarSlot(3)
```

## Customization

### Styling
Edit `html/style.css` to customize:
- Colors and themes
- Slot sizes
- Animations
- Positioning

### Icons
You can use:
- Emoji icons (🔫, ❤️, 🛡️)
- Unicode symbols
- Custom image URLs
- Font icons

## Integration with Inventory Systems

The hotbar can be easily integrated with existing inventory systems by listening to inventory events and updating hotbar slots accordingly.

```lua
-- Example integration
AddEventHandler('inventory:itemAdded', function(itemName, count)
    -- Find empty slot and add item
    for i = 1, 5 do
        local items = exports['hotbar']:GetHotbarItems()
        if items[i].name == "" then
            exports['hotbar']:SetHotbarItem(i, itemName, GetItemIcon(itemName), count)
            break
        end
    end
end)
```

## License

This resource is open source and free to use and modify.
