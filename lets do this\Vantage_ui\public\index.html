  <!DOCTYPE html>
  <html lang="en">

  <head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.11.2/css/all.css">
    <script src="https://code.jquery.com/jquery-3.4.1.min.js"
      integrity="sha256-CSXorXvZcTkaix6Yvo6HppcZGetbYMGWSFlBw8HfCJo=" crossorigin="anonymous" defer></script>
    <script src="../build/build.js" defer></script>
  </head>

  <body>
    <div id="player_notifications" class="top-right"></div>
    <div id="player_notifications" class="bottom-right"></div>
    <div id="player_notifications" class="top-left"></div>
    <div id="player_hud">
      <div class="player_bar">
        <div class="health">
          <div class="flowbar"></div>
          <div class="text">
            <i class=""></i>
            <span class="value"></span>
          </div>
        </div>
        <div class="armour">
          <div class="flowbar"></div>
          <div class="text">
            <i class="fas fa-shield-alt"></i>
            <span class="value"></span>
          </div>
        </div>
        <div class="unconscious">
          <div class="text">
            <i class=""></i>
            [E] to Respawn
          </div>
        </div>
      </div>

       <div class="voice_bar active">
        <svg class="speaker" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 480 480">
          <path class="v_1"
            d="M288 75.8v328.6c0 11.7-12.9 18.9-22.8 12.8l-128-78.8H18.5c-8.3 0-15-6.7-15-15V156.6c0-8.3 6.7-15 15-15h118.7L265 63c10-6.2 23 1 23 12.8zM337.3 326c35.9-36.3 45.3-91.1 24.2-137.5-5.8-12.8-14.3-24.5-24.2-34.5-10.9-11-27.8 6-17 17 8.9 9 16.6 20.3 21.1 31.3C346.7 215 349 227 349 240c0 12.9-2.2 24.4-7.2 37.1-4.4 11.2-12.4 22.9-21.4 31.9-10.9 11 6 28 16.9 17z" />
          <path class="v_2"
            d="M363.1 384.4c29.4-29.6 49.8-67.9 56.7-109.1 7-41.2 1.5-83.1-15.8-121.1-9.9-21.8-24.1-41.7-41-58.7-10.9-11-27.9 6-17 17 26.9 27.1 44.2 60.8 50.6 98.5 1.3 7.7 2.1 16.5 2.2 25.5.2 9.1-.3 18.3-1.5 27.4-.9 7.1-3.3 18.1-5.7 26.5-2.5 8.7-5.6 17-9 24.5-9.2 19.9-21.4 37.2-36.6 52.5-10.8 11 6.2 28 17.1 17z" />
          <path class="v_3"
            d="M399.6 417.3c36.2-36.4 61.2-83.7 69.8-134.4 8.5-50.5 1.7-102.1-19.7-148.6-12.2-26.6-29.5-50.8-50.1-71.5-10.9-11-27.9 6-17 17 33.5 33.7 55.5 76.2 63.5 123.2 1 5.8 1.4 9.1 2 15.2.5 5.7.8 11.4 1 17.1.2 11.7-.4 23.4-1.9 34.9-.4 3.5-1.7 10.8-2.8 15.8-1.2 5.6-2.6 11.2-4.1 16.7-3.2 11-7.2 22-11.6 31.6-11.5 24.9-26.9 46.7-46 66-11 11 6 27.9 16.9 17z" />
        </svg>
      </div> 

      <div class="location_bar" id="location_bar_1">
        <div class="location" id="location_1"></div>
      </div>

      <div class="location_bar" id="location_bar_2">
        <div class="heading" id="aop"></div>
        <div class="location" id="location_2"></div>
      </div>

      <div class="vehicle_bar">
        <div class="seatbelt">
          <span>
            BELT
          </span>
        </div>
        <div class="speed">
          <span class="value"></span>
          <span class="km"> km/h</span>
          <span class="knots"> knots</span>
        </div>
      </div>
      <img src="" alt="" style="opacity: 100; height: auto; width: 6%; position: absolute;">
    </div>
  </body>

  <!-- HUD COLOUR CHANGER -->
<head>
  <meta charset="UTF-8">
  <title>HUD Color Picker</title>
  <style>
      body {
          margin: 0;
          padding: 0;
          font-family: Arial, sans-serif;
      }
      .hud {
        display: none;
      }
      .color-picker-container {
          display: none;
          position: fixed;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          background-color: #333;
          padding: 20px;
          border-radius: 5px;
          z-index: 2000;
      }
      label {
          display: block;
          margin-top: 10px;
      }
      input[type="color"] {
          width: 100%;
          height: 30px;
          border: none;
          margin-top: 5px;
      }
      button {
          margin-top: 20px;
          padding: 10px;
          width: 100%;
          border: none;
          background-color: #0076ad;
          color: white;
          font-size: 16px;
          cursor: pointer;
      }
  </style>
</head>
<body>

  <div class="hud">
    
      <div class="player_bar">
          <div class="health">
              <div class="flowbar"></div>
          </div>
          <div class="armour">
              <div class="flowbar"></div>
          </div>
      </div>
  </div>

  <script>
      window.addEventListener("message", function(event) {
          if (event.data.action === "openHudColorPicker") {
              document.querySelector('.color-picker-container').style.display = "block";
          } else if (event.data.action === "closeHudColorPicker") {
              document.querySelector('.color-picker-container').style.display = "none";
          }

          if (event.data.colors) {
              document.querySelector('.player_bar .health .flowbar').style.background = event.data.colors.main;
              document.querySelector('.player_bar .health').style.background = event.data.colors.under;
              document.querySelector('.player_bar .armour .flowbar').style.background = event.data.colors.armour;
          }

      });

      function saveColors() {
    const mainColor = document.getElementById("mainColor").value;
    const underColor = document.getElementById("underColor").value;
    const armourColor = document.getElementById("armourColor").value;
    
    fetch(`https://${GetParentResourceName()}/saveColors`, {
        method: "POST",
        headers: {
            "Content-Type": "application/json"
        },
        body: JSON.stringify({
            mainColor: mainColor,
            underColor: underColor,
            armourColor: armourColor
        })
    }).then(() => {
        document.querySelector('.color-picker-container').style.display = "none";
    });
}

function closeHudColorPicker() {
    fetch(`https://${GetParentResourceName()}/closeHudColorPicker`, {
        method: "POST"
    }).then(() => {
        document.querySelector('.color-picker-container').style.display = "none";
    });
}

  </script>
</body>
  </html>