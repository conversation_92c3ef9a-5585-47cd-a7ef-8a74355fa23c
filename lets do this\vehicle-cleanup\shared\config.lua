Config = {}

-- Time in seconds before a vehicle is deleted when player is not inside
Config.DeleteTime = 180 -- 3 minutes (180 seconds)

-- Check interval in milliseconds
Config.CheckInterval = 1000 -- Check every second

-- Debug mode
Config.Debug = false

-- Exclude certain vehicle classes from deletion (optional)
Config.ExcludedClasses = {
    -- 13, -- Cycles (bicycles)
    -- 14, -- Boats
    -- 15, -- Helicopters
    -- 16, -- Planes
}

-- Notification settings
Config.Notifications = {
    enabled = true,
    warningTime = 30, -- Warn player when 30 seconds left
    warningMessage = "Your vehicle will be deleted in 30 seconds if you don't return to it!",
    deletedMessage = "Your vehicle has been deleted due to inactivity."
}
