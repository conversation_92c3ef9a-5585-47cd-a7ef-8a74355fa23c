# Leaderboard - Online Players Only Mode

## What Changed

The leaderboard now **ONLY shows players who are currently online** on the server.

### Before:
- Showed all players from database (online and offline)
- Mixed historical data with current players

### After:
- Only displays players currently connected to the server
- When a player disconnects, they disappear from the leaderboard
- When they reconnect, they reappear with their stats

## How It Works

1. **Player Joins Server**
   - Their stats are loaded from database
   - They appear on the leaderboard

2. **Player Leaves Server**
   - They disappear from the leaderboard
   - Their stats remain saved in database

3. **Press F7**
   - Shows only currently online players
   - Fresh data every time

## Benefits

- **Cleaner Display**: Only see who's actually playing
- **Real Competition**: Compare yourself to active players
- **Performance**: Faster loading (fewer players to display)
- **Privacy**: Offline players aren't shown

## Technical Details

The change was simple:
- Removed the `getTopPlayersFromDatabase()` function usage
- Now uses only `getOnlinePlayersData()` 
- No offline player merging

## Reverting (If Needed)

If you want to show all players again (online + offline), let me know and I can revert the change.

## Testing

1. Have multiple players online
2. Press F7 - see all online players
3. Have a player disconnect
4. Press F7 again - that player is gone
5. Player reconnects
6. Press F7 - they're back!
