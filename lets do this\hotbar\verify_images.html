<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Verify Hotbar Images</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #1a1a1a;
            color: white;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            background-color: #2a2a2a;
            border-radius: 8px;
        }
        .image-test {
            display: inline-block;
            margin: 10px;
            text-align: center;
        }
        .image-test img {
            width: 64px;
            height: 64px;
            object-fit: contain;
            background-color: #3a3a3a;
            padding: 8px;
            border-radius: 4px;
            display: block;
        }
        .success { color: #4CAF50; }
        .error { color: #f44336; }
        .path-info {
            font-size: 11px;
            margin-top: 5px;
            color: #888;
        }
    </style>
</head>
<body>
    <h1>Hotbar Image Verification</h1>
    
    <div class="test-section">
        <h2>Direct Path Test (from HTML folder)</h2>
        <div id="direct-test"></div>
    </div>
    
    <div class="test-section">
        <h2>Instructions to Fix Images:</h2>
        <ol>
            <li>Restart the hotbar resource: <code>/restart hotbar</code></li>
            <li>Make sure hotbar is visible: Press <code>H</code></li>
            <li>Test with command: <code>/testallimages</code></li>
            <li>Check F8 console for any error messages</li>
            <li>If images still don't show, try: <code>/testdirectimages</code></li>
        </ol>
    </div>

    <script>
        // Test images with direct paths
        const testImages = [
            'weapon_pistol50.png',
            'weapon_assaultrifle.png',
            'weapon_smg.png',
            'weapon_combatmg.png',
            'weapon_microsmg.png'
        ];
        
        const container = document.getElementById('direct-test');
        
        testImages.forEach(imageName => {
            const div = document.createElement('div');
            div.className = 'image-test';
            
            const img = document.createElement('img');
            img.src = 'html/images/' + imageName;
            
            const status = document.createElement('div');
            status.className = 'path-info';
            
            img.onload = function() {
                status.innerHTML = `<span class="success">✓ Loaded</span><br>${imageName}`;
            };
            
            img.onerror = function() {
                status.innerHTML = `<span class="error">✗ Failed</span><br>${imageName}`;
                // Try alternative path
                this.src = 'images/' + imageName;
            };
            
            div.appendChild(img);
            div.appendChild(status);
            container.appendChild(div);
        });
        
        // Log current location for debugging
        console.log('Current page location:', window.location.href);
        console.log('Expected image path example:', window.location.href.replace('verify_images.html', 'html/images/weapon_pistol50.png'));
    </script>
</body>
</html>
