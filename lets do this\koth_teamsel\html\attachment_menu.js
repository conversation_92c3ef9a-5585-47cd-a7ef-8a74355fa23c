window.addEventListener('message', function(event) {
    const data = event.data;
    
    if (data.action === 'showMenu' && data.type === 'attachments') {
        showAttachmentMenu(data);
    } else if (data.action === 'hideAll') {
        hideAttachmentMenu();
    }
});

function showAttachmentMenu(data) {
    const menu = document.getElementById('attachment-menu');
    const weaponName = document.getElementById('weapon-name');
    const attachmentList = document.getElementById('attachment-list');
    
    // Set weapon name
    weaponName.textContent = data.weaponName || 'Unknown Weapon';
    
    // Clear previous attachments
    attachmentList.innerHTML = '';
    
    // Add attachments
    if (data.items && data.items.length > 0) {
        data.items.forEach(attachment => {
            const attachmentDiv = document.createElement('div');
            attachmentDiv.className = 'attachment-item';
            
            // Create image element with better error handling
            const img = document.createElement('img');
            img.src = attachment.image;
            img.alt = attachment.name;
            img.onerror = function() {
                console.error('Failed to load attachment image:', attachment.image);
                // Try fallback paths
                if (this.src.includes('nui://')) {
                    // Try relative path
                    this.src = `../images of guns/${attachment.image.split('/').pop()}`;
                } else if (!this.src.includes('attachment_scope.png')) {
                    // Final fallback
                    this.src = '../images of guns/attachment_scope.png';
                }
            };
            
            attachmentDiv.innerHTML = '';
            attachmentDiv.appendChild(img);
            attachmentDiv.innerHTML += `
                <div class="name">${attachment.name}</div>
                <div class="price">$${attachment.price}</div>
            `;
            
            attachmentDiv.addEventListener('click', function() {
                purchaseAttachment(attachment);
            });
            
            attachmentList.appendChild(attachmentDiv);
        });
    } else {
        attachmentList.innerHTML = '<div style="text-align: center; color: #ccc;">No attachments available</div>';
    }
    
    // Show menu
    menu.classList.remove('hidden');
}

function hideAttachmentMenu() {
    const menu = document.getElementById('attachment-menu');
    menu.classList.add('hidden');
}

function purchaseAttachment(attachment) {
    // Send purchase request to client
    fetch(`https://${GetParentResourceName()}/purchaseAttachment`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json; charset=UTF-8',
        },
        body: JSON.stringify({
            name: attachment.name,
            component: attachment.component,
            price: attachment.price
        })
    }).then(resp => resp.json()).then(resp => {
        console.log('Attachment purchase response:', resp);
    });
    
    // Hide menu after purchase
    hideAttachmentMenu();
}

// Close button functionality
document.getElementById('close-btn').addEventListener('click', function() {
    fetch(`https://${GetParentResourceName()}/closeMenu`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json; charset=UTF-8',
        },
        body: JSON.stringify({})
    });
    hideAttachmentMenu();
});

// Helper function to get parent resource name
function GetParentResourceName() {
    return window.location.hostname;
}

// Add hidden class to CSS if not already present
const style = document.createElement('style');
style.textContent = `
    .hidden {
        display: none !important;
    }
`;
document.head.appendChild(style);
