-- Script to assign teams to players who don't have one
-- This will help populate the leaderboard

-- First, let's see how many players don't have teams
SELECT COUNT(*) as players_without_teams 
FROM koth_players 
WHERE current_team IS NULL OR current_team = '';

-- Assign teams to players without teams (distributing evenly)
UPDATE koth_players 
SET current_team = CASE 
    WHEN (id % 3) = 0 THEN 'red'
    WHEN (id % 3) = 1 THEN 'blue'
    ELSE 'green'
END
WHERE current_team IS NULL OR current_team = '';

-- Verify team distribution
SELECT current_team, COUNT(*) as player_count 
FROM koth_players 
GROUP BY current_team;

-- Show some sample data to verify
SELECT id, txid, player_name, current_team, kills, deaths, level 
FROM koth_players 
LIMIT 10;
