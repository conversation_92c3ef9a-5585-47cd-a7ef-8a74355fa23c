# Hotbar Integration with KOTH Weapon Shop

## Overview
The hotbar system has been successfully integrated with the KOTH weapon shop. When a player purchases a weapon from the weapon shop, it will automatically be added to slot 1 of their hotbar.

## How It Works

### 1. Resource Load Order
The `hotbar` resource must be loaded before `koth_teamsel` in server.cfg:
```cfg
ensure hotbar
ensure koth_teamsel
```

### 2. Integration Point
The integration happens in `koth_teamsel/client.lua` in the `koth:giveWeapon` event handler:

```lua
-- When a weapon is purchased and given to the player
RegisterNetEvent('koth:giveWeapon', function(weapon, classId, price)
    -- ... existing weapon giving code ...
    
    -- Add weapon to hotbar slot 1
    if exports['hotbar'] then
        exports['hotbar']:SetHotbarItem(1, weaponDisplayName, weaponIcon, 250, weaponHash)
    end
end)
```

### 3. Features
- **Automatic Addition**: Weapons are automatically added to hotbar slot 1 when purchased
- **Weapon Icons**: The integration maps weapon names to their corresponding images
- **Ammo Count**: Shows 250 rounds (the amount given with the weapon)
- **Weapon Switching**: Players can press `1` to quickly equip/unequip the weapon

## Testing

### Commands for Testing
1. **Buy a weapon from the shop**: 
   - Go to a team spawn area
   - Press E on the Classes Menu NPC
   - Select a class and weapon
   - The weapon should appear in hotbar slot 1

2. **Manual hotbar commands** (for debugging):
   - `/testhotbar` - Test if hotbar is working
   - `/clearhotbar` - Clear all hotbar slots
   - `/refreshhotbar` - Refresh hotbar display

### Expected Behavior
1. Player buys a weapon from the shop
2. Weapon is given to the player
3. Weapon automatically appears in hotbar slot 1
4. Player can press `1` to equip/unequip the weapon
5. Hotbar shows weapon icon and ammo count

## Weapon Image Mapping
The integration includes a mapping of weapon names to their image files. If a weapon doesn't have a specific image, it defaults to `weapon_pistol50.png`.

### Currently Mapped Weapons:
- Pistols (Pistol, Combat Pistol, AP Pistol, Pistol .50)
- SMGs (Micro SMG, SMG, Assault SMG, Combat PDW, etc.)
- Rifles (Assault Rifle, Bullpup Rifle, Special Carbine, etc.)
- Melee weapons (Stone Hatchet, Crowbar, Hatchet)

## Troubleshooting

### Weapon not appearing in hotbar
1. Check if hotbar resource is running: `ensure hotbar`
2. Check console for errors
3. Make sure hotbar is loaded before koth_teamsel

### Wrong weapon image
1. Check if the weapon has a mapped image in the weaponImageMap
2. Add new mappings as needed in the integration code

### Hotbar not visible
1. Press `H` to toggle hotbar visibility
2. Check if hotbar UI is properly loaded

## Future Enhancements
- Add support for multiple weapons in different slots
- Save hotbar state between sessions
- Add drag-and-drop functionality
- Support for items other than weapons
