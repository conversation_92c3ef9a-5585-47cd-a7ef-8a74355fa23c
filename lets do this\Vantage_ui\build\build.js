// HUD COLOUR CHANGER  //
window.addEventListener("message", function(event) {
  if (event.data.action === "updateHudColors") {
      const colors = event.data.colors;

      document.querySelector(".player_bar .health .flowbar").style.background = colors.main;
      document.querySelector(".player_bar .health").style.background = colors.under;
      document.querySelector(".player_bar .armour .flowbar").style.background = colors.armour;
  }
});




/******/ (function(modules) { // webpackBootstrap
/******/ 	// The module cache
/******/ 	var installedModules = {};
/******/
/******/ 	// The require function
/******/ 	function __webpack_require__(moduleId) {
/******/
/******/ 		// Check if module is in cache
/******/ 		if(installedModules[moduleId]) {
/******/ 			return installedModules[moduleId].exports;
/******/ 		}
/******/ 		// Create a new module (and put it into the cache)
/******/ 		var module = installedModules[moduleId] = {
/******/ 			i: moduleId,
/******/ 			l: false,
/******/ 			exports: {}
/******/ 		};
/******/
/******/ 		// Execute the module function
/******/ 		modules[moduleId].call(module.exports, module, module.exports, __webpack_require__);
/******/
/******/ 		// Flag the module as loaded
/******/ 		module.l = true;
/******/
/******/ 		// Return the exports of the module
/******/ 		return module.exports;
/******/ 	}
/******/
/******/
/******/ 	// expose the modules object (__webpack_modules__)
/******/ 	__webpack_require__.m = modules;
/******/
/******/ 	// expose the module cache
/******/ 	__webpack_require__.c = installedModules;
/******/
/******/ 	// define getter function for harmony exports
/******/ 	__webpack_require__.d = function(exports, name, getter) {
/******/ 		if(!__webpack_require__.o(exports, name)) {
/******/ 			Object.defineProperty(exports, name, { enumerable: true, get: getter });
/******/ 		}
/******/ 	};
/******/
/******/ 	// define __esModule on exports
/******/ 	__webpack_require__.r = function(exports) {
/******/ 		if(typeof Symbol !== 'undefined' && Symbol.toStringTag) {
/******/ 			Object.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });
/******/ 		}
/******/ 		Object.defineProperty(exports, '__esModule', { value: true });
/******/ 	};
/******/
/******/ 	// create a fake namespace object
/******/ 	// mode & 1: value is a module id, require it
/******/ 	// mode & 2: merge all properties of value into the ns
/******/ 	// mode & 4: return value when already ns object
/******/ 	// mode & 8|1: behave like require
/******/ 	__webpack_require__.t = function(value, mode) {
/******/ 		if(mode & 1) value = __webpack_require__(value);
/******/ 		if(mode & 8) return value;
/******/ 		if((mode & 4) && typeof value === 'object' && value && value.__esModule) return value;
/******/ 		var ns = Object.create(null);
/******/ 		__webpack_require__.r(ns);
/******/ 		Object.defineProperty(ns, 'default', { enumerable: true, value: value });
/******/ 		if(mode & 2 && typeof value != 'string') for(var key in value) __webpack_require__.d(ns, key, function(key) { return value[key]; }.bind(null, key));
/******/ 		return ns;
/******/ 	};
/******/
/******/ 	// getDefaultExport function for compatibility with non-harmony modules
/******/ 	__webpack_require__.n = function(module) {
/******/ 		var getter = module && module.__esModule ?
/******/ 			function getDefault() { return module['default']; } :
/******/ 			function getModuleExports() { return module; };
/******/ 		__webpack_require__.d(getter, 'a', getter);
/******/ 		return getter;
/******/ 	};
/******/
/******/ 	// Object.prototype.hasOwnProperty.call
/******/ 	__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };
/******/
/******/ 	// __webpack_public_path__
/******/ 	__webpack_require__.p = "";
/******/
/******/
/******/ 	// Load entry module and return exports
/******/ 	return __webpack_require__(__webpack_require__.s = "./src/main.js");
/******/ })
/************************************************************************/
/******/ ({

/***/ "./node_modules/css-loader/dist/cjs.js!./node_modules/sass-loader/dist/cjs.js!./src/assets/main.scss":
/*!***********************************************************************************************************!*\
  !*** ./node_modules/css-loader/dist/cjs.js!./node_modules/sass-loader/dist/cjs.js!./src/assets/main.scss ***!
  \***********************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

eval("// Imports\nvar ___CSS_LOADER_API_IMPORT___ = __webpack_require__(/*! ../../node_modules/css-loader/dist/runtime/api.js */ \"./node_modules/css-loader/dist/runtime/api.js\");\nexports = ___CSS_LOADER_API_IMPORT___(false);\n// Module\nexports.push([module.i, \"svg.sized {\\n  width: 350px;\\n  height: 350px; }\\n\\nsvg.responsive {\\n  height: 100%; }\\n\\n.speaker {\\n  height: 75%;\\n  fill: #666666; }\\n  .speaker .v_2 {\\n    display: none; }\\n  .speaker .v_3 {\\n    display: none; }\\n\\n.dial {\\n  height: 350px;\\n  fill: white; }\\n\\n#player_hud {\\n  position: relative;\\n  width: 350%;\\n  height: 100%;\\n  font-size: 1.2vh;\\n  width: 120vw;\\n  height: 56.25vw;\\n  max-width: 177.77778vh;\\n  max-height: 100vh;\\n  margin: auto;\\n  text-shadow: 1px 1px 2px #000000; }\\n  @media (width: 3440px) and (height: 1440px) {\\n    #player_hud .player_bar {\\n      left: 2%; }\\n    #player_hud .voice_bar {\\n      left: 13.95%; }\\n    #player_hud .location_bar {\\n      left: 16.25%; }\\n    #player_hud .vehicle_bar {\\n      left: 16.25%; } }\\n  @media (min-width: 2560px) and (height: 1080px) {\\n    #player_hud .player_bar {\\n      left: 2%; }\\n    #player_hud .voice_bar {\\n      left: 13.95%; }\\n    #player_hud .location_bar {\\n      left: 16.25%; }\\n    #player_hud .vehicle_bar {\\n      left: 16.25%; } }\\n\\n.vehicle_bar {\\n  display: none;\\n  position: absolute;\\n  bottom: 0.5%;\\n  left: 16%;\\n  font-size: 1.5vh; }\\n  .vehicle_bar.inVehicle {\\n    display: flex;\\n    justify-content: start;\\n    align-items: center; }\\n  .vehicle_bar .speed,\\n  .vehicle_bar .fuel,\\n  .vehicle_bar .seatbelt {\\n    background-color: #1414145d;\\n    border: 1px solid #1f1f1f;\\n    font-weight: bold;\\n    border-radius: 2px;\\n    padding: 4px 8px;\\n    margin-right: 4px; }\\n  .vehicle_bar .speed .value {\\n    transition: color 2s ease; }\\n  .vehicle_bar .speed .km {\\n    display: none; }\\n  .vehicle_bar .speed .knots {\\n    display: none; }\\n  .vehicle_bar .fuel .value {\\n    transition: color 2s ease; }\\n  .vehicle_bar .fuel i {\\n    color: orange; }\\n  .vehicle_bar .seatbelt {\\n    font-size: 0.78em;\\n    border: 4px solid transparent; }\\n    .vehicle_bar .seatbelt span {\\n      color: #FFFFFF;\\n      transition: color 0.4s linear, border 1s linear; }\\n    .vehicle_bar .seatbelt:not(.active) {\\n      animation: seatbelt_border 2s linear infinite; }\\n      .vehicle_bar .seatbelt:not(.active) span {\\n        animation: seatbelt_color 2s linear infinite; }\\n    .vehicle_bar .seatbelt.active {\\n      border: 4px solid #3d85c6; }\\n      .vehicle_bar .seatbelt.active span {\\n        color: #3d85c6; }\\n  .vehicle_bar .dial path {\\n    overflow: hidden; }\\n\\n@keyframes seatbelt_color {\\n  0%,\\n  100% {\\n    color: #FFFFFF; }\\n  20%,\\n  80% {\\n    color: #FFFFFF; } }\\n\\n@keyframes seatbelt_border {\\n  0%,\\n  100% {\\n    border-color: transparent; }\\n  20%,\\n  80% {\\n    border-color: #3d85c6; } }\\n\\n.location_bar {\\n  position: absolute;\\n  bottom: 3.5%;\\n  left: 16%;\\n  width: 17%;\\n  height: 2.8vh;\\n  display: flex;\\n  justify-content: flex-start;\\n  align-items: center;\\n  font-weight: bold;\\n  font-size: 1.2vh;\\n  padding: 0 8px; }\\n  .location_bar .heading {\\n    margin-right: 8px;\\n    font-size: 1.3em; }\\n  .location_bar i {\\n    margin-right: 6px; }\\n  .location_bar .location {\\n    display: flex;\\n    align-items: center;\\n    justify-content: start; }\\n  .location_bar #aop {\\n    color: #4863A0; }\\n  .location_bar #aop.city {\\n    color: #4863A0; }\\n  .location_bar #aop.county {\\n    color: orange; }\\n  .location_bar#location_bar_1 {\\n    bottom: 3.6%;\\n    display: none; }\\n  .location_bar#location_bar_1.inVehicle {\\n    bottom: 6.6%;\\n    display: flex; }\\n  .location_bar#location_bar_2 {\\n    bottom: 0.45%; }\\n  .location_bar#location_bar_2.inVehicle {\\n    bottom: 3.6%; }\\n\\n.voice_bar {\\n  position: absolute;\\n  bottom: 3.6%;\\n  left: 13.45%;\\n  width: 2%;\\n  height: 2.8vh;\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  font-weight: bold;\\n  border-radius: 4px; }\\n  .voice_bar.active svg {\\n    animation: voice_active 2s ease-in-out infinite; }\\n\\n@keyframes voice_active {\\n  0%,\\n  100% {\\n    fill: #666666; }\\n  20%,\\n  80% {\\n    fill: #00ff00e8; } }\\n\\n.status_bar {\\n  position: absolute;\\n  bottom: 0.4%;\\n  left: 1.45%;\\n  width: 14%;\\n  height: 2.8vh;\\n  display: flex;\\n  justify-content: flex-start;\\n  align-items: center;\\n  font-weight: bold;\\n  font-size: 2.2em;\\n  border-radius: 4px; }\\n  .status_bar.inVehicle {\\n    bottom: 400px;\\n    left: 40px; }\\n  .status_bar div {\\n    border-radius: 50%;\\n    border: 1px solid #1f1f1f;\\n    margin-right: 8px; }\\n    .status_bar div.stamina {\\n      padding: 4px 10px; }\\n    .status_bar div.water {\\n      padding: 4px 12px; }\\n    .status_bar div.food {\\n      padding: 4px 7px; }\\n\\n.player_bar {\\n  position: absolute;\\n  bottom: 0.4%;\\n  left: 1.45%;\\n  width: 14%;\\n  height: 2.8vh;\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  background-color: #1414145d;\\n  font-weight: bold;\\n  border: 1px solid #1f1f1f;\\n  border-radius: 4px;\\n  overflow: hidden; }\\n  .player_bar .health,\\n  .player_bar .armour,\\n  .player_bar .unconscious {\\n    position: relative;\\n    flex: 1;\\n    display: flex;\\n    justify-content: center;\\n    align-items: center;\\n    height: 100%;\\n    transition: flex-basis 2s ease; }\\n    .player_bar .health .flowbar,\\n    .player_bar .armour .flowbar,\\n    .player_bar .unconscious .flowbar {\\n      position: absolute;\\n      left: 0;\\n      width: 100%;\\n      height: 100%;\\n      transition: width 2s ease; }\\n    .player_bar .health .text,\\n    .player_bar .armour .text,\\n    .player_bar .unconscious .text {\\n      position: relative;\\n      display: flex;\\n      justify-content: center;\\n      align-items: center;\\n      padding: 4px;\\n      margin: 8px;\\n      border-radius: 4px; }\\n      .player_bar .health .text i,\\n      .player_bar .armour .text i,\\n      .player_bar .unconscious .text i {\\n        margin-right: 4px; }\\n  .player_bar .health {\\n    background: #0c394b; }\\n    .player_bar .health .flowbar {\\n      background:#203e81 ; }\\n  .player_bar .armour {\\n    background: #20353f;\\n    border-left: 1px solid #1f1f1f; }\\n    .player_bar .armour .flowbar {\\n      background: #0076ad; }\\n  .player_bar .unconscious {\\n    display: none;\\n    animation: unconscious 3s ease-in-out infinite; }\\n\\n@keyframes unconscious {\\n  0%,\\n  100% {\\n    background: #2c2c2c; }\\n  50% {\\n    background: rgba(163, 0, 0, 0.911); } }\\n\\n#player_notifications {\\n  margin: 0 8px;\\n  position: absolute;\\n  width: 20%;\\n  min-width: 300px;\\n  transition: height 1s ease;\\n  overflow-y: hidden; }\\n  #player_notifications.top-right {\\n    top: 5%;\\n    right: 0;\\n    max-height: 60%; }\\n  #player_notifications.bottom-right {\\n    bottom: 2%;\\n    right: 0;\\n    max-height: 30%; }\\n  #player_notifications.top-left {\\n    top: 5%;\\n    left: 0;\\n    max-height: 60%; }\\n  #player_notifications i {\\n    margin-right: 6px; }\\n  #player_notifications .notification {\\n    font-family: Arial, Helvetica, sans-serif;\\n    background-color: #131313d2;\\n    color: #ffffff;\\n    border-radius: 4px;\\n    overflow: hidden;\\n    /* Size */\\n    max-width: 100%;\\n    /* Flex Settings */\\n    display: flex;\\n    flex-direction: column;\\n    margin-bottom: 12px; }\\n  #player_notifications.low {\\n    border-color: #9dff00; }\\n  #player_notifications.medium {\\n    border-color: #ffd000; }\\n  #player_notifications.high {\\n    border-color: #b90000; }\\n  #player_notifications.high .header {\\n    animation: changecolor 2s linear infinite; }\\n\\n@keyframes changecolor {\\n  0%,\\n  100% {\\n    background: #b9000055; }\\n  50% {\\n    background: #0044c255; } }\\n  #player_notifications .p {\\n    margin: 0; }\\n  #player_notifications .pill {\\n    border-radius: 4px;\\n    padding: 4px 8px;\\n    font-size: 0.9em;\\n    text-shadow: 2px 2px 4px #000000;\\n    box-shadow: 2px 2px 6px #000000; }\\n  #player_notifications .header,\\n  #player_notifications .body {\\n    padding: 8px 8px 6px 8px; }\\n  #player_notifications .header {\\n    background-color: #0044c255;\\n    font-weight: bold;\\n    text-shadow: 2px 2px 4px #000000;\\n    box-shadow: 0 4px 8px #000000;\\n    /* Flex Settings */\\n    display: flex;\\n    justify-content: space-between;\\n    align-items: center; }\\n  #player_notifications .header .callsign {\\n    background-color: #2c2c2c; }\\n  #player_notifications .header .code {\\n    background-color: #b90000; }\\n  #player_notifications .body {\\n    font-size: 1.0em; }\\n    #player_notifications .body * {\\n      margin: 1px 2px; }\\n    #player_notifications .body i {\\n      margin-right: 4px; }\\n\\nbody {\\n  font-family: Arial, Helvetica, sans-serif;\\n  color: #ffffff;\\n  margin: 0; }\\n\", \"\"]);\n// Exports\nmodule.exports = exports;\n\n\n//# sourceURL=webpack:///./src/assets/main.scss?./node_modules/css-loader/dist/cjs.js!./node_modules/sass-loader/dist/cjs.js");

/***/ }),

/***/ "./node_modules/css-loader/dist/runtime/api.js":
/*!*****************************************************!*\
  !*** ./node_modules/css-loader/dist/runtime/api.js ***!
  \*****************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
eval("\n\n/*\n  MIT License http://www.opensource.org/licenses/mit-license.php\n  Author Tobias Koppers @sokra\n*/\n// css base code, injected by the css-loader\n// eslint-disable-next-line func-names\nmodule.exports = function (useSourceMap) {\n  var list = []; // return the list of modules as css string\n\n  list.toString = function toString() {\n    return this.map(function (item) {\n      var content = cssWithMappingToString(item, useSourceMap);\n\n      if (item[2]) {\n        return \"@media \".concat(item[2], \" {\").concat(content, \"}\");\n      }\n\n      return content;\n    }).join('');\n  }; // import a list of modules into the list\n  // eslint-disable-next-line func-names\n\n\n  list.i = function (modules, mediaQuery, dedupe) {\n    if (typeof modules === 'string') {\n      // eslint-disable-next-line no-param-reassign\n      modules = [[null, modules, '']];\n    }\n\n    var alreadyImportedModules = {};\n\n    if (dedupe) {\n      for (var i = 0; i < this.length; i++) {\n        // eslint-disable-next-line prefer-destructuring\n        var id = this[i][0];\n\n        if (id != null) {\n          alreadyImportedModules[id] = true;\n        }\n      }\n    }\n\n    for (var _i = 0; _i < modules.length; _i++) {\n      var item = [].concat(modules[_i]);\n\n      if (dedupe && alreadyImportedModules[item[0]]) {\n        // eslint-disable-next-line no-continue\n        continue;\n      }\n\n      if (mediaQuery) {\n        if (!item[2]) {\n          item[2] = mediaQuery;\n        } else {\n          item[2] = \"\".concat(mediaQuery, \" and \").concat(item[2]);\n        }\n      }\n\n      list.push(item);\n    }\n  };\n\n  return list;\n};\n\nfunction cssWithMappingToString(item, useSourceMap) {\n  var content = item[1] || ''; // eslint-disable-next-line prefer-destructuring\n\n  var cssMapping = item[3];\n\n  if (!cssMapping) {\n    return content;\n  }\n\n  if (useSourceMap && typeof btoa === 'function') {\n    var sourceMapping = toComment(cssMapping);\n    var sourceURLs = cssMapping.sources.map(function (source) {\n      return \"/*# sourceURL=\".concat(cssMapping.sourceRoot || '').concat(source, \" */\");\n    });\n    return [content].concat(sourceURLs).concat([sourceMapping]).join('\\n');\n  }\n\n  return [content].join('\\n');\n} // Adapted from convert-source-map (MIT)\n\n\nfunction toComment(sourceMap) {\n  // eslint-disable-next-line no-undef\n  var base64 = btoa(unescape(encodeURIComponent(JSON.stringify(sourceMap))));\n  var data = \"sourceMappingURL=data:application/json;charset=utf-8;base64,\".concat(base64);\n  return \"/*# \".concat(data, \" */\");\n}\n\n//# sourceURL=webpack:///./node_modules/css-loader/dist/runtime/api.js?");

/***/ }),

/***/ "./node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js":
/*!****************************************************************************!*\
  !*** ./node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js ***!
  \****************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
eval("\n\nvar isOldIE = function isOldIE() {\n  var memo;\n  return function memorize() {\n    if (typeof memo === 'undefined') {\n      // Test for IE <= 9 as proposed by Browserhacks\n      // @see http://browserhacks.com/#hack-e71d8692f65334173fee715c222cb805\n      // Tests for existence of standard globals is to allow style-loader\n      // to operate correctly into non-standard environments\n      // @see https://github.com/webpack-contrib/style-loader/issues/177\n      memo = Boolean(window && document && document.all && !window.atob);\n    }\n\n    return memo;\n  };\n}();\n\nvar getTarget = function getTarget() {\n  var memo = {};\n  return function memorize(target) {\n    if (typeof memo[target] === 'undefined') {\n      var styleTarget = document.querySelector(target); // Special case to return head of iframe instead of iframe itself\n\n      if (window.HTMLIFrameElement && styleTarget instanceof window.HTMLIFrameElement) {\n        try {\n          // This will throw an exception if access to iframe is blocked\n          // due to cross-origin restrictions\n          styleTarget = styleTarget.contentDocument.head;\n        } catch (e) {\n          // istanbul ignore next\n          styleTarget = null;\n        }\n      }\n\n      memo[target] = styleTarget;\n    }\n\n    return memo[target];\n  };\n}();\n\nvar stylesInDom = [];\n\nfunction getIndexByIdentifier(identifier) {\n  var result = -1;\n\n  for (var i = 0; i < stylesInDom.length; i++) {\n    if (stylesInDom[i].identifier === identifier) {\n      result = i;\n      break;\n    }\n  }\n\n  return result;\n}\n\nfunction modulesToDom(list, options) {\n  var idCountMap = {};\n  var identifiers = [];\n\n  for (var i = 0; i < list.length; i++) {\n    var item = list[i];\n    var id = options.base ? item[0] + options.base : item[0];\n    var count = idCountMap[id] || 0;\n    var identifier = \"\".concat(id, \" \").concat(count);\n    idCountMap[id] = count + 1;\n    var index = getIndexByIdentifier(identifier);\n    var obj = {\n      css: item[1],\n      media: item[2],\n      sourceMap: item[3]\n    };\n\n    if (index !== -1) {\n      stylesInDom[index].references++;\n      stylesInDom[index].updater(obj);\n    } else {\n      stylesInDom.push({\n        identifier: identifier,\n        updater: addStyle(obj, options),\n        references: 1\n      });\n    }\n\n    identifiers.push(identifier);\n  }\n\n  return identifiers;\n}\n\nfunction insertStyleElement(options) {\n  var style = document.createElement('style');\n  var attributes = options.attributes || {};\n\n  if (typeof attributes.nonce === 'undefined') {\n    var nonce =  true ? __webpack_require__.nc : undefined;\n\n    if (nonce) {\n      attributes.nonce = nonce;\n    }\n  }\n\n  Object.keys(attributes).forEach(function (key) {\n    style.setAttribute(key, attributes[key]);\n  });\n\n  if (typeof options.insert === 'function') {\n    options.insert(style);\n  } else {\n    var target = getTarget(options.insert || 'head');\n\n    if (!target) {\n      throw new Error(\"Couldn't find a style target. This probably means that the value for the 'insert' parameter is invalid.\");\n    }\n\n    target.appendChild(style);\n  }\n\n  return style;\n}\n\nfunction removeStyleElement(style) {\n  // istanbul ignore if\n  if (style.parentNode === null) {\n    return false;\n  }\n\n  style.parentNode.removeChild(style);\n}\n/* istanbul ignore next  */\n\n\nvar replaceText = function replaceText() {\n  var textStore = [];\n  return function replace(index, replacement) {\n    textStore[index] = replacement;\n    return textStore.filter(Boolean).join('\\n');\n  };\n}();\n\nfunction applyToSingletonTag(style, index, remove, obj) {\n  var css = remove ? '' : obj.media ? \"@media \".concat(obj.media, \" {\").concat(obj.css, \"}\") : obj.css; // For old IE\n\n  /* istanbul ignore if  */\n\n  if (style.styleSheet) {\n    style.styleSheet.cssText = replaceText(index, css);\n  } else {\n    var cssNode = document.createTextNode(css);\n    var childNodes = style.childNodes;\n\n    if (childNodes[index]) {\n      style.removeChild(childNodes[index]);\n    }\n\n    if (childNodes.length) {\n      style.insertBefore(cssNode, childNodes[index]);\n    } else {\n      style.appendChild(cssNode);\n    }\n  }\n}\n\nfunction applyToTag(style, options, obj) {\n  var css = obj.css;\n  var media = obj.media;\n  var sourceMap = obj.sourceMap;\n\n  if (media) {\n    style.setAttribute('media', media);\n  } else {\n    style.removeAttribute('media');\n  }\n\n  if (sourceMap && btoa) {\n    css += \"\\n/*# sourceMappingURL=data:application/json;base64,\".concat(btoa(unescape(encodeURIComponent(JSON.stringify(sourceMap)))), \" */\");\n  } // For old IE\n\n  /* istanbul ignore if  */\n\n\n  if (style.styleSheet) {\n    style.styleSheet.cssText = css;\n  } else {\n    while (style.firstChild) {\n      style.removeChild(style.firstChild);\n    }\n\n    style.appendChild(document.createTextNode(css));\n  }\n}\n\nvar singleton = null;\nvar singletonCounter = 0;\n\nfunction addStyle(obj, options) {\n  var style;\n  var update;\n  var remove;\n\n  if (options.singleton) {\n    var styleIndex = singletonCounter++;\n    style = singleton || (singleton = insertStyleElement(options));\n    update = applyToSingletonTag.bind(null, style, styleIndex, false);\n    remove = applyToSingletonTag.bind(null, style, styleIndex, true);\n  } else {\n    style = insertStyleElement(options);\n    update = applyToTag.bind(null, style, options);\n\n    remove = function remove() {\n      removeStyleElement(style);\n    };\n  }\n\n  update(obj);\n  return function updateStyle(newObj) {\n    if (newObj) {\n      if (newObj.css === obj.css && newObj.media === obj.media && newObj.sourceMap === obj.sourceMap) {\n        return;\n      }\n\n      update(obj = newObj);\n    } else {\n      remove();\n    }\n  };\n}\n\nmodule.exports = function (list, options) {\n  options = options || {}; // Force single-tag solution on IE6-9, which has a hard limit on the # of <style>\n  // tags it will allow on a page\n\n  if (!options.singleton && typeof options.singleton !== 'boolean') {\n    options.singleton = isOldIE();\n  }\n\n  list = list || [];\n  var lastIdentifiers = modulesToDom(list, options);\n  return function update(newList) {\n    newList = newList || [];\n\n    if (Object.prototype.toString.call(newList) !== '[object Array]') {\n      return;\n    }\n\n    for (var i = 0; i < lastIdentifiers.length; i++) {\n      var identifier = lastIdentifiers[i];\n      var index = getIndexByIdentifier(identifier);\n      stylesInDom[index].references--;\n    }\n\n    var newLastIdentifiers = modulesToDom(newList, options);\n\n    for (var _i = 0; _i < lastIdentifiers.length; _i++) {\n      var _identifier = lastIdentifiers[_i];\n\n      var _index = getIndexByIdentifier(_identifier);\n\n      if (stylesInDom[_index].references === 0) {\n        stylesInDom[_index].updater();\n\n        stylesInDom.splice(_index, 1);\n      }\n    }\n\n    lastIdentifiers = newLastIdentifiers;\n  };\n};\n\n//# sourceURL=webpack:///./node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js?");

/***/ }),

/***/ "./src/assets/main.scss":
/*!******************************!*\
  !*** ./src/assets/main.scss ***!
  \******************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

eval("var api = __webpack_require__(/*! ../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js */ \"./node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = __webpack_require__(/*! !../../node_modules/css-loader/dist/cjs.js!../../node_modules/sass-loader/dist/cjs.js!./main.scss */ \"./node_modules/css-loader/dist/cjs.js!./node_modules/sass-loader/dist/cjs.js!./src/assets/main.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.i, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = \"head\";\noptions.singleton = false;\n\nvar update = api(content, options);\n\nvar exported = content.locals ? content.locals : {};\n\n\n\nmodule.exports = exported;\n\n//# sourceURL=webpack:///./src/assets/main.scss?");

/***/ }),

/***/ "./src/classes/Notification.js":
/*!*************************************!*\
  !*** ./src/classes/Notification.js ***!
  \*************************************/
/*! exports provided: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, \"default\", function() { return Notification; });\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nfunction _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } }\n\nfunction _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); return Constructor; }\n\nvar Notification = /*#__PURE__*/function () {\n  function Notification(type, title, data, options, overrides) {\n    _classCallCheck(this, Notification);\n\n    this.type = type || {\n      // Notification Key\n      key: null,\n      // Changes Animation\n      priority: null,\n      // Changes Format\n      job: null,\n      // Default\n      \"default\": true\n    };\n    this.title = title;\n    this.data = data || {\n      // Job Related\n      callsign: null,\n      code: null,\n      // Location\n      direction: null,\n      location: null,\n      // Custom Message\n      messageIcon: null,\n      message: null\n    };\n    this.options = {\n      position: options.position || \"top-right\",\n      timeout: options.timeout || 1000\n    };\n    this.overrides = overrides || {\n      // Header\n      headerBackground: null,\n      headerTextColor: null,\n      // Body\n      bodyBackground: null,\n      bodyTextColor: null\n    };\n  }\n\n  _createClass(Notification, [{\n    key: \"create\",\n    value: function create() {// console.log(\"Created Notification\");\n    }\n  }, {\n    key: \"delete\",\n    value: function _delete() {// console.log(\"Deleted Notification\");\n    }\n  }]);\n\n  return Notification;\n}();\n\n\n\n//# sourceURL=webpack:///./src/classes/Notification.js?");

/***/ }),

/***/ "./src/classes/Player.js":
/*!*******************************!*\
  !*** ./src/classes/Player.js ***!
  \*******************************/
/*! exports provided: createPlayer */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, \"createPlayer\", function() { return createPlayer; });\n/* harmony import */ var _components_playerHud__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../components/playerHud */ \"./src/components/playerHud.js\");\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nfunction _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } }\n\nfunction _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); return Constructor; }\n\n\n\nvar Player = /*#__PURE__*/function () {\n  function Player(health, armour, voice, location, vehicle) {\n    _classCallCheck(this, Player);\n\n    this.health = health;\n    this.armour = armour;\n    this.voice = voice;\n    this.location = location;\n    this.vehicle = vehicle;\n  }\n\n  _createClass(Player, [{\n    key: \"refresh\",\n    value: function refresh() {\n      Object(_components_playerHud__WEBPACK_IMPORTED_MODULE_0__[\"playerRefresh\"])(this);\n    }\n  }]);\n\n  return Player;\n}();\n\nfunction createPlayer(data) {\n  // Create Player\n  var ply = new Player(data.health, data.armour, data.voice, data.location, data.vehicle); // Refresh Player\n\n  ply.refresh();\n}\n\n//# sourceURL=webpack:///./src/classes/Player.js?");

/***/ }),

/***/ "./src/components/playerHud.js":
/*!*************************************!*\
  !*** ./src/components/playerHud.js ***!
  \*************************************/
/*! exports provided: playerRefresh, player_hideHud */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, \"playerRefresh\", function() { return playerRefresh; });\n/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, \"player_hideHud\", function() { return player_hideHud; });\n/* harmony import */ var jquery__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! jquery */ \"jquery\");\n/* harmony import */ var jquery__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(jquery__WEBPACK_IMPORTED_MODULE_0__);\n\nfunction playerRefresh(player) {\n refreshLocation(player.location);\n refreshVoice(player.voice);\n refreshVehicle(player.vehicle); // Flexbox Disables\n\n var armour = refreshArmour(player.armour);\n var health = refreshHealth(player.health, armour);\n checkPlayer(health, armour);\n}\nfunction player_hideHud(toggle) {\n if (toggle) {\n jquery__WEBPACK_IMPORTED_MODULE_0___default()(\"#player_hud\").fadeOut();\n } else {\n jquery__WEBPACK_IMPORTED_MODULE_0___default()(\"#player_hud\").fadeIn();\n }\n}\n\nfunction getHealthLabel(amount) {\n if (amount < 1) {\n return \"1%\";\n } else if (amount <= 2) {\n return \"2%\";\n } else if (amount <= 3) {\n return \"3%\";\n } else if (amount <= 4) {\n return \"4%\";\n } else if (amount <= 5) {\n return \"5%\";\n } else if (amount <= 6) {\n return \"6%\";\n } else if (amount <= 7) {\n return \"7%\";\n } else if (amount <= 8) {\n return \"8%\";\n } else if (amount <= 9) {\n return \"9%\";\n } else if (amount <= 10) {\n return \"10%\";\n } else if (amount <= 11) {\n return \"11%\";\n } else if (amount <= 12) {\n return \"12%\";\n } else if (amount <= 13) {\n return \"13%\";\n } else if (amount <= 14) {\n return \"14%\";\n } else if (amount <= 15) {\n return \"15%\";\n } else if (amount <= 16) {\n return \"16%\";\n } else if (amount <= 17) {\n return \"17%\";\n } else if(amount <= 18) {\n return \"18%\";\n } else if (amount <= 19) {\n return \"19%\";\n } else if (amount <= 20) {\n return \"20%\";\n } else if (amount <= 21) {\n return \"21%\";\n } else if(amount <= 22) {\n return \"22%\";\n } else if (amount <= 23) {\n return \"23%\";\n } else if(amount <= 24) {\n return \"24%\";\n } else if (amount <= 25) {\n return \"25%\";\n } else if (amount <= 26) {\n return \"26%\";\n } else if (amount <= 27) {\n return \"27%\";\n } else if (amount <= 28) {\n return \"28%\";\n } else if (amount <= 29) {\n return \"29%\";\n } else if (amount <= 30) {\n return \"30%\";\n } else if (amount <= 31) {\n return \"31%\";\n } else if (amount <= 32) {\n return \"32%\";\n } else if (amount <= 33) {\n return \"33%\";\n } else if (amount <= 34) {\n return \"34%\";\n } else if (amount <= 35) {\n return \"35%\";\n } else if(amount <= 36) {\n return \"36%\";\n } else if (amount <= 37) {\n return \"37%\";\n } else if (amount <= 38) {\n return \"38%\";\n } else if (amount <= 39) {\n return \"39%\";\n } else if (amount <= 40) {\n return \"40%\";\n } else if (amount <= 41) {\n return \"41%\";\n } else if (amount <= 42) {\n return \"42%\";\n } else if (amount <= 43) {\n return \"43%\";\n } else if(amount <= 44) {\n return \"44%\";\n } else if (amount <= 45) {\n return \"45%\";\n } else if (amount <= 46) {\n return \"46%\";\n } else if (amount <= 47) {\n return \"47%\";\n } else if(amount <= 48) {\n return \"48%\";\n } else if (amount <= 49) {\n return \"49%\";\n } else if(amount <= 50) {\n return \"50%\";\n } else if (amount <= 51) {\n return \"51%\";\n } else if (amount <= 52) {\n return \"52%\";\n } else if (amount <= 53) {\n return \"53%\";\n } else if (amount <= 54) {\n return \"54%\";\n } else if (amount <= 55) {\n return \"55%\";\n } else if (amount <= 56) {\n return \"56%\";\n } else if (amount <= 57) {\n return \"57%\";\n } else if(amount <= 58) {\n return \"58%\";\n } else if (amount <= 59) {\n return \"59%\";\n } else if (amount <= 60) {\n return \"60%\";\n } else if (amount <= 61) {\n return \"61%\";\n } else if(amount <= 62) {\n return \"62%\";\n } else if (amount <= 63) {\n return \"63%\";\n } else if (amount <= 64) {\n return \"64%\";\n } else if (amount <= 65) {\n return \"65%\";\n } else if (amount <= 66) {\n return \"66%\";\n } else if (amount <= 67) {\n return \"67%\";\n } else if (amount <= 68) {\n return \"68%\";\n } else if (amount <= 69) {\n return \"69%\";\n } else if(amount <= 70) {\n return \"70%\";\n } else if (amount <= 71) {\n return \"71%\";\n } else if (amount <= 72) {\n return \"72%\";\n } else if (amount <= 73) {\n return \"73%\";\n } else if(amount <= 74) {\n return \"74%\";\n } else if (amount <= 75) {\n return \"75%\";\n } else if(amount <= 76) {\n return \"76%\";\n } else if (amount <= 77) {\n return \"77%\";\n } else if (amount <= 78) {\n return \"78%\";\n } else if (amount <= 79) {\n return \"79%\";\n } else if (amount <= 80) {\n return \"80%\";\n } else if (amount <= 81) {\n return \"81%\";\n } else if (amount <= 82) {\n return \"82%\";\n } else if (amount <= 83) {\n return \"83%\";\n } else if (amount <= 84) {\n return \"84%\";\n } else if (amount <= 85) {\n return \"85%\";\n } else if (amount <= 86) {\n return \"86%\";\n } else if (amount <= 87) {\n return \"87%\";\n } else if(amount <= 88) {\n return \"88%\";\n } else if (amount <= 89) {\n return \"89%\";\n } else if (amount <= 90) {\n return \"90%\";\n } else if (amount <= 91) {\n return \"91%\";\n } else if (amount <= 92) {\n return \"92%\";\n } else if (amount <= 93) {\n return \"93%\";\n } else if (amount <= 94) {\n return \"94%\";\n } else if (amount <= 95) {\n return \"95%\";\n } else if(amount <= 96) {\n return \"96%\";\n } else if (amount <= 97) {\n return \"97%\";\n } else if (amount <= 98) {\n return \"98%\";\n } else if (amount <= 99) {\n return \"99%\";\n } else if(amount <= 100) {\n return \"100%\";\n }\n\n return \"lol\";\n}\n\nfunction refreshHealth(health, hasArmour) {\n if (health <= 0) {\n return false;\n }\n\n var elem = jquery__WEBPACK_IMPORTED_MODULE_0___default()(\".health\");\n elem.find(\".value\").text(getHealthLabel(health));\n\n if (!hasArmour) {\n elem.children(\".flowbar\").css(\"width\", health + \"%\");\n return true;\n }\n\n elem.css(\"flex-basis\", health + \"%\");\n elem.children(\".flowbar\").css(\"width\", \"100%\");\n return true;\n}\n\nfunction refreshArmour(armour) {\n if (armour <= 0) {\n return false;\n }\n\n var elem = jquery__WEBPACK_IMPORTED_MODULE_0___default()(\".armour\");\n elem.find(\".value\").text(armour + \"%\");\n elem.css(\"flex-basis\", armour + \"%\");\n return true;\n}\n\nfunction refreshLocation(location) {\n var elem = jquery__WEBPACK_IMPORTED_MODULE_0___default()(\".location_bar\");\n elem.children(\"#heading\").text(calculateHeading(location.direction));\n elem.children(\"#location_1\").text(location.data_1);\n elem.children(\"#location_2\").text(location.data_2);\n var elem2 = jquery__WEBPACK_IMPORTED_MODULE_0___default()(\"#aop\");\n\n switch (location.aop) {\n case \"city\":\n elem2.addClass(\"city\");\n elem2.removeClass(\"county\");\n elem.children(\"#aop\").text(\"CITY\");\n break;\n\n case \"county\":\n elem2.addClass(\"county\");\n elem2.removeClass(\"city\");\n elem.children(\"#aop\").text(\"COUNTY\");\n break;\n\n default:\n elem2.removeClass(\"city\");\n elem2.removeClass(\"county\");\n elem.children(\"#aop\").text(\"\");\n }\n}\n\nfunction refreshVoice(voice) {\n var elem = jquery__WEBPACK_IMPORTED_MODULE_0___default()(\".voice_bar\"); // Check if a players voice is active\n\n if (voice.active) {\n elem.addClass(\"active\");\n } else {\n elem.removeClass(\"active\");\n } // Check the players voice level\n\n\n switch (voice.level) {\n case 1:\n elem.find(\".v_2\").css(\"display\", \"flex\");\n elem.find(\".v_3\").css(\"display\", \"none\");\n break;\n\n case 2:\n elem.find(\".v_2\").css(\"display\", \"flex\");\n elem.find(\".v_3\").css(\"display\", \"flex\");\n break;\n\n default:\n elem.find(\".v_2\").css(\"display\", \"none\");\n elem.find(\".v_3\").css(\"display\", \"none\");\n break;\n }\n}\n\nfunction refreshVehicle(vehicle) {\n var elem = jquery__WEBPACK_IMPORTED_MODULE_0___default()(\".vehicle_bar\");\n var elem2 = jquery__WEBPACK_IMPORTED_MODULE_0___default()(\".location_bar\");\n\n if (vehicle.inVehicle) {\n elem.addClass(\"inVehicle\");\n elem2.addClass(\"inVehicle\");\n } else {\n elem.removeClass(\"inVehicle\");\n elem2.removeClass(\"inVehicle\");\n } // Vehicle Speed\n\n\n if (vehicle.speed >= 200) {\n elem.children(\".speed\").find(\".value\").css({\n color: \"#e20000\"\n });\n } else if (vehicle.speed >= 100) {\n elem.children(\".speed\").find(\".value\").css({\n color: \"#e2a600\"\n });\n } else {\n elem.children(\".speed\").find(\".value\").css({\n color: \"#ffffff\"\n });\n }\n\n elem.children(\".speed\").find(\".value\").text(vehicle.speed); // Vehicle Type\n\n if (vehicle.type === \"air\") {\n elem.children(\".speed\").find(\".km\").css(\"display\", \"none\");\n elem.children(\".speed\").find(\".knots\").css(\"display\", \"inline-block\");\n } else {\n elem.children(\".speed\").find(\".km\").css(\"display\", \"inline-block\");\n elem.children(\".speed\").find(\".knots\").css(\"display\", \"none\");\n } // Vehicle Fuel\n\n\n if (vehicle.fuel <= 20) {\n elem.children(\".fuel\").find(\".value\").css({\n color: \"#e20000\"\n });\n } else if (vehicle.fuel <= 40) {\n elem.children(\".fuel\").find(\".value\").css({\n color: \"#e2a600\"\n });\n } else {\n elem.children(\".fuel\").find(\".value\").css({\n color: \"#ffffff\"\n });\n }\n\n elem.children(\".fuel\").find(\".value\").text(vehicle.fuel); // Vehicle Seatbelt\n\n if (vehicle.seatbelt) {\n elem.children(\".seatbelt\").addClass(\"active\");\n } else {\n elem.children(\".seatbelt\").removeClass(\"active\");\n }\n}\n\nfunction checkPlayer(health, armour) {\n var h = jquery__WEBPACK_IMPORTED_MODULE_0___default()(\".health\");\n var a = jquery__WEBPACK_IMPORTED_MODULE_0___default()(\".armour\");\n var u = jquery__WEBPACK_IMPORTED_MODULE_0___default()(\".unconscious\");\n\n if (health && !armour) {\n h.css(\"display\", \"flex\");\n a.css(\"display\", \"none\");\n u.css(\"display\", \"none\");\n } else if (health && armour) {\n h.css(\"display\", \"flex\");\n a.css(\"display\", \"flex\");\n u.css(\"display\", \"none\");\n } else {\n h.css(\"display\", \"none\");\n a.css(\"display\", \"none\");\n u.css(\"display\", \"flex\");\n }\n}\n\nfunction calculateHeading(direction) {\n var heading = \"\";\n\n if (direction < 22.5) {\n heading = \"N\";\n } else if (direction < 67.5) {\n heading = \"NW\";\n } else if (direction < 112.5) {\n heading = \"W\";\n } else if (direction < 157.5) {\n heading = \"SW\";\n } else if (direction < 202.5) {\n heading = \"S\";\n } else if (direction < 247.5) {\n heading = \"SE\";\n } else if (direction < 292.5) {\n heading = \"E\";\n } else if (direction < 337.5) {\n heading = \"NE\";\n } else {\n heading = \"N\";\n }\n\n return heading;\n}\n\n//# sourceURL=webpack:///./src/components/playerHud.js?");

/***/ }),

/***/ "./src/components/playerNotifications.js":
/*!***********************************************!*\
  !*** ./src/components/playerNotifications.js ***!
  \***********************************************/
/*! exports provided: createNotification */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, \"createNotification\", function() { return createNotification; });\n/* harmony import */ var jquery__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! jquery */ \"jquery\");\n/* harmony import */ var jquery__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(jquery__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _classes_Notification__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../classes/Notification */ \"./src/classes/Notification.js\");\n\n\nfunction createNotification(e) {\n  var item = new _classes_Notification__WEBPACK_IMPORTED_MODULE_1__[\"default\"](e.type, e.title, e.data, e.options, e.overrides);\n  checkNotification(item); // Show Notification\n\n  addNotification(item);\n}\n\nfunction checkNotification(e) {\n  // Check if notification exists\n  if (jquery__WEBPACK_IMPORTED_MODULE_0___default()(\"#player_notifications.\".concat(e.options.position)).children(\"#\".concat(e.type.key)).length) {\n    // console.log(e.type.key + \" exists already ... removing!\");\n    jquery__WEBPACK_IMPORTED_MODULE_0___default()(\"#player_notifications.\".concat(e.options.position)).children(\"#\".concat(e.type.key)).slideUp(\"fast\", function () {\n      jquery__WEBPACK_IMPORTED_MODULE_0___default()(this).remove();\n    });\n  }\n}\n\nfunction addNotification(e) {\n  // console.table(e);\n  // Notification Item\n  // Create Notification\n  var notification = jquery__WEBPACK_IMPORTED_MODULE_0___default()(\"<div class='notification'></div>\"); // Type\n  //    Key\n\n  if (e.type.key) {\n    notification.attr(\"id\", e.type.key);\n  } //    Priority\n\n\n  if (e.type.priority) {\n    notification.addClass(e.type.priority);\n  } //    Job\n\n\n  if (e.type.job) {\n    notification.addClass(e.type.job);\n  } // Header\n  // Create Header\n\n\n  var header = jquery__WEBPACK_IMPORTED_MODULE_0___default()(\"<div class='header'></div>\"); //    Code\n\n  if (e.data.code) {\n    header.append(\"<span class=\\\"pill code\\\">\".concat(e.data.code, \"</span>\"));\n  } //    Title\n\n\n  if (e.title) {\n    header.append(\"<span class=\\\"title\\\">\".concat(e.title, \"</span>\"));\n  } //    Callsign\n\n\n  if (e.data.callsign) {\n    header.append(\"<span class=\\\"pill callsign\\\">\".concat(e.data.callsign, \"</span>\"));\n  } // Append Header\n\n\n  if (e.data.code || e.title || e.data.callsign) {\n    notification.append(header);\n  } // Body\n  // Create Body\n\n\n  var body = jquery__WEBPACK_IMPORTED_MODULE_0___default()(\"<div class='body'></div>\"); //    Location\n\n  if (e.data.location) {\n    body.append(\"<div class=\\\"location\\\"><i class=\\\"fas fa-map-marker-alt\\\"></i><span>\".concat(e.data.location, \"</span></div>\"));\n  } //    Direction\n\n\n  if (e.data.direction) {\n    body.append(\"<div class=\\\"direction\\\"><i class=\\\"fas fa-compass\\\"></i><span>\".concat(e.data.direction, \"</span></div>\"));\n  } //    Message\n\n\n  if (e.data.message) {\n    body.append(\"<div class=\\\"direction\\\">\".concat(e.data.messageIcon || \"<i class=\\\"fas fa-comment-alt\\\"></i>\", \"<span>\").concat(e.data.message, \"</span></div>\"));\n  } // Append Body\n\n\n  notification.append(body); // Overrides\n  //    Header Background\n\n  if (e.overrides.headerBackground) {\n    notification.children(\".header\").css({\n      background: e.overrides.headerBackground\n    });\n  } //    Header Text Color\n\n\n  if (e.overrides.headerTextColor) {\n    notification.children(\".header\").css({\n      color: e.overrides.headerTextColor\n    });\n  } //    Body Background\n\n\n  if (e.overrides.bodyBackground) {\n    notification.children(\".body\").css({\n      background: e.overrides.bodyBackground\n    });\n  } //    Body Text Color\n\n\n  if (e.overrides.bodyTextColor) {\n    notification.children(\".body\").css({\n      color: e.overrides.bodyTextColor\n    });\n  } // Notifications List\n  // Options\n  //    Position\n\n\n  var notifications = jquery__WEBPACK_IMPORTED_MODULE_0___default()(\"#player_notifications.\".concat(e.options.position)); // Append Notification Item to the Notifications List\n\n  notifications.append(notification); //    Options\n\n  setTimeout(function () {\n    jquery__WEBPACK_IMPORTED_MODULE_0___default()(notification).slideUp(\"fast\", function () {\n      jquery__WEBPACK_IMPORTED_MODULE_0___default()(this).remove();\n    });\n  }, e.options.timeout || 1000);\n} // TODO: Queing system that detects the max height of #player_notification\n//       then checks to see if notifications are taller than the max height\n//       it will then add those items to the queue until there is room for\n//       them in the #player_notification\n\n//# sourceURL=webpack:///./src/components/playerNotifications.js?");

/***/ }),

/***/ "./src/main.config.js":
/*!****************************!*\
  !*** ./src/main.config.js ***!
  \****************************/
/*! exports provided: devMode, player_hudHidden */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, \"devMode\", function() { return devMode; });\n/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, \"player_hudHidden\", function() { return player_hudHidden; });\nvar devMode = false;\nvar player_hudHidden = false;\n\n//# sourceURL=webpack:///./src/main.config.js?");

/***/ }),

/***/ "./src/main.js":
/*!*********************!*\
  !*** ./src/main.js ***!
  \*********************/
/*! no exports provided */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _classes_Player__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./classes/Player */ \"./src/classes/Player.js\");\n/* harmony import */ var _main_config__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./main.config */ \"./src/main.config.js\");\n/* harmony import */ var _components_playerHud__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./components/playerHud */ \"./src/components/playerHud.js\");\n/* harmony import */ var _components_playerNotifications__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./components/playerNotifications */ \"./src/components/playerNotifications.js\");\n/* harmony import */ var _assets_main_scss__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./assets/main.scss */ \"./src/assets/main.scss\");\n/* harmony import */ var _assets_main_scss__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_assets_main_scss__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _tests_app_test__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./tests/app.test */ \"./src/tests/app.test.js\");\n/* harmony import */ var _tests_notification_test__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./tests/notification.test */ \"./src/tests/notification.test.js\");\n// Import Classes First\n // Import Application\n\n\n\n // Import Styles\n\n // Testing\n\n\n // Start Testing Modules\n\nif (_main_config__WEBPACK_IMPORTED_MODULE_1__[\"devMode\"]) {\n  _tests_app_test__WEBPACK_IMPORTED_MODULE_5__[\"default\"].start();\n} // Setup Player\n// Get Player Data\n\n\nif (_main_config__WEBPACK_IMPORTED_MODULE_1__[\"devMode\"]) {\n  // Create Test Player\n  var data = _tests_app_test__WEBPACK_IMPORTED_MODULE_5__[\"default\"].data;\n  Object(_classes_Player__WEBPACK_IMPORTED_MODULE_0__[\"createPlayer\"])(data);\n\n  if (_main_config__WEBPACK_IMPORTED_MODULE_1__[\"player_hudHidden\"]) {\n    Object(_components_playerHud__WEBPACK_IMPORTED_MODULE_2__[\"player_hideHud\"])(_main_config__WEBPACK_IMPORTED_MODULE_1__[\"player_hudHidden\"]);\n  }\n} else {\n  var playerHud = _main_config__WEBPACK_IMPORTED_MODULE_1__[\"player_hudHidden\"];\n  window.addEventListener(\"message\", function (event) {\n    var data = event.data; // Checks event\n\n    if (data.id != \"hud\") return; // Create Real Player\n\n    Object(_classes_Player__WEBPACK_IMPORTED_MODULE_0__[\"createPlayer\"])(data); // Hide Hud\n\n    if (data.hide != playerHud) {\n      Object(_components_playerHud__WEBPACK_IMPORTED_MODULE_2__[\"player_hideHud\"])(data.hide);\n      playerHud = data.hide;\n      return;\n    }\n  });\n} // Setup Notification\n\n\nif (_main_config__WEBPACK_IMPORTED_MODULE_1__[\"devMode\"]) {\n  _tests_notification_test__WEBPACK_IMPORTED_MODULE_6__[\"default\"].list.forEach(function (e) {\n    Object(_components_playerNotifications__WEBPACK_IMPORTED_MODULE_3__[\"createNotification\"])(e);\n  });\n} else {\n  // Something\n  window.addEventListener(\"message\", function (event) {\n    var data = event.data; // Checks Event\n\n    if (data.id != \"notification\") return;\n    Object(_components_playerNotifications__WEBPACK_IMPORTED_MODULE_3__[\"createNotification\"])(data);\n  });\n}\n\n//# sourceURL=webpack:///./src/main.js?");

/***/ }),

/***/ "./src/tests/app.test.js":
/*!*******************************!*\
  !*** ./src/tests/app.test.js ***!
  \*******************************/
/*! exports provided: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var jquery__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! jquery */ \"jquery\");\n/* harmony import */ var jquery__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(jquery__WEBPACK_IMPORTED_MODULE_0__);\n\n/* harmony default export */ __webpack_exports__[\"default\"] = ({\n  start: function start() {\n    jquery__WEBPACK_IMPORTED_MODULE_0___default()(\"body\").css({\n      background: \"#1c1c1c\"\n    });\n    console.log(\"Start App\");\n  },\n  data: {\n    health: 75,\n    armour: 5,\n    voice: {\n      active: true,\n      level: 3\n    },\n    location: {\n      aop: \"MAP\",\n      data_1: \"| Carson Ave\",\n      data_2: \"| Strawberry | 9133 | 15:24\",\n      direction: 45\n    },\n    vehicle: {\n      type: \"air\",\n      inVehicle: true,\n      seatbelt: true,\n      speed: 666,\n      fuel: 100\n    }\n  }\n}); // $(\"#btn_1\").click(function () {\n//   $(\".flowbar\").css(\"width\", \"100%\");\n// });\n// $(\"#btn_2\").click(function () {\n//   $(\".health\").css(\"flex-basis\", \"90%\");\n// });\n// $(\".buttons #btn_1\").click(function () {\n//   $(\".voice_bar .speaker .v_2\").css(\"display\", \"flex\");\n// });\n// $(\".buttons #btn_2\").click(function () {\n//   $(\".voice_bar .speaker .v_3\").css(\"display\", \"flex\");\n// });\n// $(\".buttons #btn_3\").click(function () {\n//   $(\".voice_bar .speaker .v_2\").css(\"display\", \"none\");\n//   $(\".voice_bar .speaker .v_3\").css(\"display\", \"none\");\n// });\n\n/* <button id=\"btn_1\">Hello</button>\r\n    <button id=\"btn_2\">flex</button>\r\n    <div class=\"buttons\">\r\n      <button id=\"btn_1\">v1</button>\r\n      <button id=\"btn_2\">v2</button>\r\n      <button id=\"btn_3\">hide_v</button>\r\n    </div> */\n\n//# sourceURL=webpack:///./src/tests/app.test.js?");

/***/ }),

/***/ "./src/tests/notification.test.js":
/*!****************************************!*\
  !*** ./src/tests/notification.test.js ***!
  \****************************************/
/*! exports provided: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = ({\n  list: [{\n    type: {\n      key: \"keys\",\n      priority: \"high\",\n      \"default\": false\n    },\n    title: \"Officer Down\",\n    data: {\n      callsign: \"AFP-11\",\n      code: \"10-99\",\n      direction: \"N\",\n      location: \"Avenue Road, Vinewood Boulevard\",\n      message: \"Some Message\"\n    },\n    options: {\n      timeout: 15000\n    },\n    overrides: {\n      headerBackground: \"#b700ff\",\n      headerTextColor: \"#3c7c3c\",\n      bodyBackground: \"rgb(255,0,0)\",\n      bodyTextColor: \"rgb(0,255,0)\"\n    }\n  }, {\n    type: {\n      key: \"keys\"\n    },\n    title: \"My Title\",\n    data: {\n      direction: \"N\",\n      location: \"Avenue Road, Vinewood Boulevard\",\n      message: \"Some Message\"\n    },\n    options: {\n      timeout: 15000\n    }\n  }, {\n    type: {},\n    title: \"My Title 2\",\n    data: {\n      message: \"Some Message\"\n    },\n    options: {\n      position: \"bottom-right\",\n      timeout: 3000\n    }\n  }, {\n    data: {\n      messageIcon: \"<i class='fas fa-comments-dollar'></i>\",\n      message: \"Some Message\"\n    },\n    options: {\n      position: \"top-left\",\n      timeout: 8000\n    }\n  }, {\n    type: {\n      key: \"keys\"\n    },\n    title: \"My Title\",\n    data: {\n      direction: \"N\",\n      location: \"Avenue Road, Vinewood Boulevard\",\n      message: \"Some Message\"\n    },\n    options: {\n      position: \"top-left\",\n      timeout: 15000\n    }\n  }, {\n    type: {\n      key: \"keys\"\n    },\n    title: \"My Title\",\n    data: {\n      direction: \"N\",\n      location: \"Avenue Road, Vinewood Boulevard\",\n      message: \"Some Message\"\n    },\n    options: {\n      position: \"top-left\",\n      timeout: 15000\n    }\n  }]\n});\n\n//# sourceURL=webpack:///./src/tests/notification.test.js?");

/***/ }),

/***/ "jquery":
/*!*************************!*\
  !*** external "jQuery" ***!
  \*************************/
/*! no static exports found */
/***/ (function(module, exports) {

eval("module.exports = jQuery;\n\n//# sourceURL=webpack:///external_%22jQuery%22?");

/***/ })

/******/ });