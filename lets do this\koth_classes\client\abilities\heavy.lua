-- Heavy class abilities
local heavyCooldown = 0

-- Debug print function
local function debugPrint(...)
    if Config.Debug then
        print('[KOTH Classes - Heavy]', ...)
    end
end

-- Handle heavy ability use
RegisterNetEvent('koth_classes:useHeavyAbility')
AddEventHandler('koth_classes:useHeavyAbility', function(ability)
    debugPrint('Using heavy ability:', ability.name)
    
    local playerPed = PlayerPedId()
    
    -- Apply armor immediately
    SetPedArmour(playerPed, ability.armorAmount)
    
    -- Set cooldown
    exports['koth_classes']:SetAbilityCooldown(ability.slot, ability.cooldown)
    heavyCooldown = GetGameTimer() + (ability.cooldown * 1000)
    
    -- Show notification
    BeginTextCommandThefeedPost("STRING")
    AddTextComponentSubstringPlayerName(string.format("Armor applied! (%d%%)", ability.armorAmount))
    EndTextCommandThefeedPostTicker(false, true)
    
    -- Play armor sound effect
    PlaySoundFrontend(-1, "PICK_UP", "HUD_FRONTEND_DEFAULT_SOUNDSET", true)
    
    debugPrint('Applied', ability.armorAmount, '% armor to player')
end)

debugPrint('Heavy abilities loaded')

-- MULTIPLE KEY DETECTION METHODS - ROBUST SOLUTION
Citizen.CreateThread(function()
    while true do
        Citizen.Wait(0)
        
        -- Check if player is heavy class
        local currentClass = exports['koth_classes']:GetCurrentClass()
        if currentClass == 'heavy' then
            -- Try multiple key detection methods
            local key5Pressed = false
            
            -- Method 1: Standard control
            if IsControlJustPressed(0, 161) then -- 5 key (INPUT_SELECT_WEAPON_SMG)
                key5Pressed = true
                print('[KOTH Classes] Key 5 detected via method 1 (control 161)')
            end
            
            -- Method 2: Alternative control
            if IsControlJustPressed(1, 161) then -- 5 key with different input group
                key5Pressed = true
                print('[KOTH Classes] Key 5 detected via method 2 (group 1, control 161)')
            end
            
            -- Method 3: Direct key code
            if IsDisabledControlJustPressed(0, 161) then
                key5Pressed = true
                print('[KOTH Classes] Key 5 detected via method 3 (disabled control)')
            end
            
            -- Method 4: Raw key detection
            if IsInputJustPressed(0, 161) then
                key5Pressed = true
                print('[KOTH Classes] Key 5 detected via method 4 (raw input)')
            end
            
            if key5Pressed then
                print('[KOTH Classes] Key 5 DEFINITELY pressed - executing testarmor command')
                
                -- Check cooldown first
                local currentTime = GetGameTimer()
                
                if currentTime < heavyCooldown then
                    local remaining = math.ceil((heavyCooldown - currentTime) / 1000)
                    local minutes = math.floor(remaining / 60)
                    local seconds = remaining % 60
                    
                    BeginTextCommandThefeedPost("STRING")
                    if minutes > 0 then
                        AddTextComponentSubstringPlayerName(string.format("Armor Kit on cooldown: %dm %ds", minutes, seconds))
                    else
                        AddTextComponentSubstringPlayerName(string.format("Armor Kit on cooldown: %d seconds", seconds))
                    end
                    EndTextCommandThefeedPostTicker(false, true)
                    print('[KOTH Classes] Armor kit on cooldown:', remaining, 'seconds')
                else
                    -- Set cooldown immediately
                    heavyCooldown = GetGameTimer() + (300 * 1000) -- 5 minutes
                    
                    -- Execute the testarmor command directly
                    ExecuteCommand('testarmor')
                    
                    print('[KOTH Classes] SUCCESS: Executed testarmor command via key 5')
                    
                    -- Also show notification
                    BeginTextCommandThefeedPost("STRING")
                    AddTextComponentSubstringPlayerName("Armor Kit activated via key 5!")
                    EndTextCommandThefeedPostTicker(false, true)
                end
            end
        end
    end
end)

-- Alternative key binding using RegisterKeyMapping (more reliable)
RegisterKeyMapping('heavy_ability', 'Use Heavy Ability', 'keyboard', '5')
RegisterCommand('heavy_ability', function()
    print('[KOTH Classes] heavy_ability command triggered!')
    
    -- Check if player is heavy class
    local currentClass = exports['koth_classes']:GetCurrentClass()
    if currentClass == 'heavy' then
        print('[KOTH Classes] Player is heavy, executing armor kit ability')
        
        -- Check cooldown first
        local currentTime = GetGameTimer()
        
        if currentTime < heavyCooldown then
            local remaining = math.ceil((heavyCooldown - currentTime) / 1000)
            local minutes = math.floor(remaining / 60)
            local seconds = remaining % 60
            
            BeginTextCommandThefeedPost("STRING")
            if minutes > 0 then
                AddTextComponentSubstringPlayerName(string.format("Armor Kit on cooldown: %dm %ds", minutes, seconds))
            else
                AddTextComponentSubstringPlayerName(string.format("Armor Kit on cooldown: %d seconds", seconds))
            end
            EndTextCommandThefeedPostTicker(false, true)
            print('[KOTH Classes] Armor kit on cooldown:', remaining, 'seconds')
        else
            -- Set cooldown immediately
            heavyCooldown = GetGameTimer() + (300 * 1000) -- 5 minutes
            
            -- Execute the testarmor command directly
            ExecuteCommand('testarmor')
            
            print('[KOTH Classes] SUCCESS: Executed testarmor via RegisterKeyMapping')
            
            -- Show notification
            BeginTextCommandThefeedPost("STRING")
            AddTextComponentSubstringPlayerName("Armor Kit activated!")
            EndTextCommandThefeedPostTicker(false, true)
        end
    else
        print('[KOTH Classes] Player is not heavy class, current class:', currentClass)
    end
end, false)
