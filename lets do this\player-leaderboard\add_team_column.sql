-- First, check if the current_team column exists
-- If you get an error, the column doesn't exist

-- Add the current_team column if it doesn't exist
ALTER TABLE koth_players 
ADD COLUMN current_team VARCHAR(10) DEFAULT NULL;

-- Now assign teams to all players
UPDATE koth_players 
SET current_team = CASE 
    WHEN (id % 3) = 0 THEN 'red'
    WHEN (id % 3) = 1 THEN 'blue'
    ELSE 'green'
END;

-- Verify the teams are assigned
SELECT current_team, COUNT(*) as player_count 
FROM koth_players 
GROUP BY current_team;

-- Show some sample players with their teams
SELECT id, player_name, current_team, kills, deaths, zone_kills 
FROM koth_players 
LIMIT 10;
