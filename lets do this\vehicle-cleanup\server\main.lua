-- Handle vehicle deletion requests from clients who don't have control
RegisterNetEvent('vehicleCleanup:requestDelete', function(vehicleNetId)
    local src = source
    
    -- Verify the network ID is valid
    if not vehicleNetId or type(vehicleNetId) ~= 'number' then
        return
    end
    
    -- Get the vehicle entity
    local vehicle = NetworkGetEntityFromNetworkId(vehicleNetId)
    
    if DoesEntityExist(vehicle) then
        -- Delete the vehicle
        DeleteEntity(vehicle)
        
        if Config.Debug then
            print(('[Vehicle Cleanup] Server deleted vehicle with Net ID %s requested by player %s'):format(vehicleNetId, src))
        end
    end
end)

print('[Vehicle Cleanup] Server script loaded')
