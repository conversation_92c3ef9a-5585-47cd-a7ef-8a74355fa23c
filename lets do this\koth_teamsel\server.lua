print('[KOTH] Server loading...')

-- MONEY AND XP SYSTEM - Define at top so all handlers can access it
local playerData = {} -- Cache for player data
print(('[KOTH] DEBUG: playerData initialized as %s'):format(type(playerData)))

local teamSpawns = {
  red =    { x = 2238.15, y = 3788.91, z = 35.89, heading = 120.5 },
  blue =   { x = 1323.77, y = 3143.33, z = 40.41, heading = 282.48 },
  green =  { x = 1865.99, y = 2607.15, z = 45.67, heading = 276.45 }
}

-- Team player tracking
local playerTeams = {} -- Track each player's team
local teamCounts = { red = 0, blue = 0, green = 0 } -- Live team counts
local zonePoints = { red = 0, blue = 0, green = 0 } -- Zone control points

-- Attachment data with correct weapon-specific components
local attachmentPrice = 150

-- Weapon-specific attachments
local weaponAttachments = {
  -- Assault Rifles
  ["WEAPON_ASSAULTRIFLE"] = {
    { name = "Extended Magazine", component = "COMPONENT_ASSAULTRIFLE_CLIP_02", image = "attachment_extendedmag.png" },
    { name = "Drum Magazine", component = "COMPONENT_ASSAULTRIFLE_CLIP_03", image = "attachment_extendedmag2.png" },
    { name = "Suppressor", component = "COMPONENT_AT_AR_SUPP_02", image = "attachment_suppressor.png" },
    { name = "Flashlight", component = "COMPONENT_AT_AR_FLSH", image = "attachment_flashlight.png" },
    { name = "Grip", component = "COMPONENT_AT_AR_AFGRIP", image = "attachment_grip.png" },
    { name = "Scope", component = "COMPONENT_AT_SCOPE_MACRO", image = "attachment_scope.png" }
  },
  ["WEAPON_CARBINERIFLE"] = {
    { name = "Extended Magazine", component = "COMPONENT_CARBINERIFLE_CLIP_02", image = "attachment_extendedmag.png" },
    { name = "Box Magazine", component = "COMPONENT_CARBINERIFLE_CLIP_03", image = "attachment_extendedmag2.png" },
    { name = "Suppressor", component = "COMPONENT_AT_AR_SUPP", image = "attachment_suppressor.png" },
    { name = "Flashlight", component = "COMPONENT_AT_AR_FLSH", image = "attachment_flashlight.png" },
    { name = "Grip", component = "COMPONENT_AT_AR_AFGRIP", image = "attachment_grip.png" },
    { name = "Scope", component = "COMPONENT_AT_SCOPE_MEDIUM", image = "attachment_scope.png" }
  },
  ["WEAPON_ADVANCEDRIFLE"] = {
    { name = "Extended Magazine", component = "COMPONENT_ADVANCEDRIFLE_CLIP_02", image = "attachment_extendedmag.png" },
    { name = "Suppressor", component = "COMPONENT_AT_AR_SUPP", image = "attachment_suppressor.png" },
    { name = "Flashlight", component = "COMPONENT_AT_AR_FLSH", image = "attachment_flashlight.png" },
    { name = "Scope", component = "COMPONENT_AT_SCOPE_SMALL", image = "attachment_scope.png" }
  },
  ["WEAPON_SPECIALCARBINE"] = {
    { name = "Extended Magazine", component = "COMPONENT_SPECIALCARBINE_CLIP_02", image = "attachment_extendedmag.png" },
    { name = "Drum Magazine", component = "COMPONENT_SPECIALCARBINE_CLIP_03", image = "attachment_extendedmag2.png" },
    { name = "Suppressor", component = "COMPONENT_AT_AR_SUPP_02", image = "attachment_suppressor.png" },
    { name = "Flashlight", component = "COMPONENT_AT_AR_FLSH", image = "attachment_flashlight.png" },
    { name = "Grip", component = "COMPONENT_AT_AR_AFGRIP", image = "attachment_grip.png" },
    { name = "Scope", component = "COMPONENT_AT_SCOPE_MEDIUM", image = "attachment_scope.png" }
  },
  ["WEAPON_BULLPUPRIFLE"] = {
    { name = "Extended Magazine", component = "COMPONENT_BULLPUPRIFLE_CLIP_02", image = "attachment_extendedmag.png" },
    { name = "Suppressor", component = "COMPONENT_AT_AR_SUPP", image = "attachment_suppressor.png" },
    { name = "Flashlight", component = "COMPONENT_AT_AR_FLSH", image = "attachment_flashlight.png" },
    { name = "Grip", component = "COMPONENT_AT_AR_AFGRIP", image = "attachment_grip.png" },
    { name = "Scope", component = "COMPONENT_AT_SCOPE_SMALL", image = "attachment_scope.png" }
  },
  -- SMGs
  ["WEAPON_SMG"] = {
    { name = "Extended Magazine", component = "COMPONENT_SMG_CLIP_02", image = "attachment_extendedmag.png" },
    { name = "Drum Magazine", component = "COMPONENT_SMG_CLIP_03", image = "attachment_extendedmag2.png" },
    { name = "Suppressor", component = "COMPONENT_AT_PI_SUPP", image = "attachment_suppressor.png" },
    { name = "Flashlight", component = "COMPONENT_AT_AR_FLSH", image = "attachment_flashlight.png" },
    { name = "Scope", component = "COMPONENT_AT_SCOPE_MACRO_02", image = "attachment_scope.png" }
  },
  ["WEAPON_MICROSMG"] = {
    { name = "Extended Magazine", component = "COMPONENT_MICROSMG_CLIP_02", image = "attachment_extendedmag.png" },
    { name = "Suppressor", component = "COMPONENT_AT_AR_SUPP_02", image = "attachment_suppressor.png" },
    { name = "Flashlight", component = "COMPONENT_AT_PI_FLSH", image = "attachment_flashlight.png" },
    { name = "Scope", component = "COMPONENT_AT_SCOPE_MACRO", image = "attachment_scope.png" }
  },
  ["WEAPON_ASSAULTSMG"] = {
    { name = "Extended Magazine", component = "COMPONENT_ASSAULTSMG_CLIP_02", image = "attachment_extendedmag.png" },
    { name = "Suppressor", component = "COMPONENT_AT_AR_SUPP_02", image = "attachment_suppressor.png" },
    { name = "Flashlight", component = "COMPONENT_AT_AR_FLSH", image = "attachment_flashlight.png" },
    { name = "Scope", component = "COMPONENT_AT_SCOPE_MACRO", image = "attachment_scope.png" }
  },
  ["WEAPON_COMBATPDW"] = {
    { name = "Extended Magazine", component = "COMPONENT_COMBATPDW_CLIP_02", image = "attachment_extendedmag.png" },
    { name = "Drum Magazine", component = "COMPONENT_COMBATPDW_CLIP_03", image = "attachment_extendedmag2.png" },
    { name = "Flashlight", component = "COMPONENT_AT_AR_FLSH", image = "attachment_flashlight.png" },
    { name = "Grip", component = "COMPONENT_AT_AR_AFGRIP", image = "attachment_grip.png" },
    { name = "Scope", component = "COMPONENT_AT_SCOPE_SMALL", image = "attachment_scope.png" }
  },
  -- Pistols
  ["WEAPON_PISTOL"] = {
    { name = "Extended Magazine", component = "COMPONENT_PISTOL_CLIP_02", image = "attachment_extendedmag.png" },
    { name = "Suppressor", component = "COMPONENT_AT_PI_SUPP_02", image = "attachment_suppressor.png" },
    { name = "Flashlight", component = "COMPONENT_AT_PI_FLSH", image = "attachment_flashlight.png" }
  },
  ["WEAPON_COMBATPISTOL"] = {
    { name = "Extended Magazine", component = "COMPONENT_COMBATPISTOL_CLIP_02", image = "attachment_extendedmag.png" },
    { name = "Suppressor", component = "COMPONENT_AT_PI_SUPP", image = "attachment_suppressor.png" },
    { name = "Flashlight", component = "COMPONENT_AT_PI_FLSH", image = "attachment_flashlight.png" }
  },
  ["WEAPON_APPISTOL"] = {
    { name = "Extended Magazine", component = "COMPONENT_APPISTOL_CLIP_02", image = "attachment_extendedmag.png" },
    { name = "Suppressor", component = "COMPONENT_AT_PI_SUPP", image = "attachment_suppressor.png" },
    { name = "Flashlight", component = "COMPONENT_AT_PI_FLSH", image = "attachment_flashlight.png" }
  },
  ["WEAPON_PISTOL50"] = {
    { name = "Extended Magazine", component = "COMPONENT_PISTOL50_CLIP_02", image = "attachment_extendedmag.png" },
    { name = "Suppressor", component = "COMPONENT_AT_AR_SUPP_02", image = "attachment_suppressor.png" },
    { name = "Flashlight", component = "COMPONENT_AT_PI_FLSH", image = "attachment_flashlight.png" }
  },
  -- MGs
  ["WEAPON_MG"] = {
    { name = "Extended Magazine", component = "COMPONENT_MG_CLIP_02", image = "attachment_extendedmag.png" },
    { name = "Scope", component = "COMPONENT_AT_SCOPE_SMALL_02", image = "attachment_scope.png" }
  },
  ["WEAPON_COMBATMG"] = {
    { name = "Extended Magazine", component = "COMPONENT_COMBATMG_CLIP_02", image = "attachment_extendedmag.png" },
    { name = "Grip", component = "COMPONENT_AT_AR_AFGRIP", image = "attachment_grip.png" },
    { name = "Scope", component = "COMPONENT_AT_SCOPE_MEDIUM", image = "attachment_scope.png" }
  },
  -- Snipers
  ["WEAPON_SNIPERRIFLE"] = {
    { name = "Suppressor", component = "COMPONENT_AT_AR_SUPP_02", image = "attachment_suppressor.png" },
    { name = "Advanced Scope", component = "COMPONENT_AT_SCOPE_MAX", image = "attachment_scope2.png" }
  },
  ["WEAPON_HEAVYSNIPER"] = {
    { name = "Advanced Scope", component = "COMPONENT_AT_SCOPE_MAX", image = "attachment_scope2.png" }
  },
  ["WEAPON_MARKSMANRIFLE"] = {
    { name = "Extended Magazine", component = "COMPONENT_MARKSMANRIFLE_CLIP_02", image = "attachment_extendedmag.png" },
    { name = "Suppressor", component = "COMPONENT_AT_AR_SUPP", image = "attachment_suppressor.png" },
    { name = "Flashlight", component = "COMPONENT_AT_AR_FLSH", image = "attachment_flashlight.png" },
    { name = "Grip", component = "COMPONENT_AT_AR_AFGRIP", image = "attachment_grip.png" },
    { name = "Scope", component = "COMPONENT_AT_SCOPE_LARGE_FIXED_ZOOM", image = "attachment_scope.png" }
  }
}

-- Default attachments for weapons not in the list
local defaultAttachments = {
  { name = "Extended Magazine", component = "COMPONENT_AT_CLIP_02", image = "attachment_extendedmag.png" },
  { name = "Suppressor", component = "COMPONENT_AT_AR_SUPP", image = "attachment_suppressor.png" },
  { name = "Flashlight", component = "COMPONENT_AT_AR_FLSH", image = "attachment_flashlight.png" },
  { name = "Grip", component = "COMPONENT_AT_AR_AFGRIP", image = "attachment_grip.png" },
  { name = "Scope", component = "COMPONENT_AT_SCOPE_MACRO", image = "attachment_scope.png" }
}

-- Initialize team counts on resource start
AddEventHandler('onResourceStart', function(resourceName)
  if GetCurrentResourceName() ~= resourceName then return end
  
  -- Ensure team counts are initialized
  teamCounts = { red = 0, blue = 0, green = 0 }
  print('[KOTH] Team counts initialized on resource start')
end)

-- Function to update team counts and broadcast to all clients
function UpdateTeamCounts()
  -- Count actual players in each team
  local actualCounts = { red = 0, blue = 0, green = 0 }

  print('[KOTH] UpdateTeamCounts called - Current playerTeams:')
  for playerId, team in pairs(playerTeams) do
    local ping = GetPlayerPing(playerId)
    print(('  Player %d: Team %s, Ping: %d'):format(playerId, team, ping))
    if ping > 0 then -- Player is still connected
      actualCounts[team] = (actualCounts[team] or 0) + 1
      print(('    -> Counted for team %s (new count: %d)'):format(team, actualCounts[team]))
    else
      print(('    -> Skipped (no ping)'):format())
    end
  end

  -- Update global team counts
  teamCounts = actualCounts

  print(('[KOTH] Final team counts - Red: %d, Blue: %d, Green: %d'):format(
    teamCounts.red, teamCounts.blue, teamCounts.green
  ))

  -- Broadcast to all clients for HUD update
  TriggerClientEvent('koth:updateTeamCounts', -1, teamCounts)
  print('[KOTH] Broadcasted team counts to all clients')
end

-- Function to get player's team
function GetPlayerTeam(playerId)
  return playerTeams[playerId]
end

-- Function to update zone points and broadcast to all clients
function UpdateZonePoints()
  -- Broadcast zone points to all clients
  TriggerClientEvent('koth:updateZonePoints', -1, zonePoints)

  print(('[KOTH] Zone points updated - Red: %d, Green: %d, Blue: %d'):format(
    zonePoints.red, zonePoints.green, zonePoints.blue
  ))
end

-- Function to award zone points to a team
function AwardZonePoints(team, points)
  if team and zonePoints[team] then
    zonePoints[team] = zonePoints[team] + points
    UpdateZonePoints()

    print(('[KOTH] Awarded %d points to %s team (Total: %d)'):format(
      points, team, zonePoints[team]
    ))
  end
end

-- Team selection handler (no persistence)
RegisterNetEvent('koth:pickTeam', function(team)
  local src = source
  print(('[KOTH] Player %d selected team: %s'):format(src, team or 'none'))

  local spawn = teamSpawns[team]
  if spawn then
    -- Update player's team (session only)
    playerTeams[src] = team
    print(('[KOTH] Updated playerTeams[%d] = %s'):format(src, team))

    -- Spawn player
    TriggerClientEvent('koth:spawnPlayer', src, spawn)
    print(('[KOTH] Spawning player %d at team %s'):format(src, team))

    -- Send player data immediately after team selection
    if playerData[src] then
      print(('[KOTH] Sending player data to %s after team selection'):format(GetPlayerName(src)))
      TriggerClientEvent('koth:updatePlayerData', src, playerData[src])
    else
      print(('[KOTH] Player data not loaded yet for %s, loading now...'):format(GetPlayerName(src)))
      LoadPlayerData(src)
      -- Send data after a short delay to ensure it loads
      Citizen.SetTimeout(1000, function()
        if playerData[src] then
          TriggerClientEvent('koth:updatePlayerData', src, playerData[src])
          print(('[KOTH] Sent player data to %s after loading'):format(GetPlayerName(src)))
        end
      end)
    end

    -- Update team counts for all players immediately
    print('[KOTH] Calling UpdateTeamCounts after team selection...')
    UpdateTeamCounts()
    
    -- Also send zone points immediately
    UpdateZonePoints()
    
    -- Also send a delayed update to ensure it gets through
    Citizen.SetTimeout(1000, function()
      print('[KOTH] Sending delayed team count update...')
      UpdateTeamCounts()
      -- Send player data again to ensure UI is updated
      if playerData[src] then
        TriggerClientEvent('koth:updatePlayerData', src, playerData[src])
      end
    end)
  else
    print(('[KOTH] Invalid team: %s'):format(team or 'none'))
  end
end)

-- New event to send attachment menu data to client
RegisterNetEvent('koth:getAttachmentMenu', function(weaponHash)
  local src = source
  print(('[KOTH] Attachment menu requested by player %d for weapon hash %s'):format(src, tostring(weaponHash)))

  -- Check player money
  local money = 0
  if playerData[src] and playerData[src].money then
    money = playerData[src].money
  end

  -- Get weapon name from hash
  local weaponName = GetWeaponNameFromHash(weaponHash)
  
  -- Get weapon-specific attachments or use defaults
  local attachments = weaponAttachments[weaponName] or defaultAttachments
  
  -- Prepare attachment list with price and full image path
  local attachmentList = {}
  for _, attachment in ipairs(attachments) do
    table.insert(attachmentList, {
      name = attachment.name,
      component = attachment.component,
      price = attachmentPrice,
      image = "nui://koth_teamsel/images of guns/" .. attachment.image  -- Use NUI protocol for images
    })
  end

  -- Send attachment menu data to client
  TriggerClientEvent('koth:showAttachmentMenu', src, {
    attachments = attachmentList,
    weaponName = weaponName:gsub("WEAPON_", ""):gsub("_", " "),
    money = money
  })
end)

-- Helper function to get weapon name from hash
function GetWeaponNameFromHash(hash)
  -- Common weapon hash to name mapping
  local weaponHashToName = {
    [GetHashKey("WEAPON_PISTOL")] = "WEAPON_PISTOL",
    [GetHashKey("WEAPON_COMBATPISTOL")] = "WEAPON_COMBATPISTOL",
    [GetHashKey("WEAPON_APPISTOL")] = "WEAPON_APPISTOL",
    [GetHashKey("WEAPON_PISTOL50")] = "WEAPON_PISTOL50",
    [GetHashKey("WEAPON_MICROSMG")] = "WEAPON_MICROSMG",
    [GetHashKey("WEAPON_SMG")] = "WEAPON_SMG",
    [GetHashKey("WEAPON_ASSAULTSMG")] = "WEAPON_ASSAULTSMG",
    [GetHashKey("WEAPON_COMBATPDW")] = "WEAPON_COMBATPDW",
    [GetHashKey("WEAPON_ASSAULTRIFLE")] = "WEAPON_ASSAULTRIFLE",
    [GetHashKey("WEAPON_CARBINERIFLE")] = "WEAPON_CARBINERIFLE",
    [GetHashKey("WEAPON_ADVANCEDRIFLE")] = "WEAPON_ADVANCEDRIFLE",
    [GetHashKey("WEAPON_SPECIALCARBINE")] = "WEAPON_SPECIALCARBINE",
    [GetHashKey("WEAPON_BULLPUPRIFLE")] = "WEAPON_BULLPUPRIFLE",
    [GetHashKey("WEAPON_MG")] = "WEAPON_MG",
    [GetHashKey("WEAPON_COMBATMG")] = "WEAPON_COMBATMG",
    [GetHashKey("WEAPON_SNIPERRIFLE")] = "WEAPON_SNIPERRIFLE",
    [GetHashKey("WEAPON_HEAVYSNIPER")] = "WEAPON_HEAVYSNIPER",
    [GetHashKey("WEAPON_MARKSMANRIFLE")] = "WEAPON_MARKSMANRIFLE",
    -- Add more weapons as needed
  }
  
  return weaponHashToName[hash] or "WEAPON_UNKNOWN"
end

-- Event to handle attachment purchase
RegisterNetEvent('koth:purchaseAttachment', function(data)
  local src = source
  local attachmentName = data.name
  local attachmentComponent = data.component
  local price = tonumber(data.price) or attachmentPrice

  print(('[KOTH] Player %d attempting to purchase attachment %s for $%d'):format(src, attachmentName, price))

  -- Validate player money
  if not playerData[src] or not playerData[src].money then
    print(('[KOTH] Player data not loaded for %d'):format(src))
    TriggerClientEvent('koth:purchaseResult', src, false, "Player data not loaded. Please try again.")
    return
  end

  if playerData[src].money < price then
    TriggerClientEvent('koth:purchaseResult', src, false, "Not enough money to purchase attachment.")
    print(('[KOTH] Player %d has insufficient funds: $%d needed, has $%d'):format(src, price, playerData[src].money))
    return
  end

  -- Deduct money
  playerData[src].money = playerData[src].money - price
  SavePlayerData(src)

  -- Apply attachment to player's weapon
  TriggerClientEvent('koth:applyAttachment', src, attachmentComponent)

  -- Notify purchase success
  TriggerClientEvent('koth:purchaseResult', src, true, ("Purchased %s for $%d"):format(attachmentName, price))

  -- Update player money HUD
  TriggerClientEvent('koth:updatePlayerData', src, playerData[src])
end)

-- Team count request (for team selection screen)
RegisterNetEvent('koth:requestCounts', function()
  local src = source
  print(('[KOTH] Player %d requested team counts'):format(src))

  -- Always show team selection (no persistence)
  TriggerClientEvent('koth:updateCounts', src, teamCounts)
end)

-- Vehicle ownership tracking
local playerOwnedVehicles = {} -- Track purchased vehicles per player
local playerRentedVehicles = {} -- Track rented vehicles (temporary)

-- Vehicle purchase handlers
RegisterNetEvent('koth:buyVehicle', function(data)
  local src = source
  local vehicleName = nil
  local price = nil
  
  -- Handle both old format (string) and new format (table)
  if type(data) == 'table' then
    vehicleName = data.name
    -- Defensive check for price field
    if type(data.price) == 'table' then
      print(('[KOTH] ERROR: price field is a table for player %d, vehicle %s'):format(src, vehicleName or 'none'))
      -- Try to extract price from nested table
      if data.price.price then
        price = tonumber(data.price.price) or 0
      elseif data.price.cost then
        price = tonumber(data.price.cost) or 0
      else
        price = 0
      end
    else
      price = tonumber(data.price) or tonumber(data.cost) or 0
    end
  else
    vehicleName = data
    price = 0 -- Default price if not provided
  end

  print(('[KOTH] Player %d wants to buy: %s (price: $%d)'):format(src, vehicleName or 'none', price))
  print(('[KOTH] Raw data received: %s'):format(json.encode(data)))

  if not vehicleName then
    TriggerClientEvent('koth:purchaseResult', src, false, 'Invalid vehicle name')
    return
  end

  -- Check if player has enough money
  if not playerData[src] then
    print(('[KOTH] Player data not loaded for %d, attempting to load...'):format(src))
    LoadPlayerData(src)

    Citizen.SetTimeout(500, function()
      if not playerData[src] then
        print(('[KOTH] Failed to load player data for %d after retry'):format(src))
        TriggerClientEvent('koth:purchaseResult', src, false, 'Player data not loaded. Please try again.')
      else
        print(('[KOTH] Player data loaded for %d, retrying vehicle purchase'):format(src))
        TriggerEvent('koth:buyVehicle', data)
      end
    end)
    return
  end

  -- Check if player already owns this vehicle
  if not playerOwnedVehicles[src] then
    playerOwnedVehicles[src] = {}
  end
  
  if playerOwnedVehicles[src][vehicleName] then
    -- Player already owns this vehicle, spawn it for free
    print(('[KOTH] Player %d already owns %s, spawning for free'):format(src, vehicleName))
    SpawnVehicleForPlayer(src, vehicleName, 'owned', 0)
    TriggerClientEvent('koth:purchaseResult', src, true, ('Spawned your owned %s'):format(vehicleName))
    return
  end

  -- Check money for new purchase
  if price > 0 and playerData[src].money < price then
    TriggerClientEvent('koth:purchaseResult', src, false, 'Not enough money')
    print(('[KOTH] Player %d cannot afford %s ($%d) - has $%d'):format(src, vehicleName, price, playerData[src].money))
    return
  end

  -- Deduct money and save to database
  if price > 0 then
    playerData[src].money = playerData[src].money - price
    SavePlayerData(src)
  end

  -- Add vehicle to owned list
  playerOwnedVehicles[src][vehicleName] = true

  -- Persist owned vehicle to database
  local identifiers = GetPlayerIdentifiers(src)
  if identifiers and identifiers.txid then
    exports.oxmysql:execute('INSERT IGNORE INTO koth_player_vehicles (txid, vehicle_name) VALUES (?, ?)', {identifiers.txid, vehicleName}, function(affectedRows)
      if affectedRows and affectedRows > 0 then
        print(('[KOTH] Persisted owned vehicle %s for player %d'):format(vehicleName, src))
      else
        print(('[KOTH] Owned vehicle %s for player %d already exists in database'):format(vehicleName, src))
      end
    end)
  else
    print(('[KOTH] Could not persist owned vehicle %s for player %d - missing TXID'):format(vehicleName, src))
  end
  
  print(('[KOTH] Player %d bought %s for $%d - remaining: $%d'):format(src, vehicleName, price, playerData[src].money))

  -- Update client HUD with new money
  TriggerClientEvent('koth:updatePlayerData', src, playerData[src])

  -- Spawn the vehicle
  SpawnVehicleForPlayer(src, vehicleName, 'buy', price)
  TriggerClientEvent('koth:purchaseResult', src, true, ('Purchased %s for $%d - You now own this vehicle!'):format(vehicleName, price))
end)

RegisterNetEvent('koth:rentVehicle', function(data)
  local src = source
  local vehicleName = nil
  local price = nil
  
  -- Handle both old format (string) and new format (table)
  if type(data) == 'table' then
    vehicleName = data.name
    price = tonumber(data.price) or tonumber(data.rent) or 0
  else
    vehicleName = data
    price = 0 -- Default price if not provided
  end

  print(('[KOTH] Player %d wants to rent: %s (price: $%d)'):format(src, vehicleName or 'none', price))
  print(('[KOTH] Raw data received: %s'):format(json.encode(data)))

  if not vehicleName then
    TriggerClientEvent('koth:purchaseResult', src, false, 'Invalid vehicle name')
    return
  end

  -- Check if player has enough money
  if not playerData[src] then
    print(('[KOTH] Player data not loaded for %d, attempting to load...'):format(src))
    LoadPlayerData(src)

    Citizen.SetTimeout(500, function()
      if not playerData[src] then
        print(('[KOTH] Failed to load player data for %d after retry'):format(src))
        TriggerClientEvent('koth:purchaseResult', src, false, 'Player data not loaded. Please try again.')
      else
        print(('[KOTH] Player data loaded for %d, retrying vehicle rental'):format(src))
        TriggerEvent('koth:rentVehicle', data)
      end
    end)
    return
  end

  -- Check if player already owns this vehicle
  if playerOwnedVehicles[src] and playerOwnedVehicles[src][vehicleName] then
    -- Player owns this vehicle, spawn it for free instead
    print(('[KOTH] Player %d owns %s, spawning for free instead of renting'):format(src, vehicleName))
    SpawnVehicleForPlayer(src, vehicleName, 'owned', 0)
    TriggerClientEvent('koth:purchaseResult', src, true, ('You own this vehicle! Spawned for free'):format(vehicleName))
    return
  end

  if price > 0 and playerData[src].money < price then
    TriggerClientEvent('koth:purchaseResult', src, false, 'Not enough money')
    print(('[KOTH] Player %d cannot afford to rent %s ($%d) - has $%d'):format(src, vehicleName, price, playerData[src].money))
    return
  end

  -- Deduct money and save to database
  if price > 0 then
    playerData[src].money = playerData[src].money - price
    SavePlayerData(src)
  end

  -- Track rented vehicle (temporary - will be removed on disconnect or after time)
  if not playerRentedVehicles[src] then
    playerRentedVehicles[src] = {}
  end
  playerRentedVehicles[src][vehicleName] = true

  print(('[KOTH] Player %d rented %s for $%d - remaining: $%d'):format(src, vehicleName, price, playerData[src].money))

  -- Update client HUD with new money
  TriggerClientEvent('koth:updatePlayerData', src, playerData[src])

  -- Spawn the vehicle
  SpawnVehicleForPlayer(src, vehicleName, 'rent', price)
  TriggerClientEvent('koth:purchaseResult', src, true, ('Rented %s for $%d - Temporary use only'):format(vehicleName, price))
  
  -- Remove rental after 10 minutes
  Citizen.SetTimeout(600000, function() -- 10 minutes
    if playerRentedVehicles[src] and playerRentedVehicles[src][vehicleName] then
      playerRentedVehicles[src][vehicleName] = nil
      print(('[KOTH] Rental expired for player %d - %s'):format(src, vehicleName))
    end
  end)
end)

-- Command to spawn owned vehicles
RegisterCommand('myvehicles', function(source, args, rawCommand)
  local src = source
  if src == 0 then return end
  
  if not playerOwnedVehicles[src] or next(playerOwnedVehicles[src]) == nil then
    TriggerClientEvent('chat:addMessage', src, {
      color = {255, 0, 0},
      multiline = true,
      args = {"[KOTH]", "You don't own any vehicles. Purchase vehicles to own them permanently!"}
    })
    return
  end
  
  local ownedList = {}
  for vehicleName, _ in pairs(playerOwnedVehicles[src]) do
    table.insert(ownedList, vehicleName)
  end
  
  TriggerClientEvent('chat:addMessage', src, {
    color = {0, 255, 0},
    multiline = true,
    args = {"[KOTH]", "Your owned vehicles: " .. table.concat(ownedList, ", ")}
  })
end, false)

-- Command to spawn a specific owned vehicle
RegisterCommand('spawnvehicle', function(source, args, rawCommand)
  local src = source
  if src == 0 then return end
  
  if not args[1] then
    TriggerClientEvent('chat:addMessage', src, {
      color = {255, 0, 0},
      multiline = true,
      args = {"[KOTH]", "Usage: /spawnvehicle [vehicle name]"}
    })
    return
  end
  
  local vehicleName = args[1]
  
  -- Check if player owns this vehicle
  if playerOwnedVehicles[src] and playerOwnedVehicles[src][vehicleName] then
    SpawnVehicleForPlayer(src, vehicleName, 'owned', 0)
    TriggerClientEvent('koth:purchaseResult', src, true, ('Spawned your owned %s'):format(vehicleName))
  else
    TriggerClientEvent('chat:addMessage', src, {
      color = {255, 0, 0},
      multiline = true,
      args = {"[KOTH]", "You don't own this vehicle! Use /myvehicles to see your owned vehicles."}
    })
  end
end, false)

-- Server-side vehicle spawning function for networking
function SpawnVehicleForPlayer(playerId, vehicleName, purchaseType, price)
  print(('[KOTH] Requesting client to spawn vehicle: %s for player %d'):format(vehicleName, playerId))
  
  -- Notify the client to spawn the vehicle
  TriggerClientEvent('koth:spawnVehicle', playerId, vehicleName, purchaseType, price)
end

-- Add new server events for vehicle shop money requests
RegisterNetEvent('koth:getMoneyForVehicleShop', function(vehicles)
  local src = source
  local playerName = GetPlayerName(src)
  
  print(('[KOTH] Vehicle shop money request from %s'):format(playerName))
  
  -- Get fresh player data
  if playerData[src] and playerData[src].money then
    local playerMoney = playerData[src].money
    print(('[KOTH] Using cached data - Player %s has $%d'):format(playerName, playerMoney))
    
    -- Send owned vehicles list as well
    local ownedList = {}
    if playerOwnedVehicles[src] then
      for vehicleName, _ in pairs(playerOwnedVehicles[src]) do
        table.insert(ownedList, vehicleName)
      end
    end
    
    TriggerClientEvent('koth:showVehicleShopWithMoney', src, {
      vehicles = vehicles,
      money = playerMoney
    })
    
    TriggerClientEvent('koth:updateOwnedVehicles', src, ownedList)
  else
    print(('[KOTH] No cached data for %s, loading from database...'):format(playerName))
    LoadPlayerData(src)
    
    -- Wait for data to load then send
    Citizen.SetTimeout(1000, function()
      local money = playerData[src] and playerData[src].money or 0
      local ownedList = {}
      if playerOwnedVehicles[src] then
        for vehicleName, _ in pairs(playerOwnedVehicles[src]) do
          table.insert(ownedList, vehicleName)
        end
      end
      TriggerClientEvent('koth:showVehicleShopWithMoney', src, {
        vehicles = vehicles,
        money = money
      })
      TriggerClientEvent('koth:updateOwnedVehicles', src, ownedList)
    end)
  end
end)

RegisterNetEvent('koth:getDataForClassShop', function(classes)
  local src = source
  local playerName = GetPlayerName(src)
  
  print(('[KOTH] Class shop data request from %s'):format(playerName))
  
  -- Get fresh player data
  if playerData[src] and playerData[src].money then
    local playerMoney = playerData[src].money
    local playerLevel = playerData[src].level or 1
    print(('[KOTH] Using cached data - Player %s has $%d, Level %d'):format(playerName, playerMoney, playerLevel))
    
    TriggerClientEvent('koth:showClassShopWithData', src, {
      classes = classes,
      money = playerMoney,
      level = playerLevel
    })
  else
    print(('[KOTH] No cached data for %s, loading from database...'):format(playerName))
    LoadPlayerData(src)
    
    -- Wait for data to load then send
    Citizen.SetTimeout(1000, function()
      local money = playerData[src] and playerData[src].money or 0
      local level = playerData[src] and playerData[src].level or 1
      TriggerClientEvent('koth:showClassShopWithData', src, {
        classes = classes,
        money = money,
        level = level
      })
    end)
  end
end)

-- Class selection handler (stub)
RegisterNetEvent('koth:selectClass', function(classId)
  local src = source
  print(('[KOTH] Player %d selected class: %s'):format(src, classId or 'none'))
  -- TODO: Add loadout/class logic
end)

-- Weapon/loadout selection handler (supports both buy and rent)
RegisterNetEvent('koth:selectLoadout', function(classId, weapon, price, purchaseType)
  local src = source
  local weaponPrice = tonumber(price or 0)
  local isRental = purchaseType == 'rent'

  print(('[KOTH] Player %d selected weapon %s for class %s (%s for $%d)'):format(src, weapon or 'none', classId or 'none', purchaseType or 'buy', weaponPrice))
  print(('[KOTH] DEBUG: playerData type: %s, playerData[%d] exists: %s'):format(type(playerData), src, tostring(playerData[src] ~= nil)))

  if not weapon or not classId then
    TriggerClientEvent('koth:purchaseResult', src, false, 'Invalid weapon selection')
    return
  end

  -- Ensure playerData table exists
  if not playerData then
    print('[KOTH] CRITICAL ERROR: playerData table is nil!')
    playerData = {}
  end

  -- Check if player has enough money (free weapons are allowed)
  if weaponPrice > 0 then
    print(('[KOTH] DEBUG: Checking money for player %d, playerData[%d] = %s'):format(src, src, tostring(playerData[src])))

    if not playerData[src] then
      print(('[KOTH] Player data not loaded for %d, attempting to load...'):format(src))
      -- Try to load player data immediately
      LoadPlayerData(src)

      -- Give it a moment to load and then check again
      Citizen.SetTimeout(1000, function()
        if not playerData[src] then
          print(('[KOTH] Failed to load player data for %d after retry'):format(src))
          TriggerClientEvent('koth:purchaseResult', src, false, 'Player data not loaded. Please try again.')
        else
          -- Retry the weapon purchase now that data is loaded
          print(('[KOTH] Player data loaded for %d, retrying weapon purchase'):format(src))
          TriggerEvent('koth:selectLoadout', classId, weapon, price, purchaseType)
        end
      end)
      return
    end

    if not playerData[src].money then
      print(('[KOTH] Player %d has invalid money data, playerData[%d] = %s'):format(src, src, json.encode(playerData[src])))
      TriggerClientEvent('koth:purchaseResult', src, false, 'Invalid player data. Please try again.')
      return
    end

    local currentMoney = tonumber(playerData[src].money) or 0
    print(('[KOTH] Player %d money check: has $%d, needs $%d'):format(src, currentMoney, weaponPrice))

    if currentMoney < weaponPrice then
      TriggerClientEvent('koth:purchaseResult', src, false, 'Not enough money')
      print(('[KOTH] Player %d cannot afford weapon %s ($%d) - has $%d'):format(src, weapon, weaponPrice, currentMoney))
      return
    end

    -- Deduct money and save to database
    playerData[src].money = currentMoney - weaponPrice
    SavePlayerData(src)

    print(('[KOTH] Player %d %s weapon %s for $%d - remaining: $%d'):format(src, isRental and 'rented' or 'bought', weapon, weaponPrice, playerData[src].money))

    -- Update client HUD with new money
    TriggerClientEvent('koth:updatePlayerData', src, playerData[src])
  end

  -- Give the player the selected weapon
  TriggerClientEvent('koth:giveWeapon', src, weapon, classId, weaponPrice, isRental)

  if weaponPrice > 0 then
    local actionText = isRental and 'Rented' or 'Purchased'
    TriggerClientEvent('koth:purchaseResult', src, true, ('%s %s for $%d'):format(actionText, weapon:gsub('WEAPON_', ''):gsub('_', ' '), weaponPrice))
  end
  
  -- Close the weapon shop UI after successful purchase/rental
  TriggerClientEvent('koth:closeWeaponShop', src)
end)

-- DATABASE CONFIGURATION
-- Configured for your Zap Hosting MySQL database
local DB_CONFIG = {
  host = "mysql-mariadb-oce02-11-101.zap-srv.com",
  port = 3306,
  database = "zap1190649-1",
  username = "zap1190649-1",
  password = "tYXDWyEmvqykO75w"
}

-- MONEY AND XP SYSTEM
-- playerData moved to top of file

-- Get player identifiers
function GetPlayerIdentifiers(source)
  local identifiers = {
    txid = nil,
    steam = nil,
    discord = nil
  }

  for i = 0, GetNumPlayerIdentifiers(source) - 1 do
    local id = GetPlayerIdentifier(source, i)

    if string.find(id, "steam:") then
      identifiers.steam = id
    elseif string.find(id, "discord:") then
      identifiers.discord = id
    end
  end

  -- Use license as primary identifier (TX Admin compatible)
  identifiers.txid = GetPlayerIdentifierByType(source, 'license') or identifiers.steam or ('temp_' .. tostring(source))

  return identifiers
end

-- Load player data from database (no team restoration)
function LoadPlayerData(source)
  print(('[KOTH] LoadPlayerData called with source: %s (type: %s)'):format(tostring(source), type(source)))

  local identifiers = GetPlayerIdentifiers(source)
  local playerName = GetPlayerName(source)

  if not identifiers.txid then
    print(('[KOTH] ERROR: Could not get identifier for player %d'):format(source))
    return
  end

  -- Load from database
  print(('[KOTH] Attempting to load player data for %s (TXID: %s)'):format(playerName, identifiers.txid))

  exports.oxmysql:execute('SELECT * FROM koth_players WHERE txid = ?', {identifiers.txid}, function(result)
    print(('[KOTH] Database query completed for %s, result type: %s'):format(playerName, type(result)))

    if result then
      print(('[KOTH] Database result length: %d'):format(#result))
      if result[1] then
        print(('[KOTH] Found existing player data: Money=%s, XP=%s, Level=%s'):format(
          tostring(result[1].money), tostring(result[1].xp), tostring(result[1].level)))
      end
    end

    if result and result[1] then
      -- Player exists, load data
      playerData[source] = result[1]
      print(('[KOTH] Loaded existing player data for %s - Money: $%d, XP: %d, Level: %d'):format(
        playerName, result[1].money, result[1].xp, result[1].level))

      -- Load owned vehicles for player
      exports.oxmysql:execute('SELECT vehicle_name FROM koth_player_vehicles WHERE txid = ?', {identifiers.txid}, function(vehicleResults)
        playerOwnedVehicles[source] = {}
        if vehicleResults then
          for _, row in ipairs(vehicleResults) do
            playerOwnedVehicles[source][row.vehicle_name] = true
          end
          print(('[KOTH] Loaded %d owned vehicles for player %s'):format(#vehicleResults, playerName))
        else
          print(('[KOTH] No owned vehicles found for player %s'):format(playerName))
        end
      end)

      -- Send data immediately
      TriggerClientEvent('koth:updatePlayerData', source, playerData[source])
      print(('[KOTH] Sent existing player data to client for %s'):format(playerName))

    else
      -- New player, create record
      print(('[KOTH] Creating new player record for %s'):format(playerName))
      exports.oxmysql:execute('INSERT INTO koth_players (txid, steam_id, discord_id, player_name, money, xp, level) VALUES (?, ?, ?, ?, ?, ?, ?)', {
        identifiers.txid,
        identifiers.steam,
        identifiers.discord,
        playerName,
        1000,
        0,
        1
      }, function(insertId)
        -- Create local data
        playerData[source] = {
          id = insertId,
          txid = identifiers.txid,
          steam_id = identifiers.steam,
          discord_id = identifiers.discord,
          player_name = playerName,
          money = 1000,
          xp = 0,
          level = 1,
          kills = 0,
          deaths = 0,
          zone_kills = 0
        }

        print(('[KOTH] Created new player record for %s - Money: $%d, XP: %d, Level: %d'):format(
          playerName, 1000, 0, 1))

        -- Send data immediately
        TriggerClientEvent('koth:updatePlayerData', source, playerData[source])
        print(('[KOTH] Sent new player data to client for %s'):format(playerName))
      end)
    end
  end)

  -- Fallback: Create local data if database fails
  Citizen.SetTimeout(10000, function() -- Wait 10 seconds
    if not playerData[source] then
      print(('[KOTH] Database failed, creating fallback data for %s'):format(playerName))
      playerData[source] = {
        txid = identifiers.txid,
        steam_id = identifiers.steam,
        discord_id = identifiers.discord,
        player_name = playerName,
        money = 1000,
        xp = 0,
        level = 1,
        kills = 0,
        deaths = 0,
        zone_kills = 0
      }
      TriggerClientEvent('koth:updatePlayerData', source, playerData[source])
    end
  end)
end

-- Save player data to database (no team saving)
function SavePlayerData(source)
  if not playerData[source] then return end

  local data = playerData[source]

  exports.oxmysql:execute('UPDATE koth_players SET money = ?, xp = ?, level = ?, kills = ?, deaths = ?, zone_kills = ?, player_name = ? WHERE txid = ?', {
    data.money,
    data.xp,
    data.level,
    data.kills,
    data.deaths,
    data.zone_kills,
    data.player_name,
    data.txid
  }, function(result)
    if result and (type(result) == 'number' and result > 0) or (type(result) == 'table' and result.affectedRows and result.affectedRows > 0) then
      print(('[KOTH] Saved data for player %s'):format(data.player_name))
    end
  end)
end

-- Player data request event
RegisterNetEvent('koth:requestPlayerData', function()
  local source = source
  print(('[KOTH] Player data requested by %s'):format(GetPlayerName(source)))

  if playerData[source] then
    TriggerClientEvent('koth:updatePlayerData', source, playerData[source])
    print(('[KOTH] Sent requested player data to %s'):format(GetPlayerName(source)))
  else
    print(('[KOTH] No player data available for %s, loading from database...'):format(GetPlayerName(source)))
    LoadPlayerData(source)
  end
end)

-- Direct money request for weapon shop (guaranteed fix)
RegisterNetEvent('koth:getMoneyForWeaponShop', function(classId, weapons)
  local source = source
  local playerName = GetPlayerName(source)

  print(('[KOTH] Direct money request for weapon shop by %s'):format(playerName))

  -- Ensure player data is loaded
  if not playerData[source] then
    print(('[KOTH] Player data not loaded for %d, loading now...'):format(source))
    LoadPlayerData(source)
    
    -- Wait a moment for data to load
    Citizen.SetTimeout(500, function()
      if playerData[source] then
        TriggerClientEvent('koth:showWeaponShopWithMoney', source, {
          class = classId,
          weapons = weapons,
          money = playerData[source].money or 0
        })
      else
        -- Fallback if data still not loaded
        TriggerClientEvent('koth:showWeaponShopWithMoney', source, {
          class = classId,
          weapons = weapons,
          money = 0
        })
      end
    end)
    return
  end

  -- Send data immediately if available
  TriggerClientEvent('koth:showWeaponShopWithMoney', source, {
    class = classId,
    weapons = weapons,
    money = playerData[source].money or 0
  })
end)

-- Player connecting event
AddEventHandler('playerConnecting', function()
  local source = source
  print(('[KOTH] playerConnecting event - source: %s (type: %s)'):format(tostring(source), type(source)))
end)

-- Player joined event (better for loading data)
AddEventHandler('playerJoining', function()
  local source = source
  print(('[KOTH] playerJoining event - source: %s'):format(tostring(source)))
  -- Load data immediately
  LoadPlayerData(source)
end)

-- Also load on player spawn for reliability
RegisterNetEvent('playerSpawned', function()
  local source = source
  if not playerData[source] then
    print(('[KOTH] Player spawned without data, loading for: %s'):format(tostring(source)))
    LoadPlayerData(source)
  end
end)

-- Load data when client is ready (new event)
RegisterNetEvent('koth:clientReady', function()
  local source = source
  print(('[KOTH] Client ready event from player %s'):format(GetPlayerName(source)))
  
  -- Load player data if not already loaded
  if not playerData[source] then
    print(('[KOTH] Loading data for ready client %s'):format(GetPlayerName(source)))
    LoadPlayerData(source)
  else
    -- If data already loaded, send it immediately
    print(('[KOTH] Sending existing data to ready client %s'):format(GetPlayerName(source)))
    TriggerClientEvent('koth:updatePlayerData', source, playerData[source])
  end
  
  -- Also send current team counts and zone points
  TriggerClientEvent('koth:updateTeamCounts', source, teamCounts)
  TriggerClientEvent('koth:updateZonePoints', source, zonePoints)
end)

-- Backup: Resource start event for existing players
AddEventHandler('onResourceStart', function(resourceName)
  if GetCurrentResourceName() ~= resourceName then return end

  print('[KOTH] Resource started, loading data for existing players...')
  -- Load immediately for all players
  for _, playerId in ipairs(GetPlayers()) do
    print(('[KOTH] Loading data for existing player: %s'):format(playerId))
    LoadPlayerData(tonumber(playerId))
  end
end)

-- KOTH ZONE SYSTEM
local kothZone = {
  controllingTeam = nil, -- 'red', 'blue', 'green', or nil for neutral
  captureProgress = 0.0, -- 0.0 to 100.0
  captureRate = 1.0, -- Points per second
  captureThreshold = 10.0, -- Only 10 seconds to capture
  decayRate = 0.5, -- Points lost per second when no players present
  contestedDecayRate = 0.25, -- Points lost per second when contested
  playersInZone = {
    red = 0,
    blue = 0,
    green = 0
  },
  dominantTeam = nil, -- Team with most players
  isContested = false -- True when multiple teams present
}

-- Track players in KOTH zone
RegisterNetEvent('koth:playerEnteredZone', function(team)
  local source = source
  if not team then return end

  -- Increment player count for team
  kothZone.playersInZone[team] = (kothZone.playersInZone[team] or 0) + 1
  print(('[KOTH] Player %s entered zone (Team: %s)'):format(source, team))

  -- Update zone status
  UpdateKothZoneStatus()
end)

RegisterNetEvent('koth:playerLeftZone', function(team)
  local source = source
  if not team then return end

  -- Decrement player count for team
  if kothZone.playersInZone[team] and kothZone.playersInZone[team] > 0 then
    kothZone.playersInZone[team] = kothZone.playersInZone[team] - 1
  end
  print(('[KOTH] Player %s left zone (Team: %s)'):format(source, team))

  -- Update zone status
  UpdateKothZoneStatus()
end)

-- Clean up when player disconnects
AddEventHandler('playerDropped', function()
  local source = source
  local playerTeam = GetPlayerTeam(source)

  if playerTeam then
    -- Remove from team tracking
    playerTeams[source] = nil
    print(('[KOTH] Player %s disconnected from team %s'):format(source, playerTeam))

    -- Update team counts
    UpdateTeamCounts()

    -- Remove from KOTH zone if present
    if kothZone.playersInZone[playerTeam] and kothZone.playersInZone[playerTeam] > 0 then
      kothZone.playersInZone[playerTeam] = kothZone.playersInZone[playerTeam] - 1
      print(('[KOTH] Player %s removed from KOTH zone count'):format(source))
      UpdateKothZoneStatus()
    end
  end

  -- Clean up vehicle ownership tracking
  playerRentedVehicles[source] = nil -- Remove all rentals on disconnect
  -- Note: playerOwnedVehicles[source] is kept for when they reconnect

  -- Save player data and clean up
  SavePlayerData(source)
  playerData[source] = nil
end)

-- Update KOTH zone status (dominant team, contested status)
function UpdateKothZoneStatus()
  local teamsInZone = {}
  local totalPlayers = 0

  -- Count teams and players in zone
  for team, count in pairs(kothZone.playersInZone) do
    if count > 0 then
      table.insert(teamsInZone, team)
      totalPlayers = totalPlayers + count
    end
  end

  local teamsPresent = #teamsInZone
  local isContested = teamsPresent > 1

  print(('[KOTH] Zone update - Teams present: %d, Total players: %d, Contested: %s'):format(
    teamsPresent, totalPlayers, tostring(isContested)
  ))

  -- Handle zone control based on teams present
  if teamsPresent == 0 then
    -- No teams in zone - becomes neutral
    if kothZone.controllingTeam ~= nil then
      local wasControlled = kothZone.controllingTeam
      kothZone.controllingTeam = nil
      kothZone.captureProgress = 0
      kothZone.dominantTeam = nil
      kothZone.isContested = false
      print('[KOTH] Zone is now neutral - no teams present')
      TriggerClientEvent('koth:zoneControlChanged', -1, nil)
      
      -- Notify zone is now neutral
      TriggerClientEvent('chat:addMessage', -1, {
        color = {128, 128, 128},
        multiline = false,
        args = {'[ZONE]', 'Zone is now neutral - no teams present'}
      })
    end
  elseif teamsPresent == 1 then
    -- Single team in zone - instant control
    local singleTeam = teamsInZone[1]
    if kothZone.controllingTeam ~= singleTeam then
      local wasContested = kothZone.isContested
      kothZone.controllingTeam = singleTeam
      kothZone.captureProgress = 0
      kothZone.dominantTeam = singleTeam
      kothZone.isContested = false
      print(('[KOTH] Zone instantly controlled by %s team!'):format(singleTeam))
      TriggerClientEvent('koth:zoneControlChanged', -1, singleTeam)
      
      -- Notify all players in chat with team color
      local teamColors = {
        red = {255, 0, 0},
        green = {0, 255, 0},
        blue = {0, 100, 255}
      }
      local color = teamColors[singleTeam] or {255, 255, 255}
      local message = wasContested and ('%s team has taken the zone!'):format(singleTeam:upper()) or ('%s team has control of the zone!'):format(singleTeam:upper())
      TriggerClientEvent('chat:addMessage', -1, {
        color = color,
        multiline = false,
        args = {'[ZONE]', message}
      })
    end
    kothZone.dominantTeam = singleTeam
    kothZone.isContested = false
  else
    -- Multiple teams - check for dominant team
    local maxPlayers = 0
    local dominantTeam = nil
    local teamPlayerCounts = {}
    
    -- Find team with most players
    for team, count in pairs(kothZone.playersInZone) do
      if count > 0 then
        teamPlayerCounts[team] = count
        if count > maxPlayers then
          maxPlayers = count
          dominantTeam = team
        end
      end
    end
    
    -- Check if one team has clear majority
    local hasMajority = true
    for team, count in pairs(teamPlayerCounts) do
      if team ~= dominantTeam and count == maxPlayers then
        hasMajority = false
        break
      end
    end
    
    if hasMajority and dominantTeam then
      -- One team has more players - they control
      if kothZone.controllingTeam ~= dominantTeam then
        local wasContested = kothZone.isContested
        kothZone.controllingTeam = dominantTeam
        kothZone.captureProgress = 0
        kothZone.dominantTeam = dominantTeam
        kothZone.isContested = false
        print(('[KOTH] Zone controlled by %s team with majority!'):format(dominantTeam))
        TriggerClientEvent('koth:zoneControlChanged', -1, dominantTeam)
        
        -- Notify with team details
        local teamColors = {
          red = {255, 0, 0},
          green = {0, 255, 0},
          blue = {0, 100, 255}
        }
        local color = teamColors[dominantTeam] or {255, 255, 255}
        
        -- Build player count string
        local countStr = ''
        for team, count in pairs(teamPlayerCounts) do
          if countStr ~= '' then countStr = countStr .. ' vs ' end
          countStr = countStr .. count .. ' ' .. team:upper()
        end
        
        local message = ('%s team has taken the zone! (%s)'):format(dominantTeam:upper(), countStr)
        TriggerClientEvent('chat:addMessage', -1, {
          color = color,
          multiline = false,
          args = {'[ZONE]', message}
        })
      end
      kothZone.dominantTeam = dominantTeam
      kothZone.isContested = false
    else
      -- Equal numbers or no clear majority - contested
      local wasContested = kothZone.isContested
      kothZone.isContested = true
      kothZone.dominantTeam = nil
      
      if not wasContested then
        -- Just became contested
        print('[KOTH] Zone is now contested by multiple teams!')
        
        -- Build contested message
        local countStr = ''
        for team, count in pairs(teamPlayerCounts) do
          if countStr ~= '' then countStr = countStr .. ' vs ' end
          countStr = countStr .. count .. ' ' .. team:upper()
        end
        
        TriggerClientEvent('chat:addMessage', -1, {
          color = {255, 255, 0},
          multiline = false,
          args = {'[ZONE]', ('Zone contested! (%s)'):format(countStr)}
        })
      end
    end
  end

  -- Broadcast zone status to all clients
  TriggerClientEvent('koth:updateZoneStatus', -1, {
    controllingTeam = kothZone.controllingTeam,
    captureProgress = kothZone.captureProgress,
    captureThreshold = kothZone.captureThreshold,
    dominantTeam = kothZone.dominantTeam,
    isContested = kothZone.isContested,
    playersInZone = kothZone.playersInZone
  })
end

-- KOTH capture processing loop
Citizen.CreateThread(function()
  while true do
    Citizen.Wait(1000) -- Update every second

    -- Only process if zone is controlled and not contested
    if kothZone.controllingTeam and not kothZone.isContested then
      -- Increase capture progress
      kothZone.captureProgress = kothZone.captureProgress + kothZone.captureRate

      -- Award points when progress reaches threshold
      if kothZone.captureProgress >= kothZone.captureThreshold then
        kothZone.captureProgress = 0
        
        -- Update zone points
        zonePoints[kothZone.controllingTeam] = (zonePoints[kothZone.controllingTeam] or 0) + 1
        
        -- Broadcast updated points immediately
        TriggerClientEvent('koth:updateZonePoints', -1, zonePoints)
        
        print(('[KOTH] The %s team earned 1 point for controlling the zone! Total: %d'):format(
          kothZone.controllingTeam, zonePoints[kothZone.controllingTeam]))
        
        -- Notify point award with team color
        local teamColors = {
          red = {255, 0, 0},
          green = {0, 255, 0},
          blue = {0, 100, 255}
        }
        local color = teamColors[kothZone.controllingTeam] or {255, 255, 255}
        local pointMessage = ('%s team earned 1 point! (Total: %d)'):format(kothZone.controllingTeam:upper(), zonePoints[kothZone.controllingTeam])
        TriggerClientEvent('chat:addMessage', -1, {
          color = color,
          multiline = false,
          args = {'[ZONE]', pointMessage}
        })
      end

      -- Always update zone status to show progress
      TriggerClientEvent('koth:updateZoneStatus', -1, {
        controllingTeam = kothZone.controllingTeam,
        captureProgress = kothZone.captureProgress,
        captureThreshold = kothZone.captureThreshold,
        dominantTeam = kothZone.dominantTeam,
        isContested = kothZone.isContested,
        playersInZone = kothZone.playersInZone
      })
    elseif kothZone.isContested then
      -- When contested, reset progress and show contested state
      if kothZone.captureProgress > 0 then
        kothZone.captureProgress = 0
      end
      
      -- Update clients about contested state
      TriggerClientEvent('koth:updateZoneStatus', -1, {
        controllingTeam = kothZone.controllingTeam,
        captureProgress = 0,
        captureThreshold = kothZone.captureThreshold,
        dominantTeam = nil,
        isContested = true,
        playersInZone = kothZone.playersInZone
      })
    end
  end
end)

-- PVP SYSTEM SERVER-SIDE
AddEventHandler('playerConnecting', function()
  local source = source

  -- Enable PVP for connecting player
  Citizen.SetTimeout(5000, function() -- Wait for player to fully load
    TriggerClientEvent('koth:enablePVP', source)
  end)
end)

-- Enable PVP for all players when resource starts
AddEventHandler('onResourceStart', function(resourceName)
  if GetCurrentResourceName() ~= resourceName then return end

  print('[KOTH] Enabling PVP for all players...')

  -- Enable PVP for all currently connected players
  for _, playerId in ipairs(GetPlayers()) do
    TriggerClientEvent('koth:enablePVP', playerId)
  end
end)

-- PVP enable event
RegisterNetEvent('koth:enablePVP', function()
  local source = source
  print(('[KOTH] PVP enabled for player %d'):format(source))
end)

-- Debug command to check player stats
RegisterCommand('checkstats', function(source, args, rawCommand)
  if source == 0 then
    print('[KOTH] This command can only be used by players')
    return
  end

  if not playerData[source] then
    TriggerClientEvent('chat:addMessage', source, {
      color = {255, 0, 0},
      multiline = true,
      args = {"[KOTH]", "Player data not loaded! Try rejoining the server."}
    })
    print(('[KOTH] Player %d requested stats but data not loaded'):format(source))
    return
  end

  local data = playerData[source]
  TriggerClientEvent('chat:addMessage', source, {
    color = {0, 255, 0},
    multiline = true,
    args = {"[KOTH Stats]", string.format("Money: $%d | XP: %d | Level: %d | Kills: %d | Deaths: %d",
      data.money, data.xp, data.level, data.kills, data.deaths)}
  })

  print(('[KOTH] Player %d (%s) stats - Money: $%d | XP: %d | Level: %d'):format(source, data.player_name, data.money, data.xp, data.level))
end, false)

-- Calculate level from XP
function CalculateLevel(xp)
  local levels = {
    {level = 1, required = 0},
    {level = 2, required = 100},
    {level = 3, required = 250},
    {level = 4, required = 500},
    {level = 5, required = 1000},
    {level = 6, required = 1750},
    {level = 7, required = 2750},
    {level = 8, required = 4000},
    {level = 9, required = 6000},
    {level = 10, required = 8500},
    {level = 11, required = 11000},
    {level = 12, required = 14000},
    {level = 13, required = 17500},
    {level = 14, required = 21500},
    {level = 15, required = 26000},
    {level = 16, required = 31000},
    {level = 17, required = 36500},
    {level = 18, required = 42500},
    {level = 19, required = 49000},
    {level = 20, required = 56000},
    {level = 25, required = 100000},
    {level = 30, required = 150000},
    {level = 40, required = 250000},
    {level = 50, required = 400000}
  }

  local currentLevel = 1
  for _, levelData in ipairs(levels) do
    if xp >= levelData.required then
      currentLevel = levelData.level
    else
      break
    end
  end
  
  -- If XP is beyond level 50, calculate level based on formula
  if xp > 400000 then
    -- After level 50, each level requires 10000 more XP
    local extraXP = xp - 400000
    local extraLevels = math.floor(extraXP / 10000)
    currentLevel = 50 + extraLevels
  end

  return currentLevel
end

-- Award money and XP for kills - FIXED VERSION WITH EXTENSIVE DEBUG
function AwardKillReward(killerSource, victimSource, inZone)
  print('=== [KOTH SERVER] AWARD KILL REWARD START ===')
  print(('[KOTH] AwardKillReward called - Killer: %s (type: %s) | Victim: %s (type: %s) | In Zone: %s'):format(
    tostring(killerSource), type(killerSource), tostring(victimSource), type(victimSource), tostring(inZone)))

  -- Convert to number if needed (FiveM sometimes passes strings)
  local killerSourceNum = tonumber(killerSource)
  local victimSourceNum = tonumber(victimSource)

  print(('[KOTH] Converted IDs - Killer: %s | Victim: %s'):format(tostring(killerSourceNum), tostring(victimSourceNum)))

  -- Prevent self-kills
  if killerSourceNum == victimSourceNum then
    print(('[KOTH] Self-kill detected - no reward given'):format())
    return
  end

  if not playerData[killerSourceNum] then
    print(('[KOTH] CRITICAL ERROR: No player data found for killer %s - loading data...'):format(killerSourceNum))
    LoadPlayerData(killerSourceNum)
    -- Wait a moment for data to load
    Citizen.SetTimeout(1000, function()
      if playerData[killerSourceNum] then
        print(('[KOTH] Killer data loaded, retrying reward...'):format())
        AwardKillReward(killerSourceNum, victimSourceNum, inZone)
      else
        print(('[KOTH] FAILED to load killer data after retry!'):format())
      end
    end)
    return
  end

  if not playerData[victimSourceNum] then
    print(('[KOTH] CRITICAL ERROR: No player data found for victim %s'):format(victimSourceNum))
    return
  end

  -- Check if killer and victim are on different teams (with fallback)
  local killerTeam = GetPlayerTeam(killerSourceNum)
  local victimTeam = GetPlayerTeam(victimSourceNum)
  
  print(('[KOTH] Team check - Killer: %s team | Victim: %s team'):format(tostring(killerTeam), tostring(victimTeam)))
  
  -- If team data is missing, still allow the kill (better than blocking all kills)
  if killerTeam and victimTeam and killerTeam == victimTeam then
    print(('[KOTH] Teamkill detected - no reward given (both on %s team)'):format(killerTeam))
    return
  end

  print(('[KOTH] Processing kill reward - Valid opposite team kill'):format())

  -- ENHANCED REWARD VALUES as requested:
  -- Normal kill (opposite team): $50 and 50 XP
  -- Zone kill (opposite team): $150 and 150 XP (3x multiplier)
  local baseXP = 50
  local baseMoney = 50
  local xpReward = inZone and 150 or baseXP
  local moneyReward = inZone and 150 or baseMoney

  print(('[KOTH] Reward calculation - Base XP: %d | Base Money: $%d | Zone Kill: %s'):format(baseXP, baseMoney, tostring(inZone)))
  print(('[KOTH] Final rewards - XP: %d | Money: $%d'):format(xpReward, moneyReward))

  -- Update killer stats (use converted numbers)
  local killerData = playerData[killerSourceNum]
  local oldLevel = killerData.level
  local oldMoney = killerData.money
  local oldXP = killerData.xp
  local oldKills = killerData.kills

  print(('[KOTH] Killer BEFORE - Money: $%d | XP: %d | Level: %d | Kills: %d'):format(
    oldMoney, oldXP, oldLevel, oldKills))

  killerData.money = killerData.money + moneyReward
  killerData.xp = killerData.xp + xpReward
  killerData.kills = killerData.kills + 1

  if inZone then
    killerData.zone_kills = killerData.zone_kills + 1
    print(('[KOTH] Zone kill recorded - new zone_kills: %d'):format(killerData.zone_kills))
  end

  -- Calculate new level
  killerData.level = CalculateLevel(killerData.xp)

  print(('[KOTH] Killer AFTER - Money: $%d | XP: %d | Level: %d | Kills: %d'):format(
    killerData.money, killerData.xp, killerData.level, killerData.kills))

  -- Update victim stats (use converted numbers)
  local victimData = playerData[victimSourceNum]
  local oldDeaths = victimData.deaths
  victimData.deaths = victimData.deaths + 1

  print(('[KOTH] Victim deaths updated from %d to %d'):format(oldDeaths, victimData.deaths))

  -- Prepare reward data for client
  local rewardData = {
    xp = xpReward,
    money = moneyReward,
    inZone = inZone,
    victimName = GetPlayerName(victimSourceNum),
    killerTeam = killerTeam,
    victimTeam = victimTeam
  }

  print('=== [KOTH SERVER] SENDING CLIENT EVENTS ===')
  print(('[KOTH] Sending kill reward to killer %d: %s'):format(killerSourceNum, json.encode(rewardData)))

  -- Send enhanced reward notification to killer (use converted numbers)
  TriggerClientEvent('koth:showKillReward', killerSourceNum, rewardData)

  -- Check for level up
  if killerData.level > oldLevel then
    local levelUpData = {
      newLevel = killerData.level,
      oldLevel = oldLevel
    }
    print(('[KOTH] Sending level up to killer %d: %s'):format(killerSourceNum, json.encode(levelUpData)))
    TriggerClientEvent('koth:levelUp', killerSourceNum, levelUpData)
  end

  print(('[KOTH] Sending updated player data to killer %d'):format(killerSourceNum))
  print(('[KOTH] Killer data being sent: Money=$%d, XP=%d, Level=%d'):format(
    killerData.money, killerData.xp, killerData.level))

  -- Update client data (use converted numbers) - FORCE UPDATE
  TriggerClientEvent('koth:updatePlayerData', killerSourceNum, killerData)
  
  print(('[KOTH] Sending updated player data to victim %d'):format(victimSourceNum))
  TriggerClientEvent('koth:updatePlayerData', victimSourceNum, victimData)

  print('=== [KOTH SERVER] SAVING TO DATABASE ===')
  -- Save data (use converted numbers)
  SavePlayerData(killerSourceNum)
  SavePlayerData(victimSourceNum)

  print('=== [KOTH SERVER] KILL REWARD COMPLETE ===')
  print(('[KOTH] FINAL SUMMARY: %s (%s) killed %s (%s) | XP: +%d | Money: +$%d | Zone: %s'):format(
    GetPlayerName(killerSourceNum),
    tostring(killerTeam),
    GetPlayerName(victimSourceNum),
    tostring(victimTeam),
    xpReward,
    moneyReward,
    inZone and 'YES' or 'NO'
  ))
  print('=== [KOTH SERVER] AWARD KILL REWARD END ===')
end

-- Kill detection event
RegisterNetEvent('koth:playerKilled', function(killerSource, victimSource, inZone)
  print('=== [KOTH SERVER] KILL EVENT RECEIVED ===')
  print(('[KOTH] Raw data - Killer: %s (type: %s) | Victim: %s (type: %s) | In Zone: %s'):format(
    tostring(killerSource), type(killerSource), tostring(victimSource), type(victimSource), tostring(inZone)))

  -- Get player names for debugging
  local killerName = 'Unknown'
  local victimName = 'Unknown'
  
  if killerSource and tonumber(killerSource) then
    killerName = GetPlayerName(tonumber(killerSource)) or 'Unknown'
  end
  if victimSource and tonumber(victimSource) then
    victimName = GetPlayerName(tonumber(victimSource)) or 'Unknown'
  end
  
  print(('[KOTH] Player names - Killer: %s | Victim: %s'):format(killerName, victimName))

  -- Validate killer and victim IDs
  local killerIdNum = tonumber(killerSource)
  local victimIdNum = tonumber(victimSource)

  print(('[KOTH] Converted IDs - Killer: %s | Victim: %s'):format(tostring(killerIdNum), tostring(victimIdNum)))

  if killerIdNum and victimIdNum and killerIdNum ~= victimIdNum then
    -- Check if killer and victim are connected players
    local killerPlayer = GetPlayerPed(killerIdNum)
    local victimPlayer = GetPlayerPed(victimIdNum)
    
    print(('[KOTH] Player peds - Killer: %s | Victim: %s'):format(tostring(killerPlayer), tostring(victimPlayer)))

    if killerPlayer and killerPlayer ~= 0 and victimPlayer and victimPlayer ~= 0 then
      print('=== [KOTH SERVER] CHECKING PLAYER DATA ===')
      
      -- Check if player data exists
      local killerHasData = playerData[killerIdNum] ~= nil
      local victimHasData = playerData[victimIdNum] ~= nil
      
      print(('[KOTH] Player data exists - Killer: %s | Victim: %s'):format(tostring(killerHasData), tostring(victimHasData)))
      
      if killerHasData then
        local kData = playerData[killerIdNum]
        print(('[KOTH] Killer data - Money: $%s | XP: %s | Level: %s | TXID: %s'):format(
          tostring(kData.money), tostring(kData.xp), tostring(kData.level), tostring(kData.txid)))
      else
        print('[KOTH] ERROR: Killer has no player data! Attempting to load...')
        LoadPlayerData(killerIdNum)
        
        -- Wait and retry
        Citizen.SetTimeout(1000, function()
          if playerData[killerIdNum] then
            print('[KOTH] Killer data loaded successfully, retrying kill reward...')
            TriggerEvent('koth:playerKilled', killerSource, victimSource, inZone)
          else
            print('[KOTH] CRITICAL ERROR: Could not load killer data!')
          end
        end)
        return
      end
      
      if victimHasData then
        local vData = playerData[victimIdNum]
        print(('[KOTH] Victim data - Money: $%s | XP: %s | Level: %s | TXID: %s'):format(
          tostring(vData.money), tostring(vData.xp), tostring(vData.level), tostring(vData.txid)))
      else
        print('[KOTH] ERROR: Victim has no player data! Attempting to load...')
        LoadPlayerData(victimIdNum)
        
        -- Wait and retry
        Citizen.SetTimeout(1000, function()
          if playerData[victimIdNum] then
            print('[KOTH] Victim data loaded successfully, retrying kill reward...')
            TriggerEvent('koth:playerKilled', killerSource, victimSource, inZone)
          else
            print('[KOTH] CRITICAL ERROR: Could not load victim data!')
          end
        end)
        return
      end
      
      print('=== [KOTH SERVER] PROCESSING KILL REWARD ===')
      AwardKillReward(killerIdNum, victimIdNum, inZone)
      
      -- TRIGGER DAILY CHALLENGE EVENTS
      print('=== [KOTH SERVER] UPDATING DAILY CHALLENGES ===')
      
      -- Track kill for killstreak challenge (specify the killer source)
      TriggerEvent('leaderboard:playerKill', killerIdNum)
      
      -- Track zone kill if applicable
      if inZone then
        print('[KOTH] Triggering zone kill challenge update for player ' .. killerIdNum)
        TriggerEvent('leaderboard:zoneKill', killerIdNum)
      end
      
      -- Track death for victim (resets killstreak)
      TriggerEvent('leaderboard:playerDied', victimIdNum)
      
      print('[KOTH] Daily challenge events triggered for killer: ' .. killerIdNum .. ' and victim: ' .. victimIdNum)
    else
      print('[KOTH] Kill event ignored - killer or victim ped invalid')
      print(('[KOTH] Killer ped: %s | Victim ped: %s'):format(tostring(killerPlayer), tostring(victimPlayer)))
    end
  else
    print('[KOTH] Kill event ignored - invalid data or self-kill')
    print(('[KOTH] Killer ID: %s | Victim ID: %s | Same player: %s'):format(
      tostring(killerIdNum), tostring(victimIdNum), tostring(killerIdNum == victimIdNum)))
  end
  
  print('=== [KOTH SERVER] KILL EVENT PROCESSING COMPLETE ===')
end)

-- Debug command to check server player data
RegisterCommand('serverstats', function(source)
  if source == 0 then
    print('[KOTH] Server player data:')
    for playerId, data in pairs(playerData) do
      print(('  Player %d: Money=$%d, Level=%d, XP=%d'):format(
        playerId, data.money or 0, data.level or 0, data.xp or 0))
    end
  else
    if playerData[source] then
      print(('[KOTH] Player %d data - Money: $%d, Level: %d, XP: %d'):format(
        source, playerData[source].money or 0, playerData[source].level or 0, playerData[source].xp or 0))
    else
      print(('[KOTH] No data found for player %d'):format(source))
    end
  end
end, true)

-- Debug command to test attachment menu
RegisterCommand('testattachments', function(source)
  if source == 0 then
    print('[KOTH] This command must be used by a player')
    return
  end
  
  -- Simulate having a weapon equipped
  local testWeaponHash = GetHashKey('WEAPON_CARBINERIFLE')
  
  print(('[KOTH] Testing attachment menu for player %d'):format(source))
  
  -- Trigger the attachment menu event
  TriggerEvent('koth:getAttachmentMenu', testWeaponHash)
  
  -- Also trigger it directly to ensure it works
  local money = 0
  if playerData[source] and playerData[source].money then
    money = playerData[source].money
  end

  -- Get weapon-specific attachments or use defaults
  local weaponName = "WEAPON_CARBINERIFLE"
  local attachments = weaponAttachments[weaponName] or defaultAttachments
  
  -- Prepare attachment list with price and full image path
  local attachmentList = {}
  for _, attachment in ipairs(attachments) do
    table.insert(attachmentList, {
      name = attachment.name,
      component = attachment.component,
      price = attachmentPrice,
      image = "nui://koth_teamsel/images of guns/" .. attachment.image  -- Use NUI protocol for images
    })
  end

  -- Send attachment menu data to client
  TriggerClientEvent('koth:showAttachmentMenu', source, {
    attachments = attachmentList,
    weaponName = "Carbine Rifle",
    money = money
  })
  
  print(('[KOTH] Sent test attachment menu to player %d'):format(source))
end, false)

-- COMPREHENSIVE TEST COMMAND FOR KILL REWARDS
RegisterCommand('testkill', function(source, args)
  if source == 0 then
    print('[KOTH] This command must be used by a player')
    return
  end
  
  local inZone = args[1] == 'zone' or args[1] == 'true'
  local xpReward = inZone and 150 or 50
  local moneyReward = inZone and 150 or 50
  
  print('=== [KOTH SERVER] TEST KILL COMMAND START ===')
  print(('[KOTH] Testing kill reward for player %d - Zone: %s, XP: %d, Money: $%d'):format(source, tostring(inZone), xpReward, moneyReward))
  
  -- Check if player data exists
  if not playerData[source] then
    print('[KOTH] ERROR: Player data not found, loading...')
    LoadPlayerData(source)
    Citizen.SetTimeout(1000, function()
      if playerData[source] then
        print('[KOTH] Player data loaded, retrying test...')
        TriggerEvent('testkill', source, args)
      else
        print('[KOTH] FAILED to load player data for test!')
      end
    end)
    return
  end
  
  print('[KOTH] Player data found, proceeding with test...')
  
  -- Send kill reward directly to test UI
  local rewardData = {
    xp = xpReward,
    money = moneyReward,
    inZone = inZone,
    victimName = 'Test Player'
  }
  
  print('[KOTH] Sending kill reward to client:', json.encode(rewardData))
  TriggerClientEvent('koth:showKillReward', source, rewardData)
  
  -- Also update player money and XP for testing
  local oldMoney = playerData[source].money or 1000
  local oldXP = playerData[source].xp or 0
  
  playerData[source].money = oldMoney + moneyReward
  playerData[source].xp = oldXP + xpReward
  playerData[source].kills = (playerData[source].kills or 0) + 1
  
  print('[KOTH] Updated player stats - Money: $' .. oldMoney .. ' -> $' .. playerData[source].money .. ', XP: ' .. oldXP .. ' -> ' .. playerData[source].xp)
  
  -- Update client HUD
  print('[KOTH] Sending updated player data to client:', json.encode(playerData[source]))
  TriggerClientEvent('koth:updatePlayerData', source, playerData[source])
  SavePlayerData(source)
  
  print('[KOTH] Test kill reward complete!')
  print('=== [KOTH SERVER] TEST KILL COMMAND END ===')
end, false)

-- Debug command to simulate a kill (original)
RegisterCommand('simulatekill', function(source, args)
  if source == 0 then
    print('[KOTH] This command must be used by a player')
    return
  end
  
  local inZone = args[1] == 'zone' or args[1] == 'true'
  
  print(('[KOTH] Simulating kill for player %d (in zone: %s)'):format(source, tostring(inZone)))
  
  -- Simulate the player killing themselves for testing
  TriggerEvent('koth:playerKilled', source, source, inZone)
  
  -- Also trigger the reward directly to test the client event
  Citizen.SetTimeout(500, function()
    local xpReward = inZone and 150 or 50
    local moneyReward = inZone and 150 or 50
    
    TriggerClientEvent('koth:showKillReward', source, {
      xp = xpReward,
      money = moneyReward,
      inZone = inZone,
      victimName = 'Test Victim'
    })
    
    print(('[KOTH] Sent kill reward to player %d - XP: %d, Money: $%d'):format(source, xpReward, moneyReward))
  end)
end, false)

-- Debug commands for testing
RegisterCommand('loaddata', function(source)
  print(('[KOTH] Manually loading data for %s'):format(GetPlayerName(source)))
  LoadPlayerData(source)
end, false)

RegisterCommand('checkdata', function(source)
  print(('[KOTH] Player data for %s: %s'):format(GetPlayerName(source), json.encode(playerData[source] or 'nil')))
  print(('[KOTH] playerData table type: %s'):format(type(playerData)))
  for k, v in pairs(playerData) do
    print(('[KOTH] playerData[%s] = %s'):format(k, json.encode(v)))
  end
end, false)

-- ADMIN PANEL INTEGRATION - Export functions for admin panel to use
exports('GivePlayerMoney', function(playerId, amount)
  local source = tonumber(playerId)
  if not source or not playerData[source] then
    print(('[KOTH] GivePlayerMoney: Invalid player ID %s or no cached data'):format(tostring(playerId)))
    return false
  end
  
  -- Update cached data
  playerData[source].money = (playerData[source].money or 0) + amount
  
  -- Save to database
  SavePlayerData(source)
  
  -- Update client HUD immediately
  TriggerClientEvent('koth:updatePlayerData', source, playerData[source])
  
  print(('[KOTH] Admin gave $%d to player %d - new balance: $%d'):format(amount, source, playerData[source].money))
  return true
end)

exports('GivePlayerXP', function(playerId, amount)
  local source = tonumber(playerId)
  if not source or not playerData[source] then
    print(('[KOTH] GivePlayerXP: Invalid player ID %s or no cached data'):format(tostring(playerId)))
    return false
  end
  
  -- Update cached data
  local oldLevel = playerData[source].level
  playerData[source].xp = (playerData[source].xp or 0) + amount
  playerData[source].level = CalculateLevel(playerData[source].xp)
  
  -- Save to database
  SavePlayerData(source)
  
  -- Check for level up
  if playerData[source].level > oldLevel then
    TriggerClientEvent('koth:levelUp', source, {
      newLevel = playerData[source].level,
      oldLevel = oldLevel
    })
  end
  
  -- Update client HUD immediately
  TriggerClientEvent('koth:updatePlayerData', source, playerData[source])
  
  print(('[KOTH] Admin gave %d XP to player %d - new XP: %d, level: %d'):format(amount, source, playerData[source].xp, playerData[source].level))
  return true
end)

exports('SetPlayerLevel', function(playerId, level)
  local source = tonumber(playerId)
  if not source or not playerData[source] then
    print(('[KOTH] SetPlayerLevel: Invalid player ID %s or no cached data'):format(tostring(playerId)))
    return false
  end
  
  -- Calculate XP for the new level
  local levels = {
    {level = 1, required = 0},
    {level = 2, required = 100},
    {level = 3, required = 250},
    {level = 4, required = 500},
    {level = 5, required = 1000},
    {level = 6, required = 1750},
    {level = 7, required = 2750},
    {level = 8, required = 4000},
    {level = 9, required = 6000},
    {level = 10, required = 8500},
    {level = 11, required = 11000},
    {level = 12, required = 14000},
    {level = 13, required = 17500},
    {level = 14, required = 21500},
    {level = 15, required = 26000},
    {level = 16, required = 31000},
    {level = 17, required = 36500},
    {level = 18, required = 42500},
    {level = 19, required = 49000},
    {level = 20, required = 56000},
    {level = 25, required = 100000},
    {level = 30, required = 150000},
    {level = 40, required = 250000},
    {level = 50, required = 400000}
  }
  
  local requiredXP = 0
  for _, levelData in ipairs(levels) do
    if levelData.level == level then
      requiredXP = levelData.required
      break
    end
  end
  
  -- If level not in table, calculate XP based on formula
  if requiredXP == 0 and level > 10 then
    -- Formula: each level after 10 requires 2500 more XP than previous
    requiredXP = 8500 + ((level - 10) * 2500)
  end
  
  -- Update cached data
  local oldLevel = playerData[source].level
  playerData[source].level = level
  playerData[source].xp = requiredXP
  
  -- Save to database
  SavePlayerData(source)
  
  -- Check for level up notification
  if level > oldLevel then
    TriggerClientEvent('koth:levelUp', source, {
      newLevel = level,
      oldLevel = oldLevel
    })
  end
  
  -- Update client HUD immediately
  TriggerClientEvent('koth:updatePlayerData', source, playerData[source])
  
  print(('[KOTH] Admin set player %d to level %d (XP: %d)'):format(source, level, requiredXP))
  return true
end)

exports('ResetPlayerStats', function(playerId)
  local source = tonumber(playerId)
  if not source or not playerData[source] then
    print(('[KOTH] ResetPlayerStats: Invalid player ID %s or no cached data'):format(tostring(playerId)))
    return false
  end
  
  -- Reset cached data to defaults
  playerData[source].money = 1000
  playerData[source].xp = 0
  playerData[source].level = 1
  playerData[source].kills = 0
  playerData[source].deaths = 0
  playerData[source].zone_kills = 0
  
  -- Save to database
  SavePlayerData(source)
  
  -- Update client HUD immediately
  TriggerClientEvent('koth:updatePlayerData', source, playerData[source])
  
  print(('[KOTH] Admin reset stats for player %d'):format(source))
  return true
end)

-- Event to handle admin panel money/XP updates
RegisterNetEvent('koth:adminUpdatePlayerData', function(playerId, newData)
  local source = tonumber(playerId)
  if not source or not playerData[source] then
    print(('[KOTH] adminUpdatePlayerData: Invalid player ID %s or no cached data'):format(tostring(playerId)))
    return
  end
  
  -- Update cached data with new values
  if newData.money then
    playerData[source].money = newData.money
  end
  if newData.xp then
    local oldLevel = playerData[source].level
    playerData[source].xp = newData.xp
    playerData[source].level = newData.level or CalculateLevel(newData.xp)
    
    -- Check for level up
    if playerData[source].level > oldLevel then
      TriggerClientEvent('koth:levelUp', source, {
        newLevel = playerData[source].level,
        oldLevel = oldLevel
      })
    end
  end
  if newData.level then
    playerData[source].level = newData.level
  end
  
  -- Save to database
  SavePlayerData(source)
  
  -- Update client HUD immediately
  TriggerClientEvent('koth:updatePlayerData', source, playerData[source])
  
  print(('[KOTH] Admin updated player %d data via event'):format(source))
end)

-- TEMPORARY TEST COMMAND FOR TEAM WIN CHALLENGE
RegisterCommand('testteamwin', function(source, args)
  if source == 0 then
    print('[KOTH] This command must be used by a player')
    return
  end
  
  local playerTeam = GetPlayerTeam(source)
  if not playerTeam then
    print('[KOTH] Player has no team assigned')
    return
  end
  
  -- Trigger team win for player's team
  TriggerEvent('leaderboard:teamWin', playerTeam)
  
  TriggerClientEvent('chat:addMessage', source, {
    color = {0, 255, 0},
    multiline = false,
    args = {'[KOTH]', 'Simulated team win for ' .. playerTeam .. ' team (for testing daily challenges)'}
  })
  
  print(('[KOTH] Triggered team win for %s team (test command by player %d)'):format(playerTeam, source))
end, false)

print('[KOTH] Server loaded successfully')
