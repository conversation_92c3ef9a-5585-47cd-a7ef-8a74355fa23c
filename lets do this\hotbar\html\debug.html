<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Hotbar Debug</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #222;
            color: white;
            padding: 20px;
        }
        .test-container {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
            margin-top: 20px;
        }
        .test-item {
            background-color: #333;
            padding: 15px;
            border-radius: 8px;
        }
        .test-item h3 {
            margin-bottom: 10px;
            color: #4CAF50;
        }
        .test-item img {
            width: 64px;
            height: 64px;
            object-fit: contain;
            background-color: #444;
            padding: 8px;
            border-radius: 4px;
            display: block;
            margin: 10px 0;
        }
        .success {
            color: #4CAF50;
        }
        .error {
            color: #f44336;
        }
        .path-info {
            font-size: 12px;
            background-color: #444;
            padding: 5px;
            border-radius: 4px;
            margin: 5px 0;
            word-break: break-all;
        }
    </style>
</head>
<body>
    <h1>Hotbar Image Path Debug</h1>
    <p>Current URL: <span id="current-url"></span></p>
    <p>Base Path: <span id="base-path"></span></p>
    
    <div class="test-container">
        <div class="test-item">
            <h3>Test 1: Direct Path</h3>
            <p>Path: images/weapon_pistol50.png</p>
            <img id="test1" src="images/weapon_pistol50.png" alt="Test 1">
            <div class="path-info" id="test1-info"></div>
        </div>
        
        <div class="test-item">
            <h3>Test 2: Relative Path</h3>
            <p>Path: ./images/weapon_pistol50.png</p>
            <img id="test2" src="./images/weapon_pistol50.png" alt="Test 2">
            <div class="path-info" id="test2-info"></div>
        </div>
        
        <div class="test-item">
            <h3>Test 3: Absolute Path</h3>
            <p>Path: /images/weapon_pistol50.png</p>
            <img id="test3" src="/images/weapon_pistol50.png" alt="Test 3">
            <div class="path-info" id="test3-info"></div>
        </div>
        
        <div class="test-item">
            <h3>Test 4: NUI Path</h3>
            <p>Path: nui://hotbar/html/images/weapon_pistol50.png</p>
            <img id="test4" src="nui://hotbar/html/images/weapon_pistol50.png" alt="Test 4">
            <div class="path-info" id="test4-info"></div>
        </div>
        
        <div class="test-item">
            <h3>Test 5: HTTP Path</h3>
            <p>Path: http://hotbar/html/images/weapon_pistol50.png</p>
            <img id="test5" src="http://hotbar/html/images/weapon_pistol50.png" alt="Test 5">
            <div class="path-info" id="test5-info"></div>
        </div>
        
        <div class="test-item">
            <h3>Test 6: Asset Path</h3>
            <p>Path: asset://hotbar/html/images/weapon_pistol50.png</p>
            <img id="test6" src="asset://hotbar/html/images/weapon_pistol50.png" alt="Test 6">
            <div class="path-info" id="test6-info"></div>
        </div>
    </div>
    
    <h2>All Available Images:</h2>
    <div id="all-images"></div>

    <script>
        // Display current URL info
        document.getElementById('current-url').textContent = window.location.href;
        document.getElementById('base-path').textContent = window.location.origin + window.location.pathname.replace(/[^\/]*$/, '');
        
        // Test each image
        for (let i = 1; i <= 6; i++) {
            const img = document.getElementById(`test${i}`);
            const info = document.getElementById(`test${i}-info`);
            
            img.onload = function() {
                info.innerHTML = `<span class="success">✓ Success</span><br>Full URL: ${this.src}`;
                console.log(`Test ${i} SUCCESS:`, this.src);
            };
            
            img.onerror = function() {
                info.innerHTML = `<span class="error">✗ Failed</span><br>Attempted URL: ${this.src}`;
                console.error(`Test ${i} FAILED:`, this.src);
            };
        }
        
        // List all weapon images
        const weaponImages = [
            'weapon_advancedrifle.png',
            'weapon_appistol.png',
            'weapon_assaultrifle_mk2.png',
            'weapon_assaultrifle.png',
            'weapon_assaultsmg.png',
            'weapon_bullpuprifle_mk2.png',
            'weapon_bullpuprifle.png',
            'weapon_combatmg.png',
            'weapon_combatpdw.png',
            'weapon_pistol50.png',
            'weapon_smg.png',
            'weapon_microsmg.png'
        ];
        
        const allImagesDiv = document.getElementById('all-images');
        weaponImages.forEach(imageName => {
            const div = document.createElement('div');
            div.style.marginBottom = '10px';
            div.innerHTML = `
                <strong>${imageName}:</strong>
                <img src="images/${imageName}" style="width: 32px; height: 32px; vertical-align: middle;" 
                     onload="this.nextElementSibling.textContent=' ✓'" 
                     onerror="this.nextElementSibling.textContent=' ✗'">
                <span></span>
            `;
            allImagesDiv.appendChild(div);
        });
    </script>
</body>
</html>
