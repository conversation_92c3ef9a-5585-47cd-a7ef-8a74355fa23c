-- Step 1: Check what team values exist in the database
SELECT DISTINCT current_team, COUNT(*) as player_count 
FROM koth_players 
GROUP BY current_team;

-- Step 2: Clean up team values - remove spaces and make lowercase
UPDATE koth_players 
SET current_team = LOWER(TRIM(current_team))
WHERE current_team IS NOT NULL;

-- Step 3: Fix any invalid team values
UPDATE koth_players 
SET current_team = CASE 
    WHEN current_team LIKE '%red%' THEN 'red'
    WHEN current_team LIKE '%blue%' THEN 'blue'
    WHEN current_team LIKE '%green%' THEN 'green'
    ELSE NULL
END
WHERE current_team IS NOT NULL;

-- Step 4: Assign teams to players without teams
UPDATE koth_players 
SET current_team = CASE 
    WHEN (id % 3) = 0 THEN 'red'
    WHEN (id % 3) = 1 THEN 'blue'
    ELSE 'green'
END
WHERE current_team IS NULL OR current_team = '';

-- Step 5: Verify the results
SELECT current_team, COUNT(*) as player_count 
FROM koth_players 
GROUP BY current_team;
