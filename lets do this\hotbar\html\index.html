<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Hotbar</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div id="hotbar-container" class="hotbar-container">
        <div class="hotbar">
            <div class="hotbar-slot" data-slot="1">
                <div class="slot-number">1</div>
                <div class="slot-icon"></div>
                <div class="slot-count"></div>
                <div class="slot-cooldown"></div>
            </div>
            <div class="hotbar-slot" data-slot="2">
                <div class="slot-number">2</div>
                <div class="slot-icon"></div>
                <div class="slot-count"></div>
                <div class="slot-cooldown"></div>
            </div>
            <div class="hotbar-slot" data-slot="3">
                <div class="slot-number">3</div>
                <div class="slot-icon"></div>
                <div class="slot-count"></div>
                <div class="slot-cooldown"></div>
            </div>
            <div class="hotbar-slot" data-slot="4">
                <div class="slot-number">4</div>
                <div class="slot-icon"></div>
                <div class="slot-count"></div>
                <div class="slot-cooldown"></div>
            </div>
            <div class="hotbar-slot" data-slot="5">
                <div class="slot-number">5</div>
                <div class="slot-icon"></div>
                <div class="slot-count"></div>
                <div class="slot-cooldown"></div>
            </div>
        </div>
    </div>

    <!-- Debug overlay (hidden by default) -->
    <div id="debug-overlay" style="position: fixed; top: 10px; left: 10px; background: rgba(0,0,0,0.8); color: white; padding: 10px; font-size: 12px; border-radius: 5px; display: none; z-index: 9999;">
        <div id="debug-info">Debug Info</div>
    </div>
    
    <script src="script.js"></script>
</body>
</html>
