# Player Leaderboard Final Fix Guide

## The Problem
Players are not showing in teams even though they exist in the database. This is likely due to:
1. Team values having extra spaces or wrong casing
2. Team values being NULL or empty strings
3. Team values not matching exactly 'red', 'blue', or 'green'

## Quick Fix Steps

### 1. Run this SQL in phpMyAdmin to see current team values:
```sql
SELECT DISTINCT current_team, LENGTH(current_team) as length, COUNT(*) as count 
FROM koth_players 
GROUP BY current_team;
```

This will show you:
- What team values exist (including NULL, empty, or with spaces)
- The length of each value (to detect hidden spaces)
- How many players have each value

### 2. Fix the team values:
```sql
-- First, clean up any spaces and make lowercase
UPDATE koth_players 
SET current_team = LOWER(TRIM(current_team))
WHERE current_team IS NOT NULL AND current_team != '';

-- Then ensure only valid teams
UPDATE koth_players 
SET current_team = CASE 
    WHEN current_team IN ('red', 'blue', 'green') THEN current_team
    ELSE CASE 
        WHEN (id % 3) = 0 THEN 'red'
        WHEN (id % 3) = 1 THEN 'blue'
        ELSE 'green'
    END
END;
```

### 3. Verify the fix:
```sql
SELECT current_team, COUNT(*) as player_count 
FROM koth_players 
GROUP BY current_team;
```

You should now see only 'red', 'blue', and 'green' teams with players in each.

## Testing the Leaderboard

1. Restart your FiveM server
2. Press F7 to open the leaderboard
3. Check the server console for debug messages
4. Run `/checkteams` in the server console to see team distribution

## If Still Not Working

Check these debug points:

### In Server Console:
```
checkteams
```
This will show all players with their teams.

### Check a specific player:
```sql
SELECT id, player_name, current_team, 
       LENGTH(current_team) as team_length,
       HEX(current_team) as team_hex
FROM koth_players 
WHERE player_name = 'YourPlayerName';
```

The HEX value will reveal any hidden characters.

## Common Issues and Solutions

### Issue: Teams show as NULL
**Solution:** Run the assignment SQL to give everyone a team

### Issue: Teams have spaces like ' red' or 'red '
**Solution:** The TRIM function in our SQL will fix this

### Issue: Teams have wrong case like 'Red' or 'RED'
**Solution:** The LOWER function in our SQL will fix this

### Issue: Database has different column name
**Solution:** Check your database structure and adjust column names in the SQL

## Final Notes

- The leaderboard now normalizes team values when reading from database
- It only accepts exactly 'red', 'blue', or 'green' (lowercase)
- Fresh data is fetched every time you open the leaderboard
- Zone kills are displayed as "captures" in the UI

After running the SQL fixes, your leaderboard should show all players in their respective teams!
