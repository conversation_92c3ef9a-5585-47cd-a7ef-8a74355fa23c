# Player Leaderboard Menu

A FiveM resource that provides a comprehensive player statistics and leaderboard menu accessible via the F7 key.

## Features

- **Player Statistics**: View your current stats including kills, deaths, assists, and captures
- **Daily Challenges**: Track progress on daily challenges with rewards
- **Team Leaderboards**: View leaderboards for different teams (Blue, <PERSON>, Green)
- **Real-time Updates**: Statistics update in real-time
- **Responsive Design**: Clean, modern UI that matches the game's aesthetic

## Installation

1. Copy the `player-leaderboard` folder to your FiveM server's `resources` directory
2. Add `ensure player-leaderboard` to your `server.cfg` file
3. Restart your server or start the resource with `start player-leaderboard`

## Usage

- Press **F7** to open/close the leaderboard menu
- Press **ESC** to close the menu
- Click outside the menu area to close it

## Configuration

The resource comes with sample data. To integrate with your existing player statistics system:

1. Modify `server/main.lua` to fetch data from your database
2. Update the `UpdatePlayerStats` function to handle stat updates from other resources
3. Customize the challenges in the sample data section

## Integration with Other Resources

Other resources can update player statistics using the exported function:

```lua
-- Example: Update a player's kill count
exports['player-leaderboard']:UpdatePlayerStats(playerId, 'kills', newKillCount)
```

## Customization

- **Colors**: Modify team colors in `html/style.css`
- **Layout**: Adjust the HTML structure in `html/index.html`
- **Data**: Update sample data in `server/main.lua`
- **Keybind**: Change the F7 keybind in `client/main.lua`

## File Structure

```
player-leaderboard/
├── fxmanifest.lua          # Resource manifest
├── client/
│   └── main.lua            # Client-side logic
├── server/
│   └── main.lua            # Server-side logic and data
├── html/
│   ├── index.html          # Menu HTML structure
│   ├── style.css           # Menu styling
│   └── script.js           # Menu JavaScript logic
└── README.md               # This file
```

## Dependencies

- FiveM server
- No additional dependencies required

## License

This resource is provided as-is for educational and development purposes.
