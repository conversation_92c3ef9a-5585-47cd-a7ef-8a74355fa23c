print('[KOTH] Client loading...')

-- Register decorator for player vehicles (must be done early)
DecorRegister("PlayerVehicle", 3)

-- TEAM SPAWN COORDS
local teamSpawns = {
  red   = { x=2238.15, y=3788.91, z=35.89, heading=120.5 },
  blue  = { x=1323.77, y=3143.33, z=40.41, heading=282.48 },
  green = { x=1865.99, y=2607.15, z=45.67, heading=276.45 },
}

-- Player variables
local playerTeam = nil
local playerStats = nil
local hasSelectedTeam = false
local weaponShopOpened = false

-- Request team counts and show UI
AddEventHandler('playerSpawned', function()
  print('[KOTH] Player spawned event triggered')
  print('[KOTH] hasSelectedTeam:', hasSelectedTeam)
  print('[KOTH] playerTeam:', playerTeam)
  
  -- Only show team selection if not already selected
  if not hasSelectedTeam and not playerTeam then
    Citizen.SetTimeout(100, function()
      print('[KOTH] Showing team selection UI...')
      TriggerServerEvent('koth:requestCounts')
      
      -- Freeze player until they select a team
      local playerPed = PlayerPedId()
      FreezeEntityPosition(playerPed, true)
      SetEntityInvincible(playerPed, true)
    end)
  else
    print('[KOTH] Player already has team, skipping team selection UI')
    -- Make sure player is unfrozen if they already have a team
    local playerPed = PlayerPedId()
    FreezeEntityPosition(playerPed, false)
    SetEntityInvincible(playerPed, false)
  end
  
  -- Always request player data
  TriggerServerEvent('koth:requestPlayerData')
end)

-- Also trigger on resource start for testing
AddEventHandler('onClientResourceStart', function(res)
  if GetCurrentResourceName() ~= res then return end
  
  -- Don't load stored team on resource start - always show team selection
  print('[KOTH] Resource started - clearing any stored team for fresh selection')
  playerTeam = nil
  hasSelectedTeam = false
  DeleteResourceKvp('playerTeam')
  
  -- Initialize HUD with default values to prevent showing full XP bar
  SendNUIMessage({
    action = 'initializeHUD',
    playerData = {
      money = 0,
      xp = 0,
      level = 1,
      kills = 0,
      deaths = 0,
      zone_kills = 0,
      player_name = GetPlayerName(PlayerId())
    }
  })
  
  -- Notify server that client is ready
  TriggerServerEvent('koth:clientReady')
  
  -- Wait a bit for player to fully load
  Citizen.SetTimeout(2000, function()
    print('[KOTH] Resource started, requesting team counts...')
    TriggerServerEvent('koth:requestCounts')
    
    -- Freeze player until they select a team
    local playerPed = PlayerPedId()
    FreezeEntityPosition(playerPed, true)
    SetEntityInvincible(playerPed, true)
    
    -- Always request player data
    TriggerServerEvent('koth:requestPlayerData')
  end)
end)

-- Command to reset stored team and selection state
RegisterCommand('resetteamselect', function()
  print('[KOTH] Resetting stored team and selection state')
  playerTeam = nil
  hasSelectedTeam = false
  DeleteResourceKvp('playerTeam')
  print('[KOTH] Stored team cleared, you can now select a team again')
end, false)

-- Handle player data updates from server (DATABASE SYNCED)
RegisterNetEvent('koth:updatePlayerData', function(data)
  print('[KOTH] Received player data update from server:', json.encode(data))
  playerStats = data
  
  -- Update HUD with real player data from database
  if playerStats then
    print('[KOTH] Updating HUD with player data - Money: $' .. (playerStats.money or 0) .. ', XP: ' .. (playerStats.xp or 0))
    
    SendNUIMessage({
      action = 'updatePlayerData',
      data = {
        player_name = playerStats.player_name or GetPlayerName(PlayerId()),
        money = playerStats.money or 0,
        level = playerStats.level or 1,
        xp = playerStats.xp or 0,
        kills = playerStats.kills or 0,
        deaths = playerStats.deaths or 0,
        zone_kills = playerStats.zone_kills or 0,
        total_playtime = playerStats.total_playtime or 0
      }
    })
    
    print('[KOTH] HUD updated successfully - Money: $' .. (playerStats.money or 0) .. ', XP: ' .. (playerStats.xp or 0))
  else
    print('[KOTH] ERROR: Received null player data from server')
  end
end)

-- Handle kill reward event to show UI and update HUD
RegisterNetEvent('koth:showKillReward', function(data)
  print('=== [KOTH CLIENT] KILL REWARD EVENT RECEIVED ===')
  print('[KOTH] Raw kill reward data:', json.encode(data))
  
  if not data then
    print('[KOTH] ERROR: Kill reward data is nil!')
    return
  end
  
  print('[KOTH] Kill reward details - XP: ' .. tostring(data.xp) .. ', Money: $' .. tostring(data.money) .. ', Zone: ' .. tostring(data.inZone))
  
  -- Send to NUI to show kill reward popup
  local nuiData = {
    action = 'showKillReward',
    xp = data.xp,
    money = data.money,
    inZone = data.inZone,
    victim = data.victimName
  }
  
  print('[KOTH] Sending to NUI:', json.encode(nuiData))
  SendNUIMessage(nuiData)
  
  -- Update player money and XP in HUD
  if playerStats then
    local oldMoney = playerStats.money or 0
    local oldXP = playerStats.xp or 0
    
    playerStats.money = oldMoney + (data.money or 0)
    playerStats.xp = oldXP + (data.xp or 0)
    
    -- Recalculate level based on new XP
    local function CalculateLevel(xp)
      local levels = {
        {level = 1, required = 0},
        {level = 2, required = 100},
        {level = 3, required = 250},
        {level = 4, required = 500},
        {level = 5, required = 1000},
        {level = 6, required = 1750},
        {level = 7, required = 2750},
        {level = 8, required = 4000},
        {level = 9, required = 6000},
        {level = 10, required = 8500}
      }
      local currentLevel = 1
      for _, levelData in ipairs(levels) do
        if xp >= levelData.required then
          currentLevel = levelData.level
        else
          break
        end
      end
      return currentLevel
    end
    
    local newLevel = CalculateLevel(playerStats.xp)
    if newLevel ~= playerStats.level then
      print('[KOTH] Player level updated from ' .. tostring(playerStats.level) .. ' to ' .. tostring(newLevel))
      playerStats.level = newLevel
    end
    
    print('[KOTH] Updated local playerStats - Money: $' .. oldMoney .. ' -> $' .. playerStats.money .. ', XP: ' .. oldXP .. ' -> ' .. playerStats.xp .. ', Level: ' .. playerStats.level)
    
    local hudUpdateData = {
      action = 'updatePlayerData',
      data = {
        money = playerStats.money,
        xp = playerStats.xp,
        level = playerStats.level
      }
    }
    
    print('[KOTH] Sending HUD update:', json.encode(hudUpdateData))
    SendNUIMessage(hudUpdateData)
  else
    print('[KOTH] WARNING: playerStats is nil, cannot update local HUD')
  end
  
  print('[KOTH] Kill reward processing complete')
  print('=== [KOTH CLIENT] KILL REWARD EVENT END ===')
end)

-- TEAM-BASED CLOTHING CONFIGURATION
local teamClothing = {
  red = {
    -- MILITIA VEST - RED
    [9] = { drawable = 62, texture = 0 },  -- Body Armor (vest)
    -- CARGO SHORTS - RED  
    [4] = { drawable = 203, texture = 0 }, -- Legs (shorts)
    -- Top - RED
    [11] = { drawable = 544, texture = 0 }, -- Jacket/Top
    -- Additional components for complete outfit
    [3] = { drawable = 0, texture = 0 },   -- Torso (arms)
    [8] = { drawable = 15, texture = 0 },  -- Undershirt
    [6] = { drawable = 25, texture = 0 },  -- Shoes (boots)
  },
  green = {
    -- MILITIA VEST - GREEN
    [9] = { drawable = 62, texture = 1 },  -- Body Armor (vest)
    -- CARGO SHORTS - GREEN
    [4] = { drawable = 203, texture = 2 }, -- Legs (shorts)
    -- Top - GREEN
    [11] = { drawable = 544, texture = 1 }, -- Jacket/Top
    -- Additional components for complete outfit
    [3] = { drawable = 0, texture = 0 },   -- Torso (arms)
    [8] = { drawable = 15, texture = 0 },  -- Undershirt
    [6] = { drawable = 25, texture = 0 },  -- Shoes (boots)
  },
  blue = {
    -- MILITIA VEST - BLUE
    [9] = { drawable = 62, texture = 2 },  -- Body Armor (vest)
    -- CARGO SHORTS - BLUE
    [4] = { drawable = 203, texture = 1 }, -- Legs (shorts)
    -- Top - BLUE
    [11] = { drawable = 544, texture = 2 }, -- Jacket/Top
    -- Additional components for complete outfit
    [3] = { drawable = 0, texture = 0 },   -- Torso (arms)
    [8] = { drawable = 15, texture = 0 },  -- Undershirt
    [6] = { drawable = 25, texture = 0 },  -- Shoes (boots)
  }
}

-- Function to apply team appearance
local function ApplyTeamAppearance(team)
  local playerPed = PlayerPedId()
  
  -- Change to the specific ped model
  local model = `mp_m_freemode_01`
  
  if not HasModelLoaded(model) then
    RequestModel(model)
    while not HasModelLoaded(model) do
      Citizen.Wait(0)
    end
  end
  
  -- Change the player model
  SetPlayerModel(PlayerId(), model)
  playerPed = PlayerPedId() -- Get new ped after model change
  
  -- Apply team-specific clothing
  if teamClothing[team] then
    for componentId, clothing in pairs(teamClothing[team]) do
      SetPedComponentVariation(playerPed, componentId, clothing.drawable, clothing.texture, 0)
    end
    
    -- Set face and hair (consistent across all teams)
    SetPedComponentVariation(playerPed, 0, 0, 0, 0)  -- Face
    SetPedComponentVariation(playerPed, 2, 11, 4, 0) -- Hair (buzz cut style)
    SetPedHairColor(playerPed, 0, 0) -- Black hair
    
    -- Remove any props (hats, glasses, etc.)
    ClearPedProp(playerPed, 0) -- Hat
    ClearPedProp(playerPed, 1) -- Glasses
    ClearPedProp(playerPed, 2) -- Ear
    ClearPedProp(playerPed, 6) -- Watch
    ClearPedProp(playerPed, 7) -- Bracelet
    
    print(('[KOTH] Applied %s team appearance'):format(team))
  else
    print(('[KOTH] No clothing config found for team: %s'):format(team))
  end
  
  -- Ensure the model doesn't get reset
  SetModelAsNoLongerNeeded(model)
end

-- Handle spawn event from server
RegisterNetEvent('koth:spawnPlayer', function(spawnData)
  print('[KOTH] Spawning player at team location')
  
  local playerPed = PlayerPedId()
  
  -- Apply team appearance BEFORE teleporting
  if playerTeam then
    ApplyTeamAppearance(playerTeam)
    playerPed = PlayerPedId() -- Get new ped after appearance change
  end
  
  -- Set spawn position
  SetEntityCoords(playerPed, spawnData.x, spawnData.y, spawnData.z, false, false, false, true)
  SetEntityHeading(playerPed, spawnData.heading or 0.0)
  
  -- Ensure player is on ground and unfrozen
  SetEntityCollision(playerPed, true, true)
  FreezeEntityPosition(playerPed, false)
  SetEntityInvincible(playerPed, false)
  
  -- Give basic weapon
  GiveWeaponToPed(playerPed, GetHashKey('WEAPON_PISTOL'), 250, false, true)
  
  -- Initialize HUD
  SendNUIMessage({ 
    action = 'initHUD',
    playerData = playerStats
  })
  
  -- Request fresh team counts after spawning
  Citizen.SetTimeout(500, function()
    print('[KOTH] Requesting team counts after spawn...')
    TriggerServerEvent('koth:requestCounts')
  end)
  
  -- Enable PVP
  SetCanAttackFriendly(playerPed, true, false)
  NetworkSetFriendlyFireOption(true)
  
  -- Make sure player can move
  ClearPedTasksImmediately(playerPed)
  
  print('[KOTH] Player spawned successfully and unfrozen')
end)

-- Apply team appearance on respawn
AddEventHandler('playerSpawned', function()
  -- Wait a moment for spawn to complete
  Citizen.SetTimeout(1000, function()
    if playerTeam and hasSelectedTeam then
      print('[KOTH] Reapplying team appearance after respawn')
      ApplyTeamAppearance(playerTeam)
    end
  end)
end)

-- Command to manually reapply team appearance
RegisterCommand('teamoutfit', function()
  if playerTeam then
    ApplyTeamAppearance(playerTeam)
    print('[KOTH] Team appearance reapplied')
  else
    print('[KOTH] No team selected yet')
  end
end, false)

-- Event handler for applying team appearance (used by death system)
RegisterNetEvent('koth:applyTeamAppearance', function(team)
  if team then
    ApplyTeamAppearance(team)
    print('[KOTH] Team appearance applied via event for team:', team)
  end
end)

-- Receive counts and show team select
RegisterNetEvent('koth:updateCounts', function(counts)
  print('[KOTH] Received counts')
  print('[KOTH] Counts data:', json.encode(counts or {}))
  print('[KOTH] hasSelectedTeam:', hasSelectedTeam, 'playerTeam:', playerTeam)
  
  -- Only show team selection UI if player hasn't selected a team yet
  if not hasSelectedTeam then
    -- Force show the UI even if counts is nil
    SendNUIMessage({ 
      action = 'showTeamSelect', 
      counts = counts or { red = 0, blue = 0, green = 0 }
    })
    SetNuiFocus(true, true)
    
    print('[KOTH] Team selection UI shown')
  else
    print('[KOTH] Skipping team selection UI - player already has team')
    -- Make sure UI is hidden
    SendNUIMessage({ action = 'hideAll' })
    SetNuiFocus(false, false)
  end
  
  -- Always initialize HUD
  SendNUIMessage({ action = 'initHUD' })
  
  print('[KOTH] NUI message sent')
end)

-- Team selection callback
RegisterNUICallback('selectTeam', function(data, cb)
  print('[KOTH] Team selected:', data.team or 'none')
  if data and data.team then
    playerTeam = data.team
    hasSelectedTeam = true
    
    -- Store team for death system and persistence
    SetResourceKvp('playerTeam', data.team)
    TriggerEvent('koth:teamSelected', data.team)
    
    -- Hide UI immediately
    SendNUIMessage({ action='hideAll' })
    SetNuiFocus(false, false)
    
    -- Send team selection to server
    TriggerServerEvent('koth:pickTeam', data.team)
    
    print('[KOTH] Team selection complete, UI hidden')
  end
  cb('ok')
end)

local ownedVehicles = {}

-- Receive updated owned vehicles list from server
RegisterNetEvent('koth:updateOwnedVehicles', function(data)
  print('[KOTH] Received owned vehicles list from server')
  ownedVehicles = {}
  if data and type(data) == 'table' then
    for _, vehicleName in ipairs(data) do
      ownedVehicles[vehicleName] = true
    end
  end
  print('[KOTH] Owned vehicles:', json.encode(ownedVehicles))
end)

-- Receive fresh vehicle shop data with money
RegisterNetEvent('koth:showVehicleShopWithMoney', function(data)
  print('[KOTH] Received vehicle shop data with money:', data.money)

  -- Mark vehicles as owned if in ownedVehicles table
  if data.vehicles and type(data.vehicles) == 'table' then
    for _, vehicle in ipairs(data.vehicles) do
      if ownedVehicles[vehicle.name] then
        vehicle.owned = true
        vehicle.cost = 0 -- Mark cost as 0 for owned vehicles
        vehicle.rent = 0 -- Mark rent as 0 for owned vehicles
      else
        vehicle.owned = false
      end
    end
  end
  
  SendNUIMessage({
    action = 'showMenu',
    type = 'vehicles',
    items = data.vehicles,
    money = data.money
  })
  SetNuiFocus(true, true)
end)

-- Receive fresh class shop data with money and level
RegisterNetEvent('koth:showClassShopWithData', function(data)
  print('[KOTH] Received class shop data with money:', data.money, 'level:', data.level)
  
  -- Add locked status based on player level
  for i, class in ipairs(data.classes) do
    class.locked = data.level < class.requiredLevel
  end
  
  SendNUIMessage({
    action = 'showMenu',
    type = 'classes',
    items = data.classes,
    playerLevel = data.level,
    money = data.money
  })
  SetNuiFocus(true, true)
end)

-- Receive fresh weapon shop data with money
RegisterNetEvent('koth:showWeaponShopWithMoney', function(data)
  print('[KOTH] Received weapon shop data with money:', data.money)
  weaponShopOpened = true
  
  SendNUIMessage({
    action = 'showWeaponSelect',
    class = data.class,
    weapons = data.weapons,
    money = data.money
  })
end)

-- Vehicle menu - triggered by ped interaction
RegisterNetEvent('koth:openVehicleMenu', function(items)
  print('[KOTH] Opening vehicle menu')

  -- Use provided items or comprehensive vehicle data
  local vehicles = items or {
    { name = 'Blista', cost = 8000, rent = 1200, img = 'images/vehicles/blista.png' },
    { name = 'Futo', cost = 12000, rent = 1800, img = 'images/vehicles/futo.png' },
    { name = 'Sultan', cost = 15000, rent = 2200, img = 'images/vehicles/sultan.png' },
    { name = 'Elegy', cost = 18000, rent = 2700, img = 'images/vehicles/elegy.png' },
    { name = 'Kuruma', cost = 22000, rent = 3300, img = 'images/vehicles/kuruma.png' },
    { name = 'Armored Kuruma', cost = 35000, rent = 5200, img = 'images/vehicles/kuruma_armored.png' },
    { name = 'Insurgent', cost = 45000, rent = 6700, img = 'images/vehicles/insurgent.png' },
    { name = 'Technical', cost = 28000, rent = 4200, img = 'images/vehicles/technical.png' },
    { name = 'Sandking XL', cost = 32000, rent = 4800, img = 'images/vehicles/sandking.png' },
    { name = 'Mesa', cost = 25000, rent = 3700, img = 'images/vehicles/mesa.png' },
    { name = 'Buzzard', cost = 85000, rent = 12700, img = 'images/vehicles/buzzard.png' },
    { name = 'Savage', cost = 120000, rent = 18000, img = 'images/vehicles/savage.png' },
    { name = 'Rhino Tank', cost = 150000, rent = 22500, img = 'images/vehicles/rhino.png' },
    { name = 'Hydra', cost = 200000, rent = 30000, img = 'images/vehicles/hydra.png' }
  }

  -- Request fresh data from server
  TriggerServerEvent('koth:getMoneyForVehicleShop', vehicles)
end)

-- Class menu - triggered by ped interaction
RegisterNetEvent('koth:openClassMenu', function(items)
  print('[KOTH] Opening class menu')

  -- Get player level
  local playerLevel = playerStats and playerStats.level or 1

  -- Use provided items or exact classes from the image
  local classes = items or {
    {
      id = 'assault',
      name = 'Assault',
      unlock = 'Unlocked',
      img = 'images/classes/assault.png',
      requiredLevel = 1
    },
    {
      id = 'medic',
      name = 'Medic',
      unlock = playerLevel >= 5 and 'Unlocked' or 'Unlock at level 5',
      img = 'images/classes/medic.png',
      requiredLevel = 5
    },
    {
      id = 'engineer',
      name = 'Engineer',
      unlock = playerLevel >= 15 and 'Unlocked' or 'Unlock at level 15',
      img = 'images/classes/engineer.png',
      requiredLevel = 15
    },
    {
      id = 'heavy',
      name = 'Heavy',
      unlock = playerLevel >= 25 and 'Unlocked' or 'Unlock at level 25',
      img = 'images/classes/heavy.png',
      requiredLevel = 25
    },
    {
      id = 'scout',
      name = 'Scout',
      unlock = playerLevel >= 35 and 'Unlocked' or 'Unlock at level 35',
      img = 'images/classes/scout.png',
      requiredLevel = 35
    },
  }

  -- Request fresh data from server
  TriggerServerEvent('koth:getDataForClassShop', classes)
end)

-- Attachment menu - triggered by ped interaction
RegisterNetEvent('koth:openAttachmentMenu', function()
  print('[KOTH] Opening attachment menu')
  
  -- Check if player has a weapon in hotbar slot 1
  local playerPed = PlayerPedId()
  local currentWeapon = GetSelectedPedWeapon(playerPed)
  
  if currentWeapon == GetHashKey('WEAPON_UNARMED') then
    -- Show error message
    BeginTextCommandThefeedPost("STRING")
    AddTextComponentSubstringPlayerName("You need a weapon equipped to purchase attachments!")
    EndTextCommandThefeedPostTicker(false, true)
    return
  end
  
  -- Request attachment menu from server
  TriggerServerEvent('koth:getAttachmentMenu', currentWeapon)
end)

-- Receive attachment menu data from server
RegisterNetEvent('koth:showAttachmentMenu', function(data)
  print('[KOTH] Received attachment menu data:', json.encode(data))
  
  -- Send message to the attachment menu UI
  SendNUIMessage({
    action = 'showAttachmentMenu',
    attachments = data.attachments,
    weaponName = data.weaponName,
    money = data.money
  })
  SetNuiFocus(true, true)
end)

-- Vehicle purchase callbacks
RegisterNUICallback('buyVehicle', function(data, cb)
  print('[KOTH] Buying vehicle:', data.name or 'none', 'price:', data.price or 'none')
  if data and data.name then
    -- Send price data with the purchase
    TriggerServerEvent('koth:buyVehicle', {
      name = data.name,
      price = tonumber(data.price) or tonumber(data.cost) or 0
    })
  end
  SendNUIMessage({ action='hideAll' })
  SetNuiFocus(false, false)
  cb('ok')
end)

RegisterNUICallback('rentVehicle', function(data, cb)
  print('[KOTH] Renting vehicle:', data.name or 'none', 'price:', data.price or 'none')
  if data and data.name then
    -- Send price data with the rental
    TriggerServerEvent('koth:rentVehicle', {
      name = data.name,
      price = tonumber(data.price) or tonumber(data.rent) or 0
    })
  end
  SendNUIMessage({ action='hideAll' })
  SetNuiFocus(false, false)
  cb('ok')
end)

-- Class selection callback - now shows weapon selection
RegisterNUICallback('selectClass', function(data, cb)
  print('[KOTH] Class selected:', data.id or 'none')
  if data and data.id then
    -- Check if player has required level for this class
    local playerLevel = playerStats and playerStats.level or 1
    local requiredLevels = {
      assault = 1,
      medic = 5,
      engineer = 15,
      heavy = 25,
      scout = 35
    }

    local requiredLevel = requiredLevels[data.id] or 1
    if playerLevel < requiredLevel then
      -- Show error message
      BeginTextCommandThefeedPost("STRING")
      AddTextComponentSubstringPlayerName(('Class locked! Requires level %d (you are level %d)'):format(requiredLevel, playerLevel))
      EndTextCommandThefeedPostTicker(false, true)
      cb('ok')
      return
    end
    
    -- Trigger class selection event for koth_classes
    TriggerEvent('koth:classSelected', data.id)
    
    -- Helper function to get weapon image using the new image handler
    local function getWeaponImage(weaponName)
      if exports['vantage-weapon-images-handler'] then
        return exports['vantage-weapon-images-handler']:GetWeaponImage(weaponName)
      else
        -- Fallback to old system if handler not available
        return 'images/guns/weapon_pistol50.png'
      end
    end
    
    -- Define class-specific weapons using the new image handler
    local classWeapons = {
      assault = {
        -- Primary weapons (Assault Rifles)
        { weapon = 'WEAPON_CARBINERIFLE', name = 'Carbine Rifle', price = 0, img = getWeaponImage('WEAPON_CARBINERIFLE'), category = 'primary', free = true },
        { weapon = 'WEAPON_CARBINERIFLE_MK2', name = 'Carbine Rifle Mk II', price = 3500, img = getWeaponImage('WEAPON_CARBINERIFLE_MK2'), category = 'primary' },
        { weapon = 'WEAPON_SPECIALCARBINE', name = 'Special Carbine', price = 4000, img = getWeaponImage('WEAPON_SPECIALCARBINE'), category = 'primary' },
        { weapon = 'WEAPON_SPECIALCARBINE_MK2', name = 'Special Carbine Mk II', price = 5000, img = getWeaponImage('WEAPON_SPECIALCARBINE_MK2'), category = 'primary' },
        { weapon = 'WEAPON_ASSAULTRIFLE', name = 'Assault Rifle', price = 3500, img = getWeaponImage('WEAPON_ASSAULTRIFLE'), category = 'primary' },
        { weapon = 'WEAPON_ASSAULTRIFLE_MK2', name = 'Assault Rifle Mk II', price = 4500, img = getWeaponImage('WEAPON_ASSAULTRIFLE_MK2'), category = 'primary' },
        { weapon = 'WEAPON_BULLPUPRIFLE', name = 'Bullpup Rifle', price = 3000, img = getWeaponImage('WEAPON_BULLPUPRIFLE'), category = 'primary' },
        { weapon = 'WEAPON_BULLPUPRIFLE_MK2', name = 'Bullpup Rifle Mk II', price = 4000, img = getWeaponImage('WEAPON_BULLPUPRIFLE_MK2'), category = 'primary' },
        { weapon = 'WEAPON_ADVANCEDRIFLE', name = 'Advanced Rifle', price = 5500, img = getWeaponImage('WEAPON_ADVANCEDRIFLE'), category = 'primary' },
        { weapon = 'WEAPON_MILITARYRIFLE', name = 'Military Rifle', price = 6000, img = getWeaponImage('WEAPON_MILITARYRIFLE'), category = 'primary' },
        { weapon = 'WEAPON_HEAVYRIFLE', name = 'Heavy Rifle', price = 6500, img = getWeaponImage('WEAPON_HEAVYRIFLE'), category = 'primary' },
        { weapon = 'WEAPON_COMPACTRIFLE', name = 'Compact Rifle', price = 2500, img = getWeaponImage('WEAPON_COMPACTRIFLE'), category = 'primary' },
        -- Secondary weapons (SMGs)
        { weapon = 'WEAPON_SMG', name = 'SMG', price = 1500, img = getWeaponImage('WEAPON_SMG'), category = 'secondary' },
        { weapon = 'WEAPON_SMG_MK2', name = 'SMG Mk II', price = 2500, img = getWeaponImage('WEAPON_SMG_MK2'), category = 'secondary' },
        { weapon = 'WEAPON_COMBATPDW', name = 'Combat PDW', price = 2000, img = getWeaponImage('WEAPON_COMBATPDW'), category = 'secondary' },
        { weapon = 'WEAPON_ASSAULTSMG', name = 'Assault SMG', price = 2500, img = getWeaponImage('WEAPON_ASSAULTSMG'), category = 'secondary' },
        { weapon = 'WEAPON_MICROSMG', name = 'Micro SMG', price = 1000, img = getWeaponImage('WEAPON_MICROSMG'), category = 'secondary' },
        { weapon = 'WEAPON_MACHINEPISTOL', name = 'Machine Pistol', price = 1200, img = getWeaponImage('WEAPON_MACHINEPISTOL'), category = 'secondary' },
        -- Sidearms
        { weapon = 'WEAPON_COMBATPISTOL', name = 'Combat Pistol', price = 500, img = getWeaponImage('WEAPON_COMBATPISTOL'), category = 'sidearm' },
        { weapon = 'WEAPON_APPISTOL', name = 'AP Pistol', price = 800, img = getWeaponImage('WEAPON_APPISTOL'), category = 'sidearm' },
        { weapon = 'WEAPON_PISTOL_MK2', name = 'Pistol Mk II', price = 700, img = getWeaponImage('WEAPON_PISTOL_MK2'), category = 'sidearm' },
        { weapon = 'WEAPON_PISTOL50', name = 'Pistol .50', price = 1000, img = getWeaponImage('WEAPON_PISTOL50'), category = 'sidearm' },
        -- Throwables
        { weapon = 'WEAPON_GRENADE', name = 'Grenade', price = 100, img = getWeaponImage('WEAPON_GRENADE'), category = 'throwable' },
        { weapon = 'WEAPON_STICKYBOMB', name = 'Sticky Bomb', price = 200, img = getWeaponImage('WEAPON_STICKYBOMB'), category = 'throwable' },
        { weapon = 'WEAPON_PIPEBOMB', name = 'Pipe Bomb', price = 150, img = getWeaponImage('WEAPON_PIPEBOMB'), category = 'throwable' }
      },
      medic = {
        -- Primary weapons (SMGs/Shotguns)
        { weapon = 'WEAPON_SMG', name = 'SMG', price = 0, img = getWeaponImage('WEAPON_SMG'), category = 'primary', free = true },
        { weapon = 'WEAPON_SMG_MK2', name = 'SMG Mk II', price = 2500, img = getWeaponImage('WEAPON_SMG_MK2'), category = 'primary' },
        { weapon = 'WEAPON_MICROSMG', name = 'Micro SMG', price = 1000, img = getWeaponImage('WEAPON_MICROSMG'), category = 'primary' },
        { weapon = 'WEAPON_COMBATPDW', name = 'Combat PDW', price = 2000, img = getWeaponImage('WEAPON_COMBATPDW'), category = 'primary' },
        { weapon = 'WEAPON_ASSAULTSMG', name = 'Assault SMG', price = 2500, img = getWeaponImage('WEAPON_ASSAULTSMG'), category = 'primary' },
        { weapon = 'WEAPON_SWEEPERSHOTGUN', name = 'Sweeper Shotgun', price = 3000, img = getWeaponImage('WEAPON_SWEEPERSHOTGUN'), category = 'primary' },
        { weapon = 'WEAPON_COMBATSHOTGUN', name = 'Combat Shotgun', price = 3500, img = getWeaponImage('WEAPON_COMBATSHOTGUN'), category = 'primary' },
        -- Sidearms
        { weapon = 'WEAPON_SNSPISTOL', name = 'SNS Pistol', price = 300, img = getWeaponImage('WEAPON_SNSPISTOL'), category = 'sidearm' },
        { weapon = 'WEAPON_SNSPISTOL_MK2', name = 'SNS Pistol Mk II', price = 500, img = getWeaponImage('WEAPON_SNSPISTOL_MK2'), category = 'sidearm' },
        { weapon = 'WEAPON_VINTAGEPISTOL', name = 'Vintage Pistol', price = 600, img = getWeaponImage('WEAPON_VINTAGEPISTOL'), category = 'sidearm' },
        { weapon = 'WEAPON_PISTOL', name = 'Pistol', price = 400, img = getWeaponImage('WEAPON_PISTOL'), category = 'sidearm' },
        { weapon = 'WEAPON_PISTOL_MK2', name = 'Pistol Mk II', price = 700, img = getWeaponImage('WEAPON_PISTOL_MK2'), category = 'sidearm' },
        { weapon = 'WEAPON_CERAMICPISTOL', name = 'Ceramic Pistol', price = 800, img = getWeaponImage('WEAPON_CERAMICPISTOL'), category = 'sidearm' },
        -- Throwables

-- Weapon selection callback
RegisterNUICallback('selectWeapon', function(data, cb)
  print('[KOTH] Weapon selected:', data.weapon or 'none', 'for class:', data.class or 'none', 'price:', data.price or 'none', 'type:', data.purchaseType or 'buy')
  if data and data.weapon and data.class then
    TriggerServerEvent('koth:selectLoadout', data.class, data.weapon, data.price, data.purchaseType)
  end
  -- Don't hide the UI immediately - let the server response handle it
  cb('ok')
end)

-- Handle weapon shop closing from server
RegisterNetEvent('koth:closeWeaponShop', function()
  print('[KOTH] Closing weapon shop UI')
  SendNUIMessage({ action='hideAll' })
  SetNuiFocus(false, false)
end)

-- Receive weapon from server and give to player
RegisterNetEvent('koth:giveWeapon', function(weapon, classId, price)
  print(('[KOTH] Receiving weapon: %s for class: %s (price: $%s)'):format(weapon or 'none', classId or 'none', price or 'free'))

  if weapon then
    local playerPed = PlayerPedId()

    -- Remove all weapons first
    RemoveAllPedWeapons(playerPed, true)

    -- Give the selected weapon with ammo
    local weaponHash = GetHashKey(weapon)
    GiveWeaponToPed(playerPed, weaponHash, 250, false, true)

    -- Set as current weapon
    SetCurrentPedWeapon(playerPed, weaponHash, true)

    print(('[KOTH] Successfully equipped %s'):format(weapon))

    -- Add weapon to hotbar slot 1
    if exports['hotbar'] then
      -- Get weapon display name (remove WEAPON_ prefix and format nicely)
      local weaponDisplayName = weapon:gsub('WEAPON_', ''):gsub('_', ' ')
      -- Capitalize first letter of each word
      weaponDisplayName = weaponDisplayName:gsub("(%a)([%w_']*)", function(first, rest)
        return first:upper() .. rest:lower()
      end)
      
      -- Get weapon image path based on weapon name
      local weaponImageMap = {
        -- Pistols
        ['WEAPON_PISTOL'] = "images/weapon_pistol50.png",
        ['WEAPON_PISTOL_MK2'] = "images/weapon_pistol50.png",
        ['WEAPON_COMBATPISTOL'] = "images/weapon_pistol50.png",
        ['WEAPON_APPISTOL'] = "images/weapon_appistol.png",
        ['WEAPON_PISTOL50'] = "images/weapon_pistol50.png",
        ['WEAPON_SNSPISTOL'] = "images/weapon_pistol50.png",
        ['WEAPON_SNSPISTOL_MK2'] = "images/weapon_pistol50.png",
        ['WEAPON_HEAVYPISTOL'] = "images/weapon_pistol50.png",
        ['WEAPON_VINTAGEPISTOL'] = "images/weapon_pistol50.png",
        ['WEAPON_CERAMICPISTOL'] = "images/weapon_pistol50.png",
        ['WEAPON_DOUBLEACTION'] = "images/weapon_pistol50.png",
        ['WEAPON_REVOLVER'] = "images/weapon_pistol50.png",
        ['WEAPON_REVOLVER_MK2'] = "images/weapon_pistol50.png",
        ['WEAPON_NAVYREVOLVER'] = "images/weapon_pistol50.png",
        -- SMGs
        ['WEAPON_MICROSMG'] = "images/weapon_microsmg.png",
        ['WEAPON_SMG'] = "images/weapon_smg.png",
        ['WEAPON_SMG_MK2'] = "images/weapon_smg.png",
        ['WEAPON_ASSAULTSMG'] = "images/weapon_assaultsmg.png",
        ['WEAPON_COMBATPDW'] = "images/weapon_combatpdw.png",
        ['WEAPON_MACHINEPISTOL'] = "images/weapon_machinepistol.png",
        ['WEAPON_MINISMG'] = "images/weapon_minismg.png",
        -- Assault Rifles
        ['WEAPON_ASSAULTRIFLE'] = "images/weapon_assaultrifle.png",
        ['WEAPON_ASSAULTRIFLE_MK2'] = "images/weapon_assaultrifle_mk2.png",
        ['WEAPON_CARBINERIFLE'] = "images/weapon_m4a1.png",
        ['WEAPON_CARBINERIFLE_MK2'] = "images/weapon_m4a1.png",
        ['WEAPON_ADVANCEDRIFLE'] = "images/weapon_advancedrifle.png",
        ['WEAPON_SPECIALCARBINE'] = "images/weapon_specialcarbine.png",
        ['WEAPON_SPECIALCARBINE_MK2'] = "images/weapon_specialcarbine_mk2.png",
        ['WEAPON_BULLPUPRIFLE'] = "images/weapon_bullpuprifle.png",
        ['WEAPON_BULLPUPRIFLE_MK2'] = "images/weapon_bullpuprifle_mk2.png",
        ['WEAPON_COMPACTRIFLE'] = "images/weapon_assaultrifle.png",
        ['WEAPON_MILITARYRIFLE'] = "images/weapon_assaultrifle.png",
        ['WEAPON_HEAVYRIFLE'] = "images/weapon_assaultrifle.png",
        -- LMGs
        ['WEAPON_MG'] = "images/weapon_combatmg.png",
        ['WEAPON_COMBATMG'] = "images/weapon_combatmg.png",
        ['WEAPON_COMBATMG_MK2'] = "images/weapon_combatmg.png",
        ['WEAPON_GUSENBERG'] = "images/weapon_combatmg.png",
        ['WEAPON_MINIGUN'] = "images/weapon_combatmg.png",
        -- Snipers
        ['WEAPON_SNIPERRIFLE'] = "images/weapon_marksmanrifle.png",
        ['WEAPON_SNIPERRIFLE_MK2'] = "images/weapon_marksmanrifle.png",
        ['WEAPON_HEAVYSNIPER'] = "images/weapon_marksmanrifle.png",
        ['WEAPON_HEAVYSNIPER_MK2'] = "images/weapon_marksmanrifle.png",
        ['WEAPON_MARKSMANRIFLE'] = "images/weapon_marksmanrifle.png",
        ['WEAPON_MARKSMANRIFLE_MK2'] = "images/weapon_marksmanrifle.png",
        -- Shotguns
        ['WEAPON_PUMPSHOTGUN'] = "images/weapon_assaultrifle.png",
        ['WEAPON_PUMPSHOTGUN_MK2'] = "images/weapon_assaultrifle.png",
        ['WEAPON_COMBATSHOTGUN'] = "images/weapon_assaultrifle.png",
        ['WEAPON_ASSAULTSHOTGUN'] = "images/weapon_assaultrifle.png",
        ['WEAPON_HEAVYSHOTGUN'] = "images/weapon_assaultrifle.png",
        ['WEAPON_SWEEPERSHOTGUN'] = "images/weapon_assaultrifle.png",
        -- Throwables (using default image)
        ['WEAPON_GRENADE'] = "images/weapon_pistol50.png",
        ['WEAPON_STICKYBOMB'] = "images/weapon_pistol50.png",
        ['WEAPON_PIPEBOMB'] = "images/weapon_pistol50.png",
        ['WEAPON_BZGAS'] = "images/weapon_pistol50.png",
        ['WEAPON_MOLOTOV'] = "images/weapon_pistol50.png",
        ['WEAPON_PROXMINE'] = "images/weapon_pistol50.png"
      }
      
      local weaponIcon = weaponImageMap[weapon] or "images/weapon_pistol50.png"
      
      -- Call hotbar export to set weapon in slot 1
      exports['hotbar']:SetHotbarItem(1, weaponDisplayName, weaponIcon, 250, weaponHash)
      print(('[KOTH] Added %s to hotbar slot 1 with icon %s'):format(weaponDisplayName, weaponIcon))
    else
      print('[KOTH] Hotbar resource not found - weapon not added to hotbar')
    end

    -- Show notification with price
    BeginTextCommandThefeedPost("STRING")
    local weaponName = weapon:gsub('WEAPON_', ''):gsub('_', ' ')
    local message = ('Purchased %s for $%s'):format(weaponName, price or '0')
    AddTextComponentSubstringPlayerName(message)
    EndTextCommandThefeedPostTicker(false, true)
  end
end)

-- Purchase result handler
RegisterNetEvent('koth:purchaseResult', function(success, message)
  print(('[KOTH] Purchase result: %s - %s'):format(success and 'SUCCESS' or 'FAILED', message))

  -- Show notification
  BeginTextCommandThefeedPost("STRING")
  AddTextComponentSubstringPlayerName(message)
  EndTextCommandThefeedPostTicker(false, true)

  -- If purchase failed, keep the shop open
  if not success then
    -- You could add specific UI feedback here
    print('[KOTH] Purchase failed, keeping shop open')
  end
end)

-- Attachment purchase callback
RegisterNUICallback('purchaseAttachment', function(data, cb)
  print('[KOTH] Purchasing attachment:', data.name or 'none', 'component:', data.component or 'none', 'price:', data.price or 'none')
  if data and data.name and data.component then
    TriggerServerEvent('koth:purchaseAttachment', data)
  end
  SendNUIMessage({ action='hideAll' })
  SetNuiFocus(false, false)
  cb('ok')
end)

-- Apply attachment to weapon
RegisterNetEvent('koth:applyAttachment', function(component)
  print('[KOTH] Applying attachment component:', component)
  
  local playerPed = PlayerPedId()
  local currentWeapon = GetSelectedPedWeapon(playerPed)
  
  if currentWeapon ~= GetHashKey('WEAPON_UNARMED') then
    -- Apply the attachment component to the current weapon
    GiveWeaponComponentToPed(playerPed, currentWeapon, GetHashKey(component))
    print('[KOTH] Successfully applied attachment component', component, 'to weapon', currentWeapon)
    
    -- Update hotbar if available
    if exports['hotbar'] then
      -- Get weapon display name
      local weaponName = GetWeaponDisplayName(currentWeapon)
      local weaponIcon = GetWeaponIcon(currentWeapon)
      local ammo = GetAmmoInPedWeapon(playerPed, currentWeapon)
      
      -- Update hotbar slot 1 with the modified weapon
      exports['hotbar']:SetHotbarItem(1, weaponName, weaponIcon, ammo, currentWeapon)
      print('[KOTH] Updated hotbar with modified weapon')
    end
  else
    print('[KOTH] No weapon equipped to apply attachment to')
  end
end)

-- Helper function to get weapon display name
function GetWeaponDisplayName(weaponHash)
  local weaponName = 'Unknown Weapon'
  
  -- Convert hash to weapon name
  for weapon, hash in pairs({
    ['WEAPON_PISTOL'] = GetHashKey('WEAPON_PISTOL'),
    ['WEAPON_COMBATPISTOL'] = GetHashKey('WEAPON_COMBATPISTOL'),
    ['WEAPON_APPISTOL'] = GetHashKey('WEAPON_APPISTOL'),
    ['WEAPON_ASSAULTRIFLE'] = GetHashKey('WEAPON_ASSAULTRIFLE'),
    ['WEAPON_CARBINERIFLE'] = GetHashKey('WEAPON_CARBINERIFLE'),
    ['WEAPON_SMG'] = GetHashKey('WEAPON_SMG'),
    ['WEAPON_SNIPERRIFLE'] = GetHashKey('WEAPON_SNIPERRIFLE')
  }) do
    if hash == weaponHash then
      weaponName = weapon:gsub('WEAPON_', ''):gsub('_', ' ')
      weaponName = weaponName:gsub("(%a)([%w_']*)", function(first, rest)
        return first:upper() .. rest:lower()
      end)
      break
    end
  end
  
  return weaponName
end

-- Helper function to get weapon icon
function GetWeaponIcon(weaponHash)
  local weaponImageMap = {
    [GetHashKey('WEAPON_PISTOL')] = "images/weapon_pistol50.png",
    [GetHashKey('WEAPON_COMBATPISTOL')] = "images/weapon_pistol50.png",
    [GetHashKey('WEAPON_APPISTOL')] = "images/weapon_appistol.png",
    [GetHashKey('WEAPON_ASSAULTRIFLE')] = "images/weapon_assaultrifle.png",
    [GetHashKey('WEAPON_CARBINERIFLE')] = "images/weapon_m4a1.png",
    [GetHashKey('WEAPON_SMG')] = "images/weapon_smg.png",
    [GetHashKey('WEAPON_SNIPERRIFLE')] = "images/weapon_marksmanrifle.png"
  }
  
  return weaponImageMap[weaponHash] or "images/weapon_pistol50.png"
end

-- Close menu callback
RegisterNUICallback('closeMenu', function(data, cb)
  print('[KOTH] Menu closed')
  SendNUIMessage({ action='hideAll' })
  SetNuiFocus(false, false)
  cb('ok')
end)

-- Vehicle spawning handler
RegisterNetEvent('koth:spawnVehicle', function(vehicleName, purchaseType, price)
  print(('[KOTH] Spawning vehicle: %s (%s for $%s)'):format(vehicleName or 'none', purchaseType or 'unknown', price or '0'))

  if vehicleName then
    local playerPed = PlayerPedId()
    local playerCoords = GetEntityCoords(playerPed)
    local playerHeading = GetEntityHeading(playerPed)

    -- Vehicle name mapping (simplified)
    local vehicleModels = {
      ['Blista'] = 'blista',
      ['Futo'] = 'futo',
      ['Sultan'] = 'sultan',
      ['Elegy'] = 'elegy2',
      ['Kuruma'] = 'kuruma',
      ['Armored Kuruma'] = 'kuruma2',
      ['Insurgent'] = 'insurgent',
      ['Technical'] = 'technical',
      ['Sandking XL'] = 'sandking',
      ['Mesa'] = 'mesa',
      ['Buzzard'] = 'buzzard2',
      ['Savage'] = 'savage',
      ['Rhino Tank'] = 'rhino',
      ['Hydra'] = 'hydra'
    }

    local modelName = vehicleModels[vehicleName] or vehicleName:lower()
    local modelHash = GetHashKey(modelName)

    -- Request model
    RequestModel(modelHash)
    local timeout = GetGameTimer() + 5000
    while not HasModelLoaded(modelHash) and GetGameTimer() < timeout do
      Citizen.Wait(1)
    end

    if not HasModelLoaded(modelHash) then
      print(('[KOTH] Initial model load failed for %s, retrying...'):format(modelName))
      RequestModel(modelHash)
      local retryTimeout = GetGameTimer() + 5000
      while not HasModelLoaded(modelHash) and GetGameTimer() < retryTimeout do
        Citizen.Wait(1)
      end
    end

    if HasModelLoaded(modelHash) then
      -- Find spawn position (in front of player)
      local spawnX = playerCoords.x + math.cos(math.rad(playerHeading)) * 5.0
      local spawnY = playerCoords.y + math.sin(math.rad(playerHeading)) * 5.0

      -- Get proper ground Z coordinate to prevent falling through map
      local foundGround, groundZ = GetGroundZFor_3dCoord(spawnX, spawnY, playerCoords.z + 50.0, false)
      local spawnZ = foundGround and (groundZ + 1.0) or (playerCoords.z + 1.0)

      -- Create vehicle
      local vehicle = CreateVehicle(modelHash, spawnX, spawnY, spawnZ, playerHeading, true, false)

      if DoesEntityExist(vehicle) then
        print(('[KOTH] Vehicle entity created: %s'):format(vehicle))
        -- Ensure vehicle is properly placed on ground
        SetEntityCoords(vehicle, spawnX, spawnY, spawnZ, false, false, false, true)
        SetVehicleOnGroundProperly(vehicle)

        -- Set player as owner
        SetVehicleHasBeenOwnedByPlayer(vehicle, true)
        -- Mark vehicle as player-owned for other resources
        DecorSetBool(vehicle, "PlayerVehicle", true)
        if Entity(vehicle) then
          Entity(vehicle).state:set('playerOwned', true, true)
        end
        
        -- Debug: Verify the decorator was set
        local decorSet = DecorExistOn(vehicle, "PlayerVehicle") and DecorGetBool(vehicle, "PlayerVehicle")
        print(('[KOTH] Vehicle decorator "PlayerVehicle" set: %s'):format(tostring(decorSet)))
        
        -- Make vehicle persistent
        SetEntityAsMissionEntity(vehicle, true, true)
        
        -- Set network ownership
        local netId = NetworkGetNetworkIdFromEntity(vehicle)
        SetNetworkIdCanMigrate(netId, true)
        SetNetworkIdExistsOnAllMachines(netId, true)
        
        -- Ensure vehicle has proper collision with ground
        SetEntityCollision(vehicle, true, true)

        -- Wait a moment for physics to settle
        Citizen.Wait(100)

        -- Put player in vehicle
        TaskWarpPedIntoVehicle(playerPed, vehicle, -1)

        print(('[KOTH] Successfully spawned %s at ground level - marked as player-owned'):format(vehicleName))

        -- Show notification
        BeginTextCommandThefeedPost("STRING")
        -- Handle price being a table or number
        local priceValue = price
        if type(price) == 'table' then
          priceValue = price.price or price.cost or price.rent or '0'
        end
        local message = ('%s %s for $%s'):format(purchaseType == 'rent' and 'Rented' or 'Purchased', vehicleName, tostring(priceValue))
        AddTextComponentSubstringPlayerName(message)
        EndTextCommandThefeedPostTicker(false, true)
      else
        print(('[KOTH] Failed to create vehicle: %s'):format(vehicleName))
      end

      SetModelAsNoLongerNeeded(modelHash)
    else
      print(('[KOTH] Failed to load model: %s'):format(modelName))
    end
  end
end)

-- PED SPAWNING SYSTEM
local spawnedPeds = {}

-- Ped configurations for each team
local pedSpawns = {
  red = {
    { model = `s_m_y_marine_01`,   offset = vector3( 3.0,  2.0, 0.0), text = 'Click [E] To Open Classes Menu',  event = 'koth:openClassMenu'  },
    { model = `s_m_m_autoshop_02`, offset = vector3(-3.0, -2.0, 0.0), text = 'Click [E] To Open Vehicle Menu', event = 'koth:openVehicleMenu' },
    { model = `s_m_y_xmech_01`,    offset = vector3( 0.0,  4.0, 0.0), text = 'Click [E] To Open The Attachment Menu', event = 'koth:openAttachmentMenu' },
  },
  blue = {
    { model = `s_m_y_marine_01`,   offset = vector3( 3.0,  2.0, 0.0), text = 'Click [E] To Open Classes Menu',  event = 'koth:openClassMenu'  },
    { model = `s_m_m_autoshop_02`, offset = vector3(-3.0, -2.0, 0.0), text = 'Click [E] To Open Vehicle Menu', event = 'koth:openVehicleMenu' },
    { model = `s_m_y_xmech_01`,    offset = vector3( 0.0,  4.0, 0.0), text = 'Click [E] To Open The Attachment Menu', event = 'koth:openAttachmentMenu' },
  },
  green = {
    { model = `s_m_y_marine_01`,   offset = vector3( 3.0,  2.0, 0.0), text = 'Click [E] To Open Classes Menu',  event = 'koth:openClassMenu'  },
    { model = `s_m_m_autoshop_02`, offset = vector3(-3.0, -2.0, 0.0), text = 'Click [E] To Open Vehicle Menu', event = 'koth:openVehicleMenu' },
    { model = `s_m_y_xmech_01`,    offset = vector3( 0.0,  4.0, 0.0), text = 'Click [E] To Open The Attachment Menu', event = 'koth:openAttachmentMenu' },
  },
}

-- 3D text helper function
local function Draw3DText(x, y, z, text)
  local onScreen, _x, _y = World3dToScreen2d(x, y, z + 1.0)
  if onScreen then
    SetTextScale(0.35, 0.35)
    SetTextFont(4)
    SetTextCentre(true)
    SetTextColour(255, 255, 255, 215)
    SetTextEntry("STRING")
    AddTextComponentString(text)
    DrawText(_x, _y)
    local factor = (#text) / 370
    DrawRect(_x, _y + 0.0125, 0.015 + factor, 0.03, 0, 0, 0, 120)
  end
end

-- Spawn peds when resource starts
AddEventHandler('onClientResourceStart', function(res)
  if GetCurrentResourceName() ~= res then return end

  Citizen.CreateThread(function()
    print('[KOTH] Starting ped spawning...')
    Citizen.Wait(3000) -- Wait for everything to load

    for team, list in pairs(pedSpawns) do
      local sv = teamSpawns[team]
      if not sv then
        print('[KOTH] WARNING: No spawn data for team ' .. team)
      else
        local basePos = vector3(sv.x, sv.y, sv.z)
        print(('[KOTH] Spawning peds for team %s at %.2f, %.2f, %.2f'):format(team, basePos.x, basePos.y, basePos.z))

        for _, info in ipairs(list) do
          -- Validate model
          if not (IsModelValid(info.model) and IsModelInCdimage(info.model)) then
            print(('[KOTH] SKIP: Invalid model %s for team %s'):format(info.model, team))
          else
            -- Load model
            RequestModel(info.model)
            local timeout = GetGameTimer() + 5000
            while not HasModelLoaded(info.model) and GetGameTimer() < timeout do
              Citizen.Wait(1)
            end

            if not HasModelLoaded(info.model) then
              print(('[KOTH] FAIL: Could not load model %s'):format(info.model))
            else
              -- Find ground level
              local spawnX, spawnY = basePos.x + info.offset.x, basePos.y + info.offset.y
              local foundGround, groundZ = GetGroundZFor_3dCoord(spawnX, spawnY, basePos.z + 50.0, false)
              local finalZ = foundGround and groundZ or basePos.z

              -- Spawn ped
              local ped = CreatePed(4, info.model, spawnX, spawnY, finalZ, sv.heading or 0.0, false, true)

              if DoesEntityExist(ped) then
                SetEntityHeading(ped, sv.heading or 0.0)
                FreezeEntityPosition(ped, true)
                SetEntityInvincible(ped, true)
                SetBlockingOfNonTemporaryEvents(ped, true)

                table.insert(spawnedPeds, {
                  ped = ped,
                  text = info.text,
                  event = info.event
                })

                print(('[KOTH] SUCCESS: Spawned %s ped for team %s'):format(info.model, team))
              else
                print(('[KOTH] FAIL: CreatePed failed for %s'):format(info.model))
              end

              SetModelAsNoLongerNeeded(info.model)
            end
          end
        end
      end
    end

    print(('[KOTH] Ped spawning complete. Total peds: %d'):format(#spawnedPeds))
  end)
end)

-- Interaction loop for peds
Citizen.CreateThread(function()
  while true do
    local playerPed = PlayerPedId()
    local playerCoords = GetEntityCoords(playerPed)

    for _, info in ipairs(spawnedPeds) do
      if DoesEntityExist(info.ped) then
        local pedCoords = GetEntityCoords(info.ped)
        local distance = #(playerCoords - pedCoords)

        if distance < 10.0 then
          Draw3DText(pedCoords.x, pedCoords.y, pedCoords.z, info.text)

          if distance < 1.5 and IsControlJustReleased(0, 38) then -- E key
            print(('[KOTH] Player interacted with ped: %s'):format(info.event))
            TriggerEvent(info.event)
          end
        end
      end
    end

    Citizen.Wait(0)
  end
end)

-- SAFE ZONE SYSTEM
local inSafeZone = false
local safeZoneRadius = 250.0 -- Radius of safe zones (increased 10x from 25.0)
local teamBlips = {} -- Store map blips

-- Zone colors for each team
local zoneColors = {
  red = { r = 255, g = 0, b = 0, a = 50 },
  blue = { r = 0, g = 100, b = 255, a = 50 },
  green = { r = 0, g = 255, b = 0, a = 50 }
}

-- Map blip colors (GTA blip color IDs)
local blipColors = {
  red = 1,    -- Red
  blue = 3,   -- Blue
  green = 2   -- Green
}

-- Safe zone check thread
Citizen.CreateThread(function()
  while true do
    if playerTeam then
      local playerPed = PlayerPedId()
      local playerCoords = GetEntityCoords(playerPed)
      local wasInSafeZone = inSafeZone
      inSafeZone = false
      
      -- Check all team spawns for safe zones
      for team, spawn in pairs(teamSpawns) do
        local distance = #(playerCoords - vector3(spawn.x, spawn.y, spawn.z))
        
        if distance <= safeZoneRadius then
          inSafeZone = true
          
          -- Disable PVP in safe zones
          SetCanAttackFriendly(playerPed, false, false)
          NetworkSetFriendlyFireOption(false)
          
          -- Removed safe zone marker to prevent flashing
          
          -- Show safe zone notification
          if not wasInSafeZone then
            BeginTextCommandThefeedPost("STRING")
            AddTextComponentSubstringPlayerName("Entered Safe Zone - PVP Disabled")
            EndTextCommandThefeedPostTicker(false, true)
          end
          
          break
        end
      end
      
      -- Re-enable PVP when leaving safe zone
      if not inSafeZone and wasInSafeZone then
        SetCanAttackFriendly(playerPed, true, false)
        NetworkSetFriendlyFireOption(true)
        
        BeginTextCommandThefeedPost("STRING")
        AddTextComponentSubstringPlayerName("Left Safe Zone - PVP Enabled")
        EndTextCommandThefeedPostTicker(false, true)
      end
    end
    
    Citizen.Wait(100)
  end
end)

-- KOTH ZONE SYSTEM
local kothZone = {
  x = 2842.4216,
  y = 2864.8088,
  z = 62.5975,
  radius = 250.0, -- 500m diameter zone (250m radius)
  controllingTeam = nil, -- 'red', 'blue', 'green', or nil for neutral
  captureProgress = 0.0, -- 0.0 to 100.0
  captureRate = 1.0, -- Points per second when capturing
  playersInZone = {}, -- Track players in zone by team
  blip = nil, -- Map blip for the zone
  centerBlip = nil -- Center marker blip
}

local inKothZone = false
local kothZoneColors = {
  neutral = { r = 128, g = 128, b = 128, a = 50 }, -- Grey
  red = { r = 255, g = 0, b = 0, a = 50 },
  blue = { r = 0, g = 100, b = 255, a = 50 },
  green = { r = 0, g = 255, b = 0, a = 50 }
}

local kothBlipColors = {
  neutral = 8, -- Grey
  red = 1,     -- Red
  blue = 3,    -- Blue
  green = 2    -- Green
}

-- Create KOTH zone blip
local function createKothBlip()
  print('[KOTH] Creating KOTH zone blip...')

  -- Create radius blip
  kothZone.blip = AddBlipForRadius(kothZone.x, kothZone.y, kothZone.z, kothZone.radius)
  SetBlipColour(kothZone.blip, kothBlipColors.neutral)
  SetBlipAlpha(kothZone.blip, 128)

  -- Create center blip
  kothZone.centerBlip = AddBlipForCoord(kothZone.x, kothZone.y, kothZone.z)
  SetBlipSprite(kothZone.centerBlip, 437) -- King crown icon
  SetBlipColour(kothZone.centerBlip, kothBlipColors.neutral)
  SetBlipScale(kothZone.centerBlip, 1.5) -- Larger than team blips
  SetBlipAsShortRange(kothZone.centerBlip, false) -- Always visible

  -- Set blip name
  BeginTextCommandSetBlipName("STRING")
  AddTextComponentSubstringPlayerName("KOTH - Quarry (Neutral)")
  EndTextCommandSetBlipName(kothZone.centerBlip)

  print(('[KOTH] Created KOTH zone blip at %.2f, %.2f'):format(kothZone.x, kothZone.y))
end

-- Create map blips for team zones
local function createTeamBlips()
  print('[KOTH] Creating team zone blips...')

  for team, spawn in pairs(teamSpawns) do
    local blip = AddBlipForRadius(spawn.x, spawn.y, spawn.z, safeZoneRadius)

    -- Set blip properties
    SetBlipColour(blip, blipColors[team] or 0)
    SetBlipAlpha(blip, 128) -- Semi-transparent

    -- Create center blip for the team
    local centerBlip = AddBlipForCoord(spawn.x, spawn.y, spawn.z)
    SetBlipSprite(centerBlip, 84) -- Shield icon
    SetBlipColour(centerBlip, blipColors[team] or 0)
    SetBlipScale(centerBlip, 1.0)
    SetBlipAsShortRange(centerBlip, true)

    -- Set blip name
    BeginTextCommandSetBlipName("STRING")
    AddTextComponentSubstringPlayerName(team:upper() .. " Team Safe Zone")
    EndTextCommandSetBlipName(centerBlip)

    -- Store blips for cleanup
    teamBlips[team] = {
      radius = blip,
      center = centerBlip
    }

    print(('[KOTH] Created %s team blip at %.2f, %.2f'):format(team, spawn.x, spawn.y))
  end
  
  -- Create KOTH zone blip
  createKothBlip()
end

-- Initialize blips when resource starts
AddEventHandler('onClientResourceStart', function(res)
  if GetCurrentResourceName() ~= res then return end
  
  Citizen.SetTimeout(1000, function()
    createTeamBlips()
  end)
end)

-- Update KOTH zone blip color and name
local function updateKothBlip()
  if not DoesBlipExist(kothZone.blip) or not DoesBlipExist(kothZone.centerBlip) then
    return
  end

  local team = kothZone.controllingTeam or 'neutral'
  local color = kothBlipColors[team] or kothBlipColors.neutral

  -- Update colors
  SetBlipColour(kothZone.blip, color)
  SetBlipColour(kothZone.centerBlip, color)

  -- Update name
  BeginTextCommandSetBlipName("STRING")
  if team == 'neutral' then
    AddTextComponentSubstringPlayerName("KOTH - Quarry (Neutral)")
  else
    AddTextComponentSubstringPlayerName(("KOTH - Quarry (%s)"):format(team:upper()))
  end
  EndTextCommandSetBlipName(kothZone.centerBlip)
end

-- KOTH zone tracking thread
Citizen.CreateThread(function()
  while true do
    if playerTeam then
      local playerPed = PlayerPedId()
      local playerCoords = GetEntityCoords(playerPed)
      local distance = #(playerCoords - vector3(kothZone.x, kothZone.y, kothZone.z))

      local wasInZone = inKothZone
      inKothZone = distance <= kothZone.radius

      -- Player entered zone
      if inKothZone and not wasInZone then
        print('[KOTH] Player entered KOTH zone')
        TriggerServerEvent('koth:playerEnteredZone', playerTeam)
      end

      -- Player left zone
      if not inKothZone and wasInZone then
        print('[KOTH] Player left KOTH zone')
        TriggerServerEvent('koth:playerLeftZone', playerTeam)
      end

      -- Removed KOTH zone marker to prevent flashing
    end

    Citizen.Wait(100)
  end
end)

-- Update zone status from server
RegisterNetEvent('koth:updateZoneStatus', function(status)
  kothZone.controllingTeam = status.controllingTeam
  kothZone.captureProgress = status.captureProgress
  kothZone.captureThreshold = status.captureThreshold
  kothZone.playersInZone = status.playersInZone
  kothZone.dominantTeam = status.dominantTeam
  kothZone.isContested = status.isContested

  -- Update blip color
  updateKothBlip()
  
  -- No UI updates - everything is in chat now
end)

-- Zone control changed event
RegisterNetEvent('koth:zoneControlChanged', function(newTeam)
  print(('[KOTH] Zone control changed to: %s'):format(newTeam or 'neutral'))
  kothZone.controllingTeam = newTeam
  updateKothBlip()
end)

-- Update team counts for HUD
RegisterNetEvent('koth:updateTeamCounts', function(counts)
  print('[KOTH] Received team counts from server:', json.encode(counts))
  SendNUIMessage({
    action = 'updateTeamCounts',
    counts = counts
  })
  print('[KOTH] Sent team counts to NUI')
end)

-- Update zone points for HUD
RegisterNetEvent('koth:updateZonePoints', function(points)
  SendNUIMessage({
    action = 'updateZonePoints',
    points = points
  })
end)

-- PVP SYSTEM
RegisterNetEvent('koth:enablePVP', function()
  print('[KOTH] Enabling PVP')
  local playerPed = PlayerPedId()
  SetCanAttackFriendly(playerPed, true, false)
  NetworkSetFriendlyFireOption(true)
end)



-- Show level up popup
RegisterNetEvent('koth:levelUp', function(data)
  SendNUIMessage({
    action = 'showLevelUp',
    oldLevel = data.oldLevel,
    newLevel = data.newLevel
  })
  
  -- Play level up sound
  PlaySoundFrontend(-1, "RANK_UP", "HUD_AWARDS", true)
end)

-- Health bar update REMOVED as requested - user will integrate their own health system later

-- Initialize HUD when resource starts
AddEventHandler('onClientResourceStart', function(res)
  if GetCurrentResourceName() ~= res then return end
  
  -- Request initial player data
  Citizen.SetTimeout(1000, function()
    TriggerServerEvent('koth:requestPlayerData')
  end)
end)

-- DATABASE SYNC SYSTEM - Periodic updates to keep HUD synced
Citizen.CreateThread(function()
  while true do
    -- Request fresh player data from database every 30 seconds
    if hasSelectedTeam then
      TriggerServerEvent('koth:requestPlayerData')
      print('[KOTH] Requesting periodic database sync for HUD')
    end
    
    Citizen.Wait(30000) -- 30 seconds
  end
end)

-- Force database sync when player performs actions that might change data
local function forceDatabaseSync()
  print('[KOTH] Forcing database sync after player action')
  TriggerServerEvent('koth:requestPlayerData')
end

-- Hook into purchase events to force sync
RegisterNetEvent('koth:purchaseResult', function(success, message)
  print(('[KOTH] Purchase result: %s - %s'):format(success and 'SUCCESS' or 'FAILED', message))

  -- Show notification
  BeginTextCommandThefeedPost("STRING")
  AddTextComponentSubstringPlayerName(message)
  EndTextCommandThefeedPostTicker(false, true)

  -- Force database sync after any purchase (successful or not)
  Citizen.SetTimeout(1000, function()
    forceDatabaseSync()
  end)

  -- If purchase failed, keep the shop open
  if not success then
    print('[KOTH] Purchase failed, keeping shop open')
  end
end)

-- Debug command to manually show team selection
RegisterCommand('showteamselect', function()
  print('[KOTH] Manually showing team selection UI')
  -- Reset team selection state for testing
  hasSelectedTeam = false
  playerTeam = nil
  DeleteResourceKvp('playerTeam')
  
  SendNUIMessage({ 
    action = 'showTeamSelect', 
    counts = { red = 0, blue = 0, green = 0 }
  })
  SetNuiFocus(true, true)
end, false)

-- Command to reset team selection
RegisterCommand('resetteam', function()
  print('[KOTH] Resetting team selection')
  hasSelectedTeam = false
  playerTeam = nil
  DeleteResourceKvp('playerTeam')
  print('[KOTH] Team selection reset - rejoin or use /showteamselect to pick a new team')
end, false)

-- Debug command to test kill reward popup
RegisterCommand('testkillreward', function(source, args)
  local inZone = args[1] == 'zone' or args[1] == 'true'
  local xp = inZone and 150 or 50
  local money = inZone and 150 or 50
  
  print('[KOTH] Testing kill reward popup - In Zone:', inZone, 'XP:', xp, 'Money:', money)
  
  -- Simulate receiving a kill reward
  TriggerEvent('koth:showKillReward', {
    xp = xp,
    money = money,
    inZone = inZone,
    victimName = 'Test Player'
  })
end, false)

-- Debug command to test database sync
RegisterCommand('syncdata', function()
  print('[KOTH] Manually requesting database sync')
  TriggerServerEvent('koth:requestPlayerData')
end, false)

-- Debug command to test kill reward popup directly
RegisterCommand('testkillpopup', function(source, args)
  local inZone = args[1] == 'zone' or args[1] == 'true'
  local xp = inZone and 150 or 50
  local money = inZone and 150 or 50
  
  print('[KOTH] Testing kill reward popup directly - In Zone:', inZone, 'XP:', xp, 'Money:', money)
  
  -- Send directly to NUI to test popup display
  SendNUIMessage({
    action = 'showKillReward',
    xp = xp,
    money = money,
    inZone = inZone,
    victim = 'Test Player'
  })
  
  -- Also update player stats to simulate a real kill
  if playerStats then
    playerStats.money = (playerStats.money or 0) + money
    playerStats.xp = (playerStats.xp or 0) + xp
    playerStats.kills = (playerStats.kills or 0) + 1
    
    -- Update HUD
    SendNUIMessage({
      action = 'updatePlayerData',
      data = playerStats
    })
  end
  
  print('[KOTH] Kill reward popup test sent to NUI')
end, false)

-- Force kill reward display command
RegisterCommand('forcekillreward', function()
  print('[KOTH] Forcing kill reward display...')
  
  -- Send multiple attempts to ensure it shows
  for i = 1, 3 do
    Citizen.SetTimeout(i * 100, function()
      SendNUIMessage({
        action = 'showKillReward',
        xp = 150,
        money = 150,
        inZone = true,
        victim = 'Enemy Player'
      })
      print('[KOTH] Kill reward attempt', i, 'sent')
    end)
  end
end, false)

-- SIMPLE CLIENT TEST COMMAND
RegisterCommand('testui', function(source, args)
  local inZone = args[1] == 'zone' or args[1] == 'true'
  local xp = inZone and 150 or 50
  local money = inZone and 150 or 50
  
  print('[KOTH] Testing UI directly - Zone:', inZone, 'XP:', xp, 'Money:', money)
  
  -- Test the showKillReward function directly
  TriggerEvent('koth:showKillReward', {
    xp = xp,
    money = money,
    inZone = inZone,
    victimName = 'Test Victim'
  })
  
  print('[KOTH] UI test triggered')
end, false)

-- Debug command to check vehicle decorator
RegisterCommand('checkvehicle', function()
  local playerPed = PlayerPedId()
  local vehicle = GetVehiclePedIsIn(playerPed, false)
  
  if vehicle ~= 0 then
    local hasDecorator = DecorExistOn(vehicle, "PlayerVehicle")
    local decoratorValue = hasDecorator and DecorGetBool(vehicle, "PlayerVehicle") or false
    local isPlayerOwned = Entity(vehicle) and Entity(vehicle).state.playerOwned or false
    
    print('[KOTH] === Vehicle Debug Info ===')
    print('[KOTH] Vehicle entity:', vehicle)
    print('[KOTH] Has PlayerVehicle decorator:', hasDecorator)
    print('[KOTH] PlayerVehicle decorator value:', decoratorValue)
    print('[KOTH] Entity state playerOwned:', isPlayerOwned)
    print('[KOTH] =========================')
  else
    print('[KOTH] You are not in a vehicle')
  end
end, false)

print('[KOTH] Client loaded successfully')
