-- KILL SYSTEM FIX
-- This file contains the fixed AwardKillReward function with team validation

-- Fixed Award Kill Reward Function
function AwardKillRewardFixed(killerSource, victimSource, inZone)
  print('=== [KOTH SERVER] AWARD KILL REWARD START ===')
  print(('[KOTH] AwardKillReward called - Killer: %s (type: %s) | Victim: %s (type: %s) | In Zone: %s'):format(
    tostring(killerSource), type(killerSource), tostring(victimSource), type(victimSource), tostring(inZone)))

  -- Convert to number if needed (FiveM sometimes passes strings)
  local killerSourceNum = tonumber(killerSource)
  local victimSourceNum = tonumber(victimSource)

  print(('[KOTH] Converted IDs - Killer: %s | Victim: %s'):format(tostring(killerSourceNum), tostring(victimSourceNum)))

  -- Validate player IDs
  if not killerSourceNum or killerSourceNum <= 0 then
    print('[KOTH] Invalid killer ID')
    return
  end

  if not victimSourceNum or victimSourceNum <= 0 then
    print('[KOTH] Invalid victim ID')
    return
  end

  -- Check if same player (suicide)
  if killerSourceNum == victimSourceNum then
    print('[KOTH] Suicide detected, no reward')
    return
  end

  -- Check player data exists
  if not playerData[killerSourceNum] then
    print(('[KOTH] No player data found for killer %s'):format(killerSourceNum))
    return
  end

  if not playerData[victimSourceNum] then
    print(('[KOTH] No player data found for victim %s'):format(victimSourceNum))
    return
  end

  -- CHECK TEAMS - Only award if different teams
  local killerTeam = playerTeams[killerSourceNum]
  local victimTeam = playerTeams[victimSourceNum]
  
  print(('[KOTH] Team check - Killer team: %s | Victim team: %s'):format(killerTeam or 'none', victimTeam or 'none'))
  
  if not killerTeam or not victimTeam then
    print('[KOTH] One or both players have no team assigned, skipping reward')
    return
  end
  
  if killerTeam == victimTeam then
    print('[KOTH] Same team kill detected, no reward given')
    return
  end

  print(('[KOTH] Valid opposite team kill, processing reward...'):format())

  -- FIXED REWARD VALUES as requested:
  -- Out of zone: $50 and 50 XP
  -- In zone: $150 and 150 XP
  local xpReward = inZone and 150 or 50
  local moneyReward = inZone and 150 or 50

  -- Update killer stats
  local killerData = playerData[killerSourceNum]
  local oldLevel = killerData.level or 1
  local oldMoney = killerData.money or 0
  local oldXP = killerData.xp or 0

  killerData.money = (killerData.money or 0) + moneyReward
  killerData.xp = (killerData.xp or 0) + xpReward
  killerData.kills = (killerData.kills or 0) + 1

  if inZone then
    killerData.zone_kills = (killerData.zone_kills or 0) + 1
  end

  -- Calculate new level
  killerData.level = CalculateLevel(killerData.xp)

  -- Update victim stats
  local victimData = playerData[victimSourceNum]
  victimData.deaths = (victimData.deaths or 0) + 1

  -- Get player names properly
  local killerName = GetPlayerName(killerSourceNum)
  if not killerName or killerName == '' then
    killerName = killerData.player_name or 'Unknown'
  end

  local victimName = GetPlayerName(victimSourceNum)
  if not victimName or victimName == '' then
    victimName = victimData.player_name or 'Unknown'
  end

  print(('[KOTH] Player names - Killer: %s, Victim: %s'):format(killerName, victimName))
  print(('[KOTH] Rewards - XP: %d, Money: $%d'):format(xpReward, moneyReward))
  print(('[KOTH] Killer stats - Old Money: $%d -> New Money: $%d'):format(oldMoney, killerData.money))
  print(('[KOTH] Killer stats - Old XP: %d -> New XP: %d'):format(oldXP, killerData.xp))

  -- Send reward notification to killer
  print(('[KOTH] Sending kill reward to client %d'):format(killerSourceNum))
  
  TriggerClientEvent('koth:showKillReward', killerSourceNum, {
    xp = xpReward,
    money = moneyReward,
    inZone = inZone,
    victimName = victimName,
    killerTeam = killerTeam,
    victimTeam = victimTeam
  })

  -- Check for level up
  if killerData.level > oldLevel then
    print(('[KOTH] Level up! %d -> %d'):format(oldLevel, killerData.level))
    TriggerClientEvent('koth:levelUp', killerSourceNum, {
      newLevel = killerData.level,
      oldLevel = oldLevel
    })
  end

  -- Update client data
  TriggerClientEvent('koth:updatePlayerData', killerSourceNum, killerData)
  TriggerClientEvent('koth:updatePlayerData', victimSourceNum, victimData)

  -- Save data to database
  SavePlayerData(killerSourceNum)
  SavePlayerData(victimSourceNum)

  -- Announce kill in chat (optional)
  local killMessage = string.format('%s (%s) killed %s (%s) %s', 
    killerName, killerTeam, victimName, victimTeam, 
    inZone and 'in the KOTH Zone!' or '')
  
  TriggerClientEvent('chat:addMessage', -1, {
    color = inZone and {255, 215, 0} or {255, 255, 255},
    multiline = true,
    args = {'[KOTH]', killMessage}
  })

  print(('[KOTH] Kill reward complete: %s (%s) killed %s (%s) | XP: +%d | Money: +$%d | Zone: %s'):format(
    killerName,
    killerTeam,
    victimName,
    victimTeam,
    xpReward,
    moneyReward,
    inZone and 'YES' or 'NO'
  ))
  print('=== [KOTH SERVER] AWARD KILL REWARD END ===')
end

-- Override the original function
AwardKillReward = AwardKillRewardFixed

print('[KOTH] Kill system fix loaded - Team validation and proper rewards enabled')
