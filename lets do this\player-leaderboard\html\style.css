@import url('https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap');

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Roboto', sans-serif;
    background: transparent;
    color: white;
    overflow: hidden;
}

.hidden {
    display: none !important;
}

#leaderboard-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(0, 0, 0, 0);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.leaderboard-content {
    display: flex;
    width: 95%;
    height: 90%;
    gap: 20px;
}

/* Left Panel Styles */
.left-panel {
    flex: 0 0 400px;
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.player-info-section {
    background: rgba(124, 119, 136, 0.568);
    border-radius: 8px;
    padding: 20px;
    position: relative;
    overflow: hidden;
}

.section-header {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 20px;
    opacity: 0.9;
}

.player-info {
    margin-bottom: 30px;
}

.player-id {
    font-size: 24px;
    font-weight: 700;
    margin-bottom: 8px;
}

.player-level {
    font-size: 16px;
    opacity: 0.8;
    margin-bottom: 20px;
}

.stats-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
}

.stat-item {
    text-align: center;
}

.stat-icon {
    font-size: 20px;
    margin-bottom: 5px;
}

.stat-label {
    font-size: 12px;
    opacity: 0.7;
    margin-bottom: 5px;
}

.stat-value {
    font-size: 24px;
    font-weight: 700;
}



.challenges-section {
    border-radius: 8px;
    padding: 20px;
    flex: 1;
}

.challenges-timer {
    font-size: 14px;
    margin-bottom: 20px;
    opacity: 0.8;
}

.challenge-item {
    background: rgba(124, 119, 136, 0.568);
    border-radius: 6px;
    padding: 15px;
    margin-bottom: 15px;
    border-left: 4px solid #10b981;
}

.challenge-name {
    font-weight: 600;
    margin-bottom: 5px;
    color: #10b981;
}

.challenge-desc {
    font-size: 13px;
    opacity: 0.8;
    margin-bottom: 10px;
}

.challenge-progress {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 12px;
}

.challenge-reward {
    color: #fbbf24;
}

/* Right Panel Styles */
.right-panel {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.leaderboard-tables {
    display: flex;
    flex-direction: column;
    gap: 15px;
    height: 100%;
}

.team-section {
    border-radius: 8px;
    overflow: hidden;
}

.blue-team .team-header {
    background-color: #3b82f6a6;
}

.red-team .team-header {
    background-color: #ef4444a6;
}

.green-team .team-header {
    background-color: #189e71a6;
}

.blue-team .team-list,
.red-team .team-list,
.green-team .team-list {
    background: transparent;
}

.team-header {
    padding: 12px 15px;
    font-weight: 600;
    font-size: 13px;
}

.team-columns {
    display: grid;
    grid-template-columns: 80px 1fr 80px 60px 60px 60px 60px;
    gap: 10px;
    align-items: center;
}

.team-list {
    max-height: 200px;
    overflow-y: auto;
}

.player-row {
    display: grid;
    grid-template-columns: 80px 1fr 80px 60px 60px 60px 60px;
    gap: 10px;
    align-items: center;
    padding: 10px 15px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    font-size: 13px;
    background: transparent;
}

.player-row:last-child {
    border-bottom: none;
}

.player-row:hover {
    background: rgba(255, 255, 255, 0.05);
}

.col-uid {
    font-family: monospace;
    opacity: 0.8;
}

.col-player {
    font-weight: 500;
    color: white !important;
    display: flex;
    align-items: center;
    gap: 5px;
}

.col-score, .col-kills, .col-deaths, .col-assists, .col-kd {
    text-align: center;
    font-weight: 600;
}

/* Online indicator */
.online-indicator {
    color: #10b981;
    font-size: 8px;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

/* Player row states */
.player-row.online {
    background: rgba(16, 185, 129, 0.05);
}

.player-row.current-player {
    background: rgba(251, 191, 36, 0.1);
    border-left: 3px solid #fbbf24;
}

/* Rank highlights */
.player-row.rank-1 {
    background: rgba(255, 215, 0, 0.1);
}

.player-row.rank-1 .col-player::before {
    content: "🥇 ";
}

.player-row.rank-2 {
    background: rgba(192, 192, 192, 0.1);
}

.player-row.rank-2 .col-player::before {
    content: "🥈 ";
}

.player-row.rank-3 {
    background: rgba(205, 127, 50, 0.1);
}

.player-row.rank-3 .col-player::before {
    content: "🥉 ";
}

/* Hover effects */
.player-row:hover {
    background: rgba(255, 255, 255, 0.1) !important;
    transform: translateX(5px);
    transition: all 0.2s ease;
}

/* Scrollbar Styles */
.team-list::-webkit-scrollbar {
    width: 6px;
}

.team-list::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
}

.team-list::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 3px;
}

.team-list::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.5);
}
