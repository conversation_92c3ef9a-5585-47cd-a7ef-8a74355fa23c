body {
  font-family: Arial, sans-serif;
  background-color: rgba(0,0,0,0.75);
  color: white;
  margin: 0;
  padding: 0;
}

#attachment-menu {
  position: absolute;
  top: 10%;
  left: 50%;
  transform: translateX(-50%);
  width: 400px;
  background-color: #222;
  border-radius: 8px;
  padding: 15px;
  box-shadow: 0 0 10px #000;
}

#attachment-menu h2 {
  margin-top: 0;
  font-size: 20px;
  text-align: center;
}

#attachment-list {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  margin-top: 10px;
}

.attachment-item {
  width: 90px;
  margin: 5px;
  background-color: #333;
  border-radius: 6px;
  padding: 8px;
  text-align: center;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.attachment-item:hover {
  background-color: #555;
}

.attachment-item img {
  width: 64px;
  height: 64px;
  margin-bottom: 5px;
}

.attachment-item .name {
  font-size: 12px;
  margin-bottom: 3px;
}

.attachment-item .price {
  font-size: 11px;
  color: #ccc;
}

#close-btn {
  display: block;
  margin: 15px auto 0 auto;
  padding: 8px 20px;
  background-color: #444;
  border: none;
  border-radius: 6px;
  color: white;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

#close-btn:hover {
  background-color: #666;
}
