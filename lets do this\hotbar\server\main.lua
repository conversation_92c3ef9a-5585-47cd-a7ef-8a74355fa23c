-- Server-side hotbar management

-- Player hotbar data storage
local playerHotbars = {}

-- Initialize player hotbar when they join
AddEventHandler('playerJoining', function()
    local source = source
    playerHotbars[source] = {
        [1] = { name = "", icon = "", count = 0, weaponHash = nil },
        [2] = { name = "", icon = "", count = 0, weaponHash = nil },
        [3] = { name = "", icon = "", count = 0, weaponHash = nil },
        [4] = { name = "", icon = "", count = 0, weaponHash = nil },
        [5] = { name = "", icon = "", count = 0, weaponHash = nil }
    }
end)

-- Clean up when player leaves
AddEventHandler('playerDropped', function()
    local source = source
    playerHotbars[source] = nil
end)

-- Handle item usage
RegisterNetEvent('hotbar:useItem')
AddEventHandler('hotbar:useItem', function(slot, itemName)
    local source = source
    
    if not playerHotbars[source] or not playerHotbars[source][slot] then
        return
    end
    
    local item = playerHotbars[source][slot]
    
    if item.name == itemName and item.count > 0 then
        print("Player " .. source .. " used " .. itemName .. " from slot " .. slot)
        
        -- Decrease item count
        item.count = item.count - 1
        
        -- If count reaches 0, clear the slot
        if item.count <= 0 then
            playerHotbars[source][slot] = { name = "", icon = "", count = 0, weaponHash = nil }
        end
        
        -- Update client
        TriggerClientEvent('hotbar:updateItems', source, playerHotbars[source])
        
        -- Trigger item use event for other resources to handle
        TriggerEvent('hotbar:itemUsed', source, itemName, slot)
    end
end)

-- Function to set item in player's hotbar
function SetPlayerHotbarItem(playerId, slot, itemName, itemIcon, itemCount, weaponHash)
    if not playerHotbars[playerId] then
        return false
    end

    if slot >= 1 and slot <= 5 then
        playerHotbars[playerId][slot] = {
            name = itemName or "",
            icon = itemIcon or "",
            count = itemCount or 0,
            weaponHash = weaponHash or nil
        }

        TriggerClientEvent('hotbar:setItem', playerId, slot, itemName, itemIcon, itemCount, weaponHash)
        return true
    end

    return false
end

-- Function to clear player's hotbar slot
function ClearPlayerHotbarSlot(playerId, slot)
    if not playerHotbars[playerId] then
        return false
    end

    if slot >= 1 and slot <= 5 then
        playerHotbars[playerId][slot] = { name = "", icon = "", count = 0, weaponHash = nil }
        TriggerClientEvent('hotbar:clearSlot', playerId, slot)
        return true
    end

    return false
end

-- Function to get player's hotbar items
function GetPlayerHotbarItems(playerId)
    return playerHotbars[playerId] or {}
end

-- Export functions for other resources
exports('SetPlayerHotbarItem', SetPlayerHotbarItem)
exports('ClearPlayerHotbarSlot', ClearPlayerHotbarSlot)
exports('GetPlayerHotbarItems', GetPlayerHotbarItems)

-- Commands for testing (remove in production)
RegisterCommand('sethotbar', function(source, args)
    if #args >= 3 then
        local slot = tonumber(args[1])
        local itemName = args[2]
        local itemCount = tonumber(args[3]) or 1
        local itemIcon = args[4] or "📦"
        
        if slot and slot >= 1 and slot <= 5 then
            SetPlayerHotbarItem(source, slot, itemName, itemIcon, itemCount)
            print("Set hotbar slot " .. slot .. " to " .. itemName .. " x" .. itemCount)
        end
    else
        print("Usage: /sethotbar <slot> <itemname> <count> [icon]")
    end
end, false)

RegisterCommand('clearhotbar', function(source, args)
    if #args >= 1 then
        local slot = tonumber(args[1])
        if slot and slot >= 1 and slot <= 5 then
            ClearPlayerHotbarSlot(source, slot)
            print("Cleared hotbar slot " .. slot)
        end
    else
        print("Usage: /clearhotbar <slot>")
    end
end, false)
