# New Class Selection and Weapon Shop UI System

## Overview
The class selection and weapon shop UI has been completely redesigned to match the provided mockups with a modern, dark theme.

## Features Implemented

### 1. **Class Selection UI**
- Full-screen class selection with 5 classes (Assault, Medic, Engineer, Heavy, Scout)
- Visual lock indicators for classes that require higher levels
- Hover effects and smooth transitions
- Level requirements displayed clearly
- Automatic level checking before allowing selection

### 2. **Weapon Shop UI**
- Category-based weapon selection (Primary, Secondary, Special, Throwable)
- Real-time money display
- Level-based weapon unlocking system
- "Spawned" indicator for currently equipped weapon
- Prestige system support for high-level weapons
- Clean grid layout with weapon icons

### 3. **Database Integration**
- All purchases are saved to the database
- Money is deducted properly and synced
- Player stats (money, XP, level) are always current
- Weapon ownership could be extended similar to vehicle ownership

## How It Works

### Class Selection Flow:
1. Player interacts with the Classes ped (Press E)
2. UI shows all 5 classes with lock status based on player level
3. Clicking a locked class shows an error message
4. Clicking an unlocked class opens the weapon shop for that class

### Weapon Shop Flow:
1. Weapon shop opens with Primary weapons selected by default
2. Player can switch between categories (Primary, Secondary, Special, Throwable)
3. Locked weapons show a lock icon and level requirement
4. Clicking "Spawn" on an unlocked weapon:
   - Deducts money if weapon has a price
   - Gives the weapon to the player
   - Updates the UI to show "Spawned" status

## Level Requirements

### Classes:
- **Assault**: Level 1 (Unlocked by default)
- **Medic**: Level 5
- **Engineer**: Level 15
- **Heavy**: Level 25
- **Scout**: Level 40

### Weapons (Examples):
- **Primary**: Bullpup Rifle (Level 1), Carbine Rifle (Level 20), Assault Rifle (Level 55)
- **Secondary**: Pistol (Level 1), Combat Pistol (Level 10), Revolver (Level 40)
- **Special**: Special Rifle (Level 35), Carbine Rifle Mk2 (Level 55)
- **Throwable**: Grenade (Level 5), Smoke Grenade (Level 15), Sticky Bomb (Level 35)

## File Structure

### New Files:
- `html/script_new_ui.js` - Handles all new UI logic
- Class images should be placed in `html/images/classes/`
- Weapon icons should be placed in `html/images/guns/`

### Modified Files:
- `html/ui.html` - Added new UI structures
- `html/style.css` - Added new styles for both UIs
- `client.lua` - Updated to use new UI system

## Testing Commands

```lua
/showclasses - Manually open class selection
/testkillreward - Test kill reward system
/checkstats - Check your current stats
```

## Future Enhancements

### 1. **Weapon Ownership System**
Similar to vehicles, weapons could be:
- **Purchased**: Owned permanently, free to spawn
- **Rented**: Temporary use for a lower price
- Stored in database like vehicle ownership

### 2. **Class-Specific Loadouts**
Each class could have:
- Unique weapon restrictions
- Special abilities or perks
- Different health/armor values
- Class-specific vehicles

### 3. **Prestige System**
For weapons marked with prestige levels:
- Additional progression after max level
- Special weapon skins or variants
- Exclusive prestige rewards

## Notes for Implementation

1. **Image Assets**: You'll need to add the actual images for:
   - Class portraits (assault.jpg, medic.jpg, etc.)
   - Weapon icons (all weapon .png files)

2. **Weapon Prices**: Currently all weapons are set to price: 0 (free). Update the prices in `script_new_ui.js` as needed.

3. **Category Expansion**: More weapon categories can be added by updating the `weaponCategories` object.

4. **Server Validation**: The server already validates purchases, but you may want to add additional checks for class-specific restrictions.

## Troubleshooting

If the UI doesn't show:
1. Check browser console for errors (F8 -> F12)
2. Ensure all script files are loaded in ui.html
3. Verify NUI focus is being set properly
4. Check that player data is loaded before opening UI

The new UI system is fully integrated with the existing database and money system, ensuring all purchases and stats are properly tracked and saved.
