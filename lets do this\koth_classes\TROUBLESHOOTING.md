# KOTH Classes Troubleshooting Guide

## Testing Steps

### 1. Check if the resource is loaded:
```
ensure koth_classes
```

### 2. Test commands to verify functionality:

#### Force set medic class:
```
/testmedic
```
This will manually set your class to medic and add the med bag to slot 5.

#### Test med bag placement directly:
```
/testmedbag
```
This will trigger the med bag placement without using the hotbar.

#### Check current class status:
```
/checkclass
```
This will show your current class and abilities.

### 3. Check console (F8) for debug messages:
- Look for `[KOTH Classes]` messages
- Check for any error messages

### 4. Common Issues and Solutions:

#### Issue: Pressing 5 does nothing
**Solutions:**
1. Make sure you selected the Medic class from the Classes NPC
2. Use `/testmedic` to force set the class
3. Check if the hotbar resource is running: `ensure hotbar`
4. Try `/testmedbag` to test placement directly

#### Issue: Wrong image showing
**Solutions:**
1. Make sure `images/ifak.png` exists in the hotbar resource
2. The image path should be relative to the hotbar resource's HTML folder

#### Issue: Class not persisting
**Solutions:**
1. Check if the database column was created:
   ```sql
   SHOW COLUMNS FROM koth_players LIKE 'player_class';
   ```
2. Make sure MySQL is connected properly

### 5. Manual Database Fix:
If the player_class column is missing, run this SQL:
```sql
ALTER TABLE koth_players 
ADD COLUMN IF NOT EXISTS player_class VARCHAR(50) DEFAULT NULL;
```

### 6. Resource Load Order:
Make sure resources load in this order in server.cfg:
```
ensure mysql-async
ensure hotbar
ensure koth_teamsel
ensure koth_classes
```

### 7. Debug Mode:
Debug mode is enabled by default. Check console for detailed logs.

## Quick Test Procedure:

1. Restart the server
2. Join and select a team
3. Go to the Classes NPC and select Medic (requires level 5)
4. Check if Med Bag appears in slot 5
5. Press 5 to activate placement
6. If it doesn't work, use `/testmedic` then press 5 again
7. If still not working, use `/testmedbag` to test directly

## Still Not Working?

If none of the above works:
1. Check F8 console for any error messages
2. Make sure all resources are started properly
3. Try restarting the entire server
4. Check that the hotbar is working with weapons first
