# Files That Can Be Safely Deleted

These files were created during development/testing and are no longer needed:

## Test and Documentation Files
1. `kill_test.lua` - Test file for kill system (functionality already integrated)
2. `kill_system_fix.lua` - Old fix file (changes already applied)
3. `test_kill_system.md` - Testing documentation (no longer needed)
4. `test_ui_interaction.lua` - UI testing script (testing complete)
5. `UI_TEST_GUIDE.md` - Testing guide (no longer needed)

## Old Fix Documentation
6. `KILL_SYSTEM_FIX_README.md` - Old documentation (kill system working)
7. `ADMIN_PANEL_FIX.md` - Admin panel documentation (if not using admin panel)
8. `UI_VISIBILITY_FIX.md` - Old UI fix documentation
9. `WEAPON_RENTAL_SYSTEM.md` - Development notes (feature implemented)
10. `TEAM_SELECTION_FIX.md` - Team selection fix notes (already fixed)
11. `NEW_UI_SYSTEM.md` - UI development notes

## Unused HTML/JS Files
12. `html/script_new_ui.js` - Unused new UI script
13. `html/ui_fix.js` - Old UI fix script (changes integrated)
14. `html/ui.html` - Old UI file (using ui_simple.html instead)
15. `html/script_death.js` - Death system already integrated into main script.js

## Summary Files (Optional - keep if you want documentation)
16. `FINAL_IMPLEMENTATION_SUMMARY.md` - Can delete after reviewing

## Files to KEEP:
- `server.lua` - Main server script
- `client.lua` - Main client script  
- `client_death.lua` - Death system client script
- `fxmanifest.lua` - Resource manifest
- `html/ui_simple.html` - Main UI HTML
- `html/script.js` - Main UI JavaScript
- `html/style.css` - UI styling
- `html/images/` - All image folders and files
- `database.sql` - Database structure
- `sql_create_vehicle_ownership_table.sql` - Vehicle ownership table

## How to Delete:
You can delete these files manually or use this command in the terminal:
```bash
# From the koth_teamsel directory
rm kill_test.lua kill_system_fix.lua test_kill_system.md test_ui_interaction.lua UI_TEST_GUIDE.md KILL_SYSTEM_FIX_README.md ADMIN_PANEL_FIX.md UI_VISIBILITY_FIX.md WEAPON_RENTAL_SYSTEM.md TEAM_SELECTION_FIX.md NEW_UI_SYSTEM.md html/script_new_ui.js html/ui_fix.js html/ui.html
```

Note: Always backup your files before deleting in case you need to reference them later!
