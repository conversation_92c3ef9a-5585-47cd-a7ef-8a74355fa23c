-- Server-side class system
local playerClasses = {}
local activeMedBags = {}
local medBagIdCounter = 0

-- Debug print function
local function debugPrint(...)
    if Config.Debug then
        print('[KOTH Classes Server]', ...)
    end
end

-- Initialize database tables
MySQL.ready(function()
    -- Add player_class column if it doesn't exist
    MySQL.Async.execute([[
        ALTER TABLE koth_players 
        ADD COLUMN IF NOT EXISTS player_class VARCHAR(50) DEFAULT NULL
    ]], {}, function(rowsChanged)
        debugPrint('Database initialized - player_class column ready')
    end)
    
    -- Create cooldowns table
    MySQL.Async.execute([[
        CREATE TABLE IF NOT EXISTS koth_class_cooldowns (
            id INT AUTO_INCREMENT PRIMARY KEY,
            txid VARCHAR(50) NOT NULL,
            ability_slot INT NOT NULL,
            cooldown_end BIGINT NOT NULL,
            UNIQUE KEY unique_player_ability (txid, ability_slot)
        )
    ]], {}, function(rowsChanged)
        debugPrint('Database initialized - cooldowns table ready')
    end)
end)

-- Get player identifier
local function getPlayerIdentifier(source)
    local identifiers = GetPlayerIdentifiers(source)
    for _, id in ipairs(identifiers) do
        if string.find(id, "license:") then
            return id
        end
    end
    return nil
end

-- Player joined - don't auto-load class
AddEventHandler('playerJoining', function()
    local source = source
    local txid = getPlayerIdentifier(source)
    
    if txid then
        -- Don't auto-load class - let players choose via NPC
        debugPrint('Player', source, 'joined - no auto class assignment')
    end
end)

-- Request player class
RegisterNetEvent('koth_classes:requestPlayerClass')
AddEventHandler('koth_classes:requestPlayerClass', function()
    local source = source
    local txid = getPlayerIdentifier(source)
    
    if txid then
        MySQL.Async.fetchScalar('SELECT player_class FROM koth_players WHERE txid = @txid', {
            ['@txid'] = txid
        }, function(playerClass)
            if playerClass then
                playerClasses[source] = playerClass
                TriggerClientEvent('koth_classes:setPlayerClass', source, playerClass)
                debugPrint('Sent class', playerClass, 'to player', source)
            end
        end)
    end
end)

-- Player selects a class
RegisterNetEvent('koth_classes:selectClass')
AddEventHandler('koth_classes:selectClass', function(classId)
    local source = source
    local txid = getPlayerIdentifier(source)
    
    if not classId or not Config.Classes[classId] then
        debugPrint('Invalid class ID:', classId)
        return
    end
    
    -- Check if player meets level requirement
    MySQL.Async.fetchAll('SELECT level FROM koth_players WHERE txid = @txid', {
        ['@txid'] = txid
    }, function(result)
        if result[1] then
            local playerLevel = result[1].level or 1
            local requiredLevel = Config.Classes[classId].requiredLevel or 1
            
            if playerLevel >= requiredLevel then
                -- Update database
                MySQL.Async.execute('UPDATE koth_players SET player_class = @class WHERE txid = @txid', {
                    ['@class'] = classId,
                    ['@txid'] = txid
                }, function(rowsChanged)
                    playerClasses[source] = classId
                    TriggerClientEvent('koth_classes:setPlayerClass', source, classId)
                    debugPrint('Player', source, 'selected class:', classId)
                end)
            else
                TriggerClientEvent('koth:purchaseResult', source, false, 
                    string.format('Class locked! Requires level %d (you are level %d)', requiredLevel, playerLevel))
            end
        end
    end)
end)

-- Removed server-side hotbar handling as it's handled client-side

-- Place med bag
RegisterNetEvent('koth_classes:placeMedBag')
AddEventHandler('koth_classes:placeMedBag', function(data)
    local source = source
    
    -- Generate unique ID for this med bag
    medBagIdCounter = medBagIdCounter + 1
    local medBagId = 'medbag_' .. medBagIdCounter
    
    -- Store med bag data
    activeMedBags[medBagId] = {
        source = source,
        x = data.x,
        y = data.y,
        z = data.z,
        ability = data.ability,
        endTime = GetGameTimer() + (data.ability.duration * 1000)
    }
    
    -- Notify all clients about the med bag
    TriggerClientEvent('koth_classes:medBagPlaced', -1, {
        id = medBagId,
        source = source,
        x = data.x,
        y = data.y,
        z = data.z,
        ability = data.ability
    })
    
    debugPrint('Med bag placed by', source, 'at', data.x, data.y, data.z)
    
    -- Set timer to remove med bag
    SetTimeout(data.ability.duration * 1000, function()
        if activeMedBags[medBagId] then
            activeMedBags[medBagId] = nil
            TriggerClientEvent('koth_classes:removeMedBag', -1, medBagId)
            debugPrint('Med bag', medBagId, 'expired')
        end
    end)
end)

-- Place ammo bag
RegisterNetEvent('koth_classes:placeAmmoBag')
AddEventHandler('koth_classes:placeAmmoBag', function(data)
    local source = source
    
    -- Generate unique ID for this ammo bag
    medBagIdCounter = medBagIdCounter + 1
    local ammoBagId = 'ammobag_' .. medBagIdCounter
    
    -- Store ammo bag data
    activeMedBags[ammoBagId] = {
        source = source,
        x = data.x,
        y = data.y,
        z = data.z,
        ability = data.ability,
        endTime = GetGameTimer() + (data.ability.duration * 1000)
    }
    
    -- Notify all clients about the ammo bag
    TriggerClientEvent('koth_classes:ammoBagPlaced', -1, {
        id = ammoBagId,
        source = source,
        x = data.x,
        y = data.y,
        z = data.z,
        ability = data.ability
    })
    
    debugPrint('Ammo bag placed by', source, 'at', data.x, data.y, data.z)
    
    -- Set timer to remove ammo bag
    SetTimeout(data.ability.duration * 1000, function()
        if activeMedBags[ammoBagId] then
            activeMedBags[ammoBagId] = nil
            TriggerClientEvent('koth_classes:removeAmmoBag', -1, ammoBagId)
            debugPrint('Ammo bag', ammoBagId, 'expired')
        end
    end)
end)

-- Clean up when player drops
AddEventHandler('playerDropped', function(reason)
    local source = source
    playerClasses[source] = nil
    
    -- Remove any active med bags from this player
    for medBagId, medBag in pairs(activeMedBags) do
        if medBag.source == source then
            activeMedBags[medBagId] = nil
            TriggerClientEvent('koth_classes:removeMedBag', -1, medBagId)
            debugPrint('Removed med bag', medBagId, 'from dropped player', source)
        end
    end
end)

-- Export to get player class
exports('GetPlayerClass', function(source)
    return playerClasses[source]
end)

debugPrint('Server main.lua loaded')
