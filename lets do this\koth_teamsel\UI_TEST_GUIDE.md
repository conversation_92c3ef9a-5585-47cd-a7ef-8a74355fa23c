# UI Interaction Test Guide

## Quick Test Commands

After restarting the resource, use these commands to test UI functionality:

### 1. Test Class Selection UI
```
/testclassui
```
This simulates pressing E on the class selection ped. You should see:
- Class selection menu opens
- 5 class cards (<PERSON>, Medic, Engineer, Heavy, Scout)
- Classes locked based on your level
- Overlay darkens the background

### 2. Test Vehicle Shop UI
```
/testvehicleui
```
This simulates pressing E on the vehicle shop ped. You should see:
- Vehicle shop menu opens
- List of vehicles with Buy/Rent prices
- Your current money displayed at top
- Owned vehicles show "SPAWN" button instead of prices

### 3. Test Full UI Flow
```
/testfullui
```
This runs a complete UI test sequence:
1. Requests player data
2. Opens class menu for 3 seconds
3. Closes menu
4. Opens vehicle menu for 3 seconds
5. Closes menu

### 4. Check UI State
```
/checkui
```
Sends a test message to verify N<PERSON> is responding.

## Manual Testing Steps

### Test 1: Ped Interaction
1. Go to any team spawn (Red, Blue, or Green)
2. Look for two NPCs:
   - Marine (Classes Menu)
   - Mechanic (Vehicle Menu)
3. Walk up to each NPC (within 1.5 meters)
4. You should see text: "Click [E] To Open [Menu Name]"
5. Press E
6. The respective menu should open

### Test 2: UI Visibility
When a menu opens, verify:
- [ ] Dark overlay covers the game
- [ ] Menu appears at top of screen
- [ ] Mouse cursor is visible
- [ ] Can click on elements
- [ ] ESC key closes the menu

### Test 3: Class Selection
1. Open class menu (`/testclassui` or press E on marine)
2. Check that:
   - [ ] Assault class is unlocked (level 1)
   - [ ] Other classes show lock icon if below required level
   - [ ] Clicking a class opens weapon selection
   - [ ] Close button (X) works

### Test 4: Vehicle Shop
1. Open vehicle menu (`/testvehicleui` or press E on mechanic)
2. Check that:
   - [ ] Your money displays at top
   - [ ] Vehicles show Buy and Rent prices
   - [ ] Clicking Buy/Rent processes the purchase
   - [ ] Close button (X) works

## Console Monitoring

Watch F8 console for these messages:
- `[KOTH UI FIX] Showing UI element: [element-name]`
- `[KOTH UI FIX] Received message: [action]`
- `[UI TEST] [various test messages]`

## Common Issues & Solutions

### UI Opens But Is Invisible
- Check F8 console for errors
- Verify `html/ui_simple.html` is loading
- Check that overlay element exists in HTML

### UI Doesn't Open At All
- Verify peds spawned correctly
- Check distance to ped (must be < 1.5)
- Ensure `SetNuiFocus(true, true)` is called

### Mouse Cursor Not Showing
- NUI focus issue
- Check `SetNuiFocus` calls in client.lua

### Can't Close UI
- ESC key handler may be broken
- Use `/testfullui` to force close
- Check ui_fix.js ESC handler

## Expected Behavior

✅ **Working Correctly:**
- Pressing E on peds opens respective menus
- UI elements are visible and interactive
- Money/XP updates show in HUD
- Kill rewards show popup (+$50/150)
- ESC or X button closes menus

❌ **Not Working:**
- UI opens but is invisible
- No cursor when UI opens
- Can't interact with buttons
- Console errors about missing elements
