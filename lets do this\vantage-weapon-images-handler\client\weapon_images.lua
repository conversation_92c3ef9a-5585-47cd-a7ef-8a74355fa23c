-- Vantage Weapon Images Handler - Client Side
-- Provides centralized weapon image management without breaking existing systems

local WeaponImageHandler = {}

-- Complete weapon image mapping using Vantage-weapon-images
local weaponImageMap = {
    -- Assault Rifles
    ['WEAPON_ASSAULTRIFLE'] = 'nui://vantage-weapon-images-handler/images/Assault Rifles/Assault Rifle.png',
    ['WEAPON_ASSAULTRIFLE_MK2'] = 'nui://vantage-weapon-images-handler/images/Assault Rifles/Assault Rifle MK II.png',
    ['WEAPON_CARBINERIFLE'] = 'nui://vantage-weapon-images-handler/images/Assault Rifles/Carbine Rifle.png',
    ['WEAPON_CARBINERIFLE_MK2'] = 'nui://vantage-weapon-images-handler/images/Assault Rifles/Carbine Rifle MK II.png',
    ['WEAPON_ADVANCEDRIFLE'] = 'nui://vantage-weapon-images-handler/images/Assault Rifles/Advanced Rifle.png',
    ['WEAPON_SPECIALCARBINE'] = 'nui://vantage-weapon-images-handler/images/Assault Rifles/Special Carbine.png',
    ['WEAPON_SPECIALCARBINE_MK2'] = 'nui://vantage-weapon-images-handler/images/Assault Rifles/Special Carbine MK II.png',
    ['WEAPON_BULLPUPRIFLE'] = 'nui://vantage-weapon-images-handler/images/Assault Rifles/Bullpup Rifle.png',
    ['WEAPON_BULLPUPRIFLE_MK2'] = 'nui://vantage-weapon-images-handler/images/Assault Rifles/Bullpup Rifle MK II.png',
    ['WEAPON_COMPACTRIFLE'] = 'nui://vantage-weapon-images-handler/images/Assault Rifles/Compact Rifle.png',
    ['WEAPON_MILITARYRIFLE'] = 'nui://vantage-weapon-images-handler/images/Assault Rifles/Military Rifle.png',
    ['WEAPON_HEAVYRIFLE'] = 'nui://vantage-weapon-images-handler/images/Assault Rifles/Heavy Rifle.png',
    
    -- SMGs
    ['WEAPON_SMG'] = 'nui://vantage-weapon-images-handler/images/SMGs/SMG.png',
    ['WEAPON_SMG_MK2'] = 'nui://vantage-weapon-images-handler/images/SMGs/SMG MK II.png',
    ['WEAPON_ASSAULTSMG'] = 'nui://vantage-weapon-images-handler/images/SMGs/Assault SMG.png',
    ['WEAPON_COMBATPDW'] = 'nui://vantage-weapon-images-handler/images/SMGs/Combat PDW.png',
    ['WEAPON_MACHINEPISTOL'] = 'nui://vantage-weapon-images-handler/images/SMGs/Machine Pistol.png',
    ['WEAPON_MICROSMG'] = 'nui://vantage-weapon-images-handler/images/SMGs/Micro SMG.png',
    ['WEAPON_MINISMG'] = 'nui://vantage-weapon-images-handler/images/SMGs/Mini SMG.png',
    ['WEAPON_TACTICALSMG'] = 'nui://vantage-weapon-images-handler/images/SMGs/Tactical SMG.png',
    
    -- Pistols
    ['WEAPON_PISTOL'] = 'nui://vantage-weapon-images-handler/images/Pistols/Pistol.png',
    ['WEAPON_PISTOL_MK2'] = 'nui://vantage-weapon-images-handler/images/Pistols/Pistol MK II.png',
    ['WEAPON_COMBATPISTOL'] = 'nui://vantage-weapon-images-handler/images/Pistols/Combat Pistol.png',
    ['WEAPON_APPISTOL'] = 'nui://vantage-weapon-images-handler/images/Pistols/AP Pistol.png',
    ['WEAPON_PISTOL50'] = 'nui://vantage-weapon-images-handler/images/Pistols/Pistol .50.png',
    ['WEAPON_SNSPISTOL'] = 'nui://vantage-weapon-images-handler/images/Pistols/SNS Pistol.png',
    ['WEAPON_SNSPISTOL_MK2'] = 'nui://vantage-weapon-images-handler/images/Pistols/SNS Pistol MK II.png',
    ['WEAPON_HEAVYPISTOL'] = 'nui://vantage-weapon-images-handler/images/Pistols/Heavy Pistol.png',
    ['WEAPON_VINTAGEPISTOL'] = 'nui://vantage-weapon-images-handler/images/Pistols/Vintage Pistol.png',
    ['WEAPON_CERAMICPISTOL'] = 'nui://vantage-weapon-images-handler/images/Pistols/Ceramic Pistol.png',
    ['WEAPON_DOUBLEACTION'] = 'nui://vantage-weapon-images-handler/images/Pistols/Double-Action Revolver.png',
    ['WEAPON_REVOLVER'] = 'nui://vantage-weapon-images-handler/images/Pistols/Heavy Revolver.png',
    ['WEAPON_REVOLVER_MK2'] = 'nui://vantage-weapon-images-handler/images/Pistols/Heavy Revolver MK II.png',
    ['WEAPON_NAVYREVOLVER'] = 'nui://vantage-weapon-images-handler/images/Pistols/Navy Revolver.png',
    ['WEAPON_MARKSMANPISTOL'] = 'nui://vantage-weapon-images-handler/images/Pistols/Marksman Pistol.png',
    ['WEAPON_FLAREGUN'] = 'nui://vantage-weapon-images-handler/images/Pistols/Flare Gun.png',
    
    -- Shotguns
    ['WEAPON_PUMPSHOTGUN'] = 'nui://vantage-weapon-images-handler/images/Shotguns/Pump Shotgun.png',
    ['WEAPON_PUMPSHOTGUN_MK2'] = 'nui://vantage-weapon-images-handler/images/Shotguns/Pump Shotgun MK II.png',
    ['WEAPON_COMBATSHOTGUN'] = 'nui://vantage-weapon-images-handler/images/Shotguns/Combat Shotgun.png',
    ['WEAPON_ASSAULTSHOTGUN'] = 'nui://vantage-weapon-images-handler/images/Shotguns/Assault Shotgun.png',
    ['WEAPON_HEAVYSHOTGUN'] = 'nui://vantage-weapon-images-handler/images/Shotguns/Heavy Shotgun.png',
    ['WEAPON_SWEEPERSHOTGUN'] = 'nui://vantage-weapon-images-handler/images/Shotguns/Sweeper Shotgun.png',
    ['WEAPON_BULLPUPSHOTGUN'] = 'nui://vantage-weapon-images-handler/images/Shotguns/Bullpup Shotgun.png',
    ['WEAPON_DBSHOTGUN'] = 'nui://vantage-weapon-images-handler/images/Shotguns/Double Barrel Shotgun.png',
    ['WEAPON_SAWNOFFSHOTGUN'] = 'nui://vantage-weapon-images-handler/images/Shotguns/Sawed-off Shotgun.png',
    ['WEAPON_MUSKET'] = 'nui://vantage-weapon-images-handler/images/Shotguns/Musket.png',
    
    -- Sniper Rifles
    ['WEAPON_SNIPERRIFLE'] = 'nui://vantage-weapon-images-handler/images/Sniper Rifles/Sniper Rifle.png',
    ['WEAPON_HEAVYSNIPER'] = 'nui://vantage-weapon-images-handler/images/Sniper Rifles/Heavy Sniper.png',
    ['WEAPON_HEAVYSNIPER_MK2'] = 'nui://vantage-weapon-images-handler/images/Sniper Rifles/Heavy Sniper MK II.png',
    ['WEAPON_MARKSMANRIFLE'] = 'nui://vantage-weapon-images-handler/images/Sniper Rifles/Marksman Rifle.png',
    ['WEAPON_MARKSMANRIFLE_MK2'] = 'nui://vantage-weapon-images-handler/images/Sniper Rifles/Marksman Rifle MK II.png',
    ['WEAPON_PRECISIONRIFLE'] = 'nui://vantage-weapon-images-handler/images/Sniper Rifles/Precision Rifle.png',
    
    -- LMGs
    ['WEAPON_MG'] = 'nui://vantage-weapon-images-handler/images/LMGs/MG.png',
    ['WEAPON_COMBATMG'] = 'nui://vantage-weapon-images-handler/images/LMGs/Combat MG.png',
    ['WEAPON_COMBATMG_MK2'] = 'nui://vantage-weapon-images-handler/images/LMGs/Combat MG MK II.png',
    ['WEAPON_GUSENBERG'] = 'nui://vantage-weapon-images-handler/images/LMGs/Gusenberg Sweeper.png',
    
    -- Heavy Weapons
    ['WEAPON_MINIGUN'] = 'nui://vantage-weapon-images-handler/images/Heavy Weapons/Minigun.png',
    ['WEAPON_RPG'] = 'nui://vantage-weapon-images-handler/images/Heavy Weapons/RPG.png',
    ['WEAPON_GRENADELAUNCHER'] = 'nui://vantage-weapon-images-handler/images/Heavy Weapons/Grenade Launcher.png',
    ['WEAPON_COMPACTLAUNCHER'] = 'nui://vantage-weapon-images-handler/images/Heavy Weapons/Compact Grenade Launcher.png',
    ['WEAPON_HOMINGLAUNCHER'] = 'nui://vantage-weapon-images-handler/images/Heavy Weapons/Homing Launcher.png',
    ['WEAPON_FIREWORK'] = 'nui://vantage-weapon-images-handler/images/Heavy Weapons/Firework Launcher.png',
    ['WEAPON_RAILGUN'] = 'nui://vantage-weapon-images-handler/images/Heavy Weapons/Railgun.png',
    
    -- Thrown & Utility Weapons
    ['WEAPON_GRENADE'] = 'nui://vantage-weapon-images-handler/images/Thrown & Utility Weapons/Grenade.png',
    ['WEAPON_STICKYBOMB'] = 'nui://vantage-weapon-images-handler/images/Thrown & Utility Weapons/Sticky Bomb.png',
    ['WEAPON_PIPEBOMB'] = 'nui://vantage-weapon-images-handler/images/Thrown & Utility Weapons/Pipe Bomb.png',
    ['WEAPON_BZGAS'] = 'nui://vantage-weapon-images-handler/images/Thrown & Utility Weapons/Tear Gas.png',
    ['WEAPON_MOLOTOV'] = 'nui://vantage-weapon-images-handler/images/Thrown & Utility Weapons/Molotov Cocktail.png',
    ['WEAPON_PROXMINE'] = 'nui://vantage-weapon-images-handler/images/Thrown & Utility Weapons/Proximity Mine.png',
    ['WEAPON_FLARE'] = 'nui://vantage-weapon-images-handler/images/Thrown & Utility Weapons/Flare.png',
    ['WEAPON_BALL'] = 'nui://vantage-weapon-images-handler/images/Thrown & Utility Weapons/Ball.png',
    ['WEAPON_SNOWBALL'] = 'nui://vantage-weapon-images-handler/images/Thrown & Utility Weapons/Snowball.png',
    
    -- Melee Weapons
    ['WEAPON_KNIFE'] = 'nui://vantage-weapon-images-handler/images/Melee Weapons/Knife.png',
    ['WEAPON_SWITCHBLADE'] = 'nui://vantage-weapon-images-handler/images/Melee Weapons/Switchblade.png',
    ['WEAPON_MACHETE'] = 'nui://vantage-weapon-images-handler/images/Melee Weapons/Machete.png',
    ['WEAPON_HATCHET'] = 'nui://vantage-weapon-images-handler/images/Melee Weapons/Hatchet.png',
    ['WEAPON_BAT'] = 'nui://vantage-weapon-images-handler/images/Melee Weapons/Baseball Bat.png',
    ['WEAPON_CROWBAR'] = 'nui://vantage-weapon-images-handler/images/Melee Weapons/Crowbar.png',
    ['WEAPON_HAMMER'] = 'nui://vantage-weapon-images-handler/images/Melee Weapons/Hammer.png',
    ['WEAPON_GOLFCLUB'] = 'nui://vantage-weapon-images-handler/images/Melee Weapons/Golf Club.png',
    ['WEAPON_NIGHTSTICK'] = 'nui://vantage-weapon-images-handler/images/Melee Weapons/Nightstick.png',
    ['WEAPON_KNUCKLE'] = 'nui://vantage-weapon-images-handler/images/Melee Weapons/Knuckledusters.png',
    ['WEAPON_FLASHLIGHT'] = 'nui://vantage-weapon-images-handler/images/Melee Weapons/Flashlight.png',
    ['WEAPON_BOTTLE'] = 'nui://vantage-weapon-images-handler/images/Melee Weapons/Broken Bottle.png',
    ['WEAPON_DAGGER'] = 'nui://vantage-weapon-images-handler/images/Melee Weapons/Antique Cavalry Dagger.png',
    ['WEAPON_BATTLEAXE'] = 'nui://vantage-weapon-images-handler/images/Melee Weapons/Battle Axe.png',
    ['WEAPON_POOLCUE'] = 'nui://vantage-weapon-images-handler/images/Melee Weapons/Pool Cue.png',
    ['WEAPON_WRENCH'] = 'nui://vantage-weapon-images-handler/images/Melee Weapons/Pipe Wrench.png',
    ['WEAPON_STONE_HATCHET'] = 'nui://vantage-weapon-images-handler/images/Melee Weapons/Stone Hatchet.png'
}

-- Attachment image mapping
local attachmentImageMap = {
    ['attachment_extendedmag'] = 'nui://koth_teamsel/images of guns/attachment_extendedmag.png',
    ['attachment_extendedmag2'] = 'nui://koth_teamsel/images of guns/attachment_extendedmag2.png',
    ['attachment_extendedmag3'] = 'nui://koth_teamsel/images of guns/attachment_extendedmag3.png',
    ['attachment_suppressor'] = 'nui://koth_teamsel/images of guns/attachment_suppressor.png',
    ['attachment_suppressor2'] = 'nui://koth_teamsel/images of guns/attachment_suppressor2.png',
    ['attachment_flashlight'] = 'nui://koth_teamsel/images of guns/attachment_flashlight.png',
    ['attachment_grip'] = 'nui://koth_teamsel/images of guns/attachment_grip.png',
    ['attachment_scope'] = 'nui://koth_teamsel/images of guns/attachment_scope.png',
    ['attachment_scope2'] = 'nui://koth_teamsel/images of guns/attachment_scope2.png',
    ['attachment_reddot'] = 'nui://koth_teamsel/images of guns/attachment_reddot.png',
    ['attachment_heavybarrel'] = 'nui://koth_teamsel/images of guns/attachment_heavybarrel.png',
    ['attachment_muzzleboost'] = 'nui://koth_teamsel/images of guns/attachment_muzzleboost.png',
    ['attachment_engrave'] = 'nui://koth_teamsel/images of guns/attachment_engrave.png',
    ['attachment_engrave3'] = 'nui://koth_teamsel/images of guns/attachment_engrave3.png'
}

-- Class ability image mapping
local classAbilityImageMap = {
    ['ammo_bag'] = 'nui://koth_teamsel/images of guns/ammo_allammo.png',
    ['med_bag'] = 'nui://koth_teamsel/images of guns/ifak.png',
    ['armor_kit'] = 'nui://koth_teamsel/images of guns/lightarmour.png',
    ['radar'] = 'nui://koth_teamsel/images of guns/radar.png',
    ['radio'] = 'nui://koth_teamsel/images of guns/radio.png'
}

-- Default fallback image
local defaultWeaponImage = 'nui://vantage-weapon-images-handler/images/Pistols/Pistol.png'

-- Export function to get weapon image
function WeaponImageHandler.GetWeaponImage(weaponName)
    if not weaponName then
        return defaultWeaponImage
    end
    
    -- Normalize weapon name (ensure it starts with WEAPON_)
    local normalizedName = weaponName
    if not string.find(weaponName, 'WEAPON_') then
        normalizedName = 'WEAPON_' .. weaponName:upper()
    end
    
    local imagePath = weaponImageMap[normalizedName:upper()]
    if imagePath then
        return imagePath
    end
    
    -- Fallback to default image
    print('[VANTAGE-IMAGES] Warning: No image found for weapon:', normalizedName, '- using default')
    return defaultWeaponImage
end

-- Export function to get attachment image
function WeaponImageHandler.GetAttachmentImage(attachmentName)
    if not attachmentName then
        return 'nui://koth_teamsel/images of guns/attachment_scope.png'
    end
    
    local imagePath = attachmentImageMap[attachmentName:lower()]
    if imagePath then
        return imagePath
    end
    
    -- Fallback to default attachment image
    return 'nui://koth_teamsel/images of guns/attachment_scope.png'
end

-- Export function to get class ability image
function WeaponImageHandler.GetClassAbilityImage(abilityName)
    if not abilityName then
        return 'nui://koth_teamsel/images of guns/ifak.png'
    end
    
    local imagePath = classAbilityImageMap[abilityName:lower()]
    if imagePath then
        return imagePath
    end
    
    -- Fallback to default ability image
    return 'nui://koth_teamsel/images of guns/ifak.png'
end

-- Export functions for other resources to use
exports('GetWeaponImage', WeaponImageHandler.GetWeaponImage)
exports('GetAttachmentImage', WeaponImageHandler.GetAttachmentImage)
exports('GetClassAbilityImage', WeaponImageHandler.GetClassAbilityImage)

print('[VANTAGE-IMAGES] Weapon image handler loaded with', #weaponImageMap, 'weapon mappings')
