-- Remove Ambient Peds Resource - Enhanced Version
-- This script aggressively removes all ambient peds, vehicles, police, and EMS

-- Disable dispatch services before anything else
for i = 1, 15 do
    EnableDispatchService(i, false)
end

-- Main density control thread (runs every frame)
Citizen.CreateThread(function()
    while true do
        -- Set all densities to 0
        SetPedDensityMultiplierThisFrame(0.0)
        SetScenarioPedDensityMultiplierThisFrame(0.0, 0.0)
        SetRandomVehicleDensityMultiplierThisFrame(0.0)
        SetParkedVehicleDensityMultiplierThisFrame(0.0)
        SetVehicleDensityMultiplierThisFrame(0.0)
        SetNumberOfParkedVehicles(0)
        
        -- Disable cops
        SetCreateRandomCops(false)
        SetCreateRandomCopsNotOnScenarios(false)
        SetCreateRandomCopsOnScenarios(false)
        
        -- Disable specific vehicles
        SetGarbageTrucks(false)
        SetRandomBoats(false)
        SetRandomTrains(false)
        
        -- Keep dispatch services disabled
        for i = 1, 15 do
            EnableDispatchService(i, false)
        end
        
        -- Disable police response
        SetPoliceIgnorePlayer(PlayerId(), true)
        SetDispatchCopsForPlayer(PlayerId(), false)
        SetMaxWantedLevel(0)
        
        -- Audio flags
        DistantCopCarSirens(false)
        SetAudioFlag("PoliceScannerDisabled", true)
        
        Citizen.Wait(0)
    end
end)

-- Clear wanted level constantly
Citizen.CreateThread(function()
    while true do
        if GetPlayerWantedLevel(PlayerId()) > 0 then
            ClearPlayerWantedLevel(PlayerId())
            SetPlayerWantedLevel(PlayerId(), 0, false)
            SetPlayerWantedLevelNow(PlayerId(), false)
        end
        
        SetPoliceIgnorePlayer(PlayerId(), true)
        SetEveryoneIgnorePlayer(PlayerId(), true)
        SetPlayerCanBeHassledByGangs(PlayerId(), false)
        SetIgnoreLowPriorityShockingEvents(PlayerId(), true)
        
        Citizen.Wait(100)
    end
end)

-- List of ped models to keep (mechanic and army)
local keepPedModels = {
    -- Mechanic models
    GetHashKey("s_m_y_xmech_01"),
    GetHashKey("s_m_y_xmech_02"),
    GetHashKey("s_m_y_xmech_02_mp"),
    GetHashKey("s_m_m_autoshop_01"),
    GetHashKey("s_m_m_autoshop_02"),
    
    -- Army models
    GetHashKey("s_m_y_marine_01"),
    GetHashKey("s_m_y_marine_02"),
    GetHashKey("s_m_y_marine_03"),
    GetHashKey("s_m_m_marine_01"),
    GetHashKey("s_m_m_marine_02"),
    GetHashKey("s_m_y_armymech_01"),
    GetHashKey("s_m_y_blackops_01"),
    GetHashKey("s_m_y_blackops_02"),
    GetHashKey("s_m_y_blackops_03"),
    
    -- Add any other specific models you need to keep here
}

-- Function to check if a ped should be kept
local function shouldKeepPed(ped)
    -- Check if it's a player
    if IsPedAPlayer(ped) then
        return true
    end
    
    -- Check if it's a model we want to keep
    local pedModel = GetEntityModel(ped)
    for _, keepModel in ipairs(keepPedModels) do
        if pedModel == keepModel then
            return true
        end
    end
    
    -- Check if ped has a relationship group that indicates it's important
    -- This helps preserve peds spawned by scripts for interaction
    local pedGroup = GetPedRelationshipGroupHash(ped)
    if pedGroup == GetHashKey("MISSION1") or 
       pedGroup == GetHashKey("MISSION2") or 
       pedGroup == GetHashKey("MISSION3") or 
       pedGroup == GetHashKey("MISSION4") or 
       pedGroup == GetHashKey("MISSION5") or 
       pedGroup == GetHashKey("MISSION6") or 
       pedGroup == GetHashKey("MISSION7") or 
       pedGroup == GetHashKey("MISSION8") then
        return true
    end
    
    return false
end

-- Aggressive ped removal (with exceptions)
Citizen.CreateThread(function()
    while true do
        local playerPed = PlayerPedId()
        local pos = GetEntityCoords(playerPed)
        
        -- Clear area of all cops (they're not in our keep list)
        ClearAreaOfCops(pos.x, pos.y, pos.z, 5000.0, 0)
        
        -- Don't use ClearAreaOfPeds as it would remove our kept peds
        -- Instead, manually check and remove only unwanted peds
        
        -- Delete all non-player peds except those we want to keep
        local allPeds = GetGamePool('CPed')
        for _, ped in ipairs(allPeds) do
            if DoesEntityExist(ped) and not shouldKeepPed(ped) then
                SetEntityAsMissionEntity(ped, true, true)
                DeleteEntity(ped)
            end
        end
        
        Citizen.Wait(250)
    end
end)

-- Register the decorator for player vehicles
DecorRegister("PlayerVehicle", 3)

-- Aggressive vehicle removal (modified to preserve player-owned vehicles)
Citizen.CreateThread(function()
    -- Wait a bit for everything to initialize
    Citizen.Wait(5000)
    
    while true do
        local playerPed = PlayerPedId()
        local pos = GetEntityCoords(playerPed)
        
        -- Clear area of vehicles (only ambient spawned vehicles)
        -- Don't use ClearAreaOfVehicles as it removes player vehicles too
        
        -- Delete only ambient/NPC vehicles, not player-owned ones
        local allVehicles = GetGamePool('CVehicle')
        for _, vehicle in ipairs(allVehicles) do
            if DoesEntityExist(vehicle) then
                -- Check if this vehicle has been owned by a player
                local hasBeenOwnedByPlayer = false
                
                -- Check multiple methods to determine if it's a player vehicle
                if DecorExistOn(vehicle, "PlayerVehicle") and DecorGetBool(vehicle, "PlayerVehicle") then
                    hasBeenOwnedByPlayer = true
                elseif Entity(vehicle).state.playerOwned then
                    hasBeenOwnedByPlayer = true
                elseif GetVehicleHasBeenOwnedByPlayer(vehicle) then
                    hasBeenOwnedByPlayer = true
                end
                
                -- Also check if the vehicle was created by a player (network check)
                if NetworkGetEntityIsNetworked(vehicle) then
                    local netId = NetworkGetNetworkIdFromEntity(vehicle)
                    if NetworkDoesNetworkIdExist(netId) then
                        -- This is a networked vehicle, likely player-spawned
                        hasBeenOwnedByPlayer = true
                    end
                end
                
                -- Check if any player is currently in the vehicle
                local hasPlayer = false
                for i = -1, GetVehicleMaxNumberOfPassengers(vehicle) - 1 do
                    local ped = GetPedInVehicleSeat(vehicle, i)
                    if DoesEntityExist(ped) and IsPedAPlayer(ped) then
                        hasPlayer = true
                        break
                    end
                end
                
                -- Only delete if it's not a player vehicle and has no players
                if not hasBeenOwnedByPlayer and not hasPlayer then
                    -- Additional check: is this a vehicle that was recently exited by a player?
                    local driver = GetPedInVehicleSeat(vehicle, -1)
                    if not DoesEntityExist(driver) or not IsPedAPlayer(driver) then
                        -- This is an ambient vehicle, safe to delete
                        SetEntityAsMissionEntity(vehicle, true, true)
                        DeleteEntity(vehicle)
                    end
                end
            end
        end
        
        Citizen.Wait(250)
    end
end)

-- Disable all scenarios
Citizen.CreateThread(function()
    while true do
        local scenarios = {
            'WORLD_VEHICLE_ATTRACTOR',
            'WORLD_VEHICLE_AMBULANCE',
            'WORLD_VEHICLE_BICYCLE_BMX',
            'WORLD_VEHICLE_BICYCLE_BMX_BALLAS',
            'WORLD_VEHICLE_BICYCLE_BMX_FAMILY',
            'WORLD_VEHICLE_BICYCLE_BMX_HARMONY',
            'WORLD_VEHICLE_BICYCLE_BMX_VAGOS',
            'WORLD_VEHICLE_BICYCLE_MOUNTAIN',
            'WORLD_VEHICLE_BICYCLE_ROAD',
            'WORLD_VEHICLE_BIKE_OFF_ROAD_RACE',
            'WORLD_VEHICLE_BIKER',
            'WORLD_VEHICLE_BOAT_IDLE',
            'WORLD_VEHICLE_BOAT_IDLE_ALAMO',
            'WORLD_VEHICLE_BOAT_IDLE_MARQUIS',
            'WORLD_VEHICLE_BROKEN_DOWN',
            'WORLD_VEHICLE_BUSINESSMEN',
            'WORLD_VEHICLE_HELI_LIFEGUARD',
            'WORLD_VEHICLE_CLUCKIN_BELL_TRAILER',
            'WORLD_VEHICLE_CONSTRUCTION_SOLO',
            'WORLD_VEHICLE_CONSTRUCTION_PASSENGERS',
            'WORLD_VEHICLE_DRIVE_PASSENGERS',
            'WORLD_VEHICLE_DRIVE_PASSENGERS_LIMITED',
            'WORLD_VEHICLE_DRIVE_SOLO',
            'WORLD_VEHICLE_FIRE_TRUCK',
            'WORLD_VEHICLE_EMPTY',
            'WORLD_VEHICLE_MARIACHI',
            'WORLD_VEHICLE_MECHANIC',
            'WORLD_VEHICLE_MILITARY_PLANES_BIG',
            'WORLD_VEHICLE_MILITARY_PLANES_SMALL',
            'WORLD_VEHICLE_PARK_PARALLEL',
            'WORLD_VEHICLE_PARK_PERPENDICULAR_NOSE_IN',
            'WORLD_VEHICLE_PASSENGER_EXIT',
            'WORLD_VEHICLE_POLICE_BIKE',
            'WORLD_VEHICLE_POLICE_CAR',
            'WORLD_VEHICLE_POLICE',
            'WORLD_VEHICLE_POLICE_NEXT_TO_CAR',
            'WORLD_VEHICLE_QUARRY',
            'WORLD_VEHICLE_SALTON',
            'WORLD_VEHICLE_SALTON_DIRT_BIKE',
            'WORLD_VEHICLE_SECURITY_CAR',
            'WORLD_VEHICLE_STREETRACE',
            'WORLD_VEHICLE_TOURBUS',
            'WORLD_VEHICLE_TOURIST',
            'WORLD_VEHICLE_TANDL',
            'WORLD_VEHICLE_TRACTOR',
            'WORLD_VEHICLE_TRACTOR_BEACH',
            'WORLD_VEHICLE_TRUCK_LOGS',
            'WORLD_VEHICLE_TRUCKS_TRAILERS',
            'WORLD_VEHICLE_DISTANT_EMPTY_GROUND',
            'WORLD_HUMAN_PAPARAZZI',
            'WORLD_HUMAN_DRUG_DEALER',
            'WORLD_HUMAN_DRUG_DEALER_HARD',
            'WORLD_HUMAN_COP_IDLES',
            'WORLD_HUMAN_GUARD_PATROL',
            'WORLD_HUMAN_GUARD_STAND',
            'WORLD_HUMAN_GUARD_STAND_ARMY',
            'WORLD_HUMAN_SECURITY_SHINE_TORCH',
            'WORLD_COP_IDLES',
            'WORLD_COP_INVESTIGATE',
            'WORLD_GANG_MEMBERS',
            'WORLD_GANG_HANG_OUT'
        }
        
        for _, scenario in ipairs(scenarios) do
            SetScenarioTypeEnabled(scenario, false)
        end
        
        -- Disable scenario groups
        SetScenarioGroupEnabled("COPS", false)
        SetScenarioGroupEnabled("GANG_MEMBERS", false)
        SetScenarioGroupEnabled("QUARRY", false)
        SetScenarioGroupEnabled("Biker", false)
        
        Citizen.Wait(10000) -- Only need to do this every 10 seconds
    end
end)

-- Remove vehicle generators
Citizen.CreateThread(function()
    while true do
        RemoveVehiclesFromGeneratorsInArea(-10000.0, -10000.0, -1000.0, 10000.0, 10000.0, 1000.0)
        
        -- Suppress police vehicle models
        local policeModels = {
            "police", "police2", "police3", "police4", "policeb", "polmav",
            "policeold1", "policeold2", "policet", "sheriff", "sheriff2",
            "ambulance", "firetruk", "fbi", "fbi2", "pbus", "riot", "riot2", "swat"
        }
        
        for _, model in ipairs(policeModels) do
            SetVehicleModelIsSuppressed(GetHashKey(model), true)
        end
        
        Citizen.Wait(5000)
    end
end)

-- Set ped non-creation area
Citizen.CreateThread(function()
    while true do
        SetPedNonCreationArea(-10000.0, -10000.0, -10000.0, 10000.0, 10000.0, 10000.0)
        SetAllVehicleGeneratorsActiveInArea(-10000.0, -10000.0, -10000.0, 10000.0, 10000.0, 10000.0, false, false)
        
        Citizen.Wait(5000)
    end
end)

-- Disable relationships
Citizen.CreateThread(function()
    local groups = {
        "PLAYER", "CIVMALE", "CIVFEMALE", "COP", "GANG_1", "GANG_2", "GANG_9", "GANG_10",
        "AMBIENT_GANG_LOST", "AMBIENT_GANG_MEXICAN", "AMBIENT_GANG_FAMILY", "AMBIENT_GANG_BALLAS",
        "AMBIENT_GANG_MARABUNTE", "AMBIENT_GANG_CULT", "AMBIENT_GANG_SALVA", "AMBIENT_GANG_WEICHENG",
        "AMBIENT_GANG_HILLBILLY", "DEALER", "HATES_PLAYER", "PRIVATE_SECURITY", "SECURITY_GUARD",
        "ARMY", "MEDIC", "FIREMAN"
    }
    
    for _, group in ipairs(groups) do
        SetRelationshipBetweenGroups(1, GetHashKey("PLAYER"), GetHashKey(group))
        SetRelationshipBetweenGroups(1, GetHashKey(group), GetHashKey("PLAYER"))
    end
end)

-- Additional cleanup
Citizen.CreateThread(function()
    while true do
        -- Remove police blips
        RemoveBlip(GetFirstBlipInfoId(1))
        RemoveBlip(GetFirstBlipInfoId(57))
        RemoveBlip(GetFirstBlipInfoId(60))
        
        -- Clear help messages
        ClearAllHelpMessages()
        
        -- Disable police radar
        SetPoliceRadarBlips(false)
        
        -- Set ideal spawn distance to 0
        SetDispatchIdealSpawnDistance(0.0)
        
        Citizen.Wait(1000)
    end
end)

-- Audio scene management
Citizen.CreateThread(function()
    StartAudioScene("CHARACTER_CHANGE_IN_SKY_SCENE")
    StartAudioScene("FBI_HEIST_H5_MUTE_AMBIENCE_SCENE")
    SetAudioFlag("DisableFlightMusic", true)
    SetAudioFlag("PoliceScannerDisabled", true)
    
    print("^2[Remove-Ambient-Peds] ^7Enhanced removal system activated.")
    print("^2[Remove-Ambient-Peds] ^7All ambient peds, vehicles, police, and EMS should now be removed.")
end)

-- Nuclear option - constant area clearing (modified to preserve kept peds and player vehicles)
Citizen.CreateThread(function()
    while true do
        local playerCoords = GetEntityCoords(PlayerPedId())
        
        -- Clear cops (they're not in our keep list)
        ClearAreaOfCops(playerCoords.x, playerCoords.y, playerCoords.z, 10000.0, 0)
        
        -- Don't use ClearAreaOfVehicles as it removes player vehicles
        -- The aggressive vehicle removal thread above handles selective removal
        
        -- Clear objects and projectiles
        ClearAreaOfObjects(playerCoords.x, playerCoords.y, playerCoords.z, 10000.0, 0)
        ClearAreaOfProjectiles(playerCoords.x, playerCoords.y, playerCoords.z, 10000.0, 0)
        
        -- Don't use ClearAreaOfPeds here as it would remove our kept peds
        -- The aggressive ped removal thread above handles selective removal
        
        Citizen.Wait(2000) -- Every 2 seconds
    end
end)

-- Print kept models for debugging
Citizen.CreateThread(function()
    Citizen.Wait(5000) -- Wait for resource to fully load
    print("^2[Remove-Ambient-Peds] ^7The following ped models are preserved:")
    print("^2[Remove-Ambient-Peds] ^7- Mechanics: s_m_y_xmech_01, s_m_y_xmech_02, s_m_m_autoshop_01/02")
    print("^2[Remove-Ambient-Peds] ^7- Army: s_m_y_marine_01/02/03, s_m_m_marine_01/02, s_m_y_blackops_01/02/03")
    print("^2[Remove-Ambient-Peds] ^7- Mission peds with relationship groups MISSION1-8")
end)
