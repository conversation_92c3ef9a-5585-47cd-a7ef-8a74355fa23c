# Player Leaderboard - Troubleshooting Guide

## SQL Error: Unknown column 'current_team'

If you're getting the error about `current_team` column not existing, here are the solutions:

### Solution 1: Check if column exists
Run this SQL query in your phpMyAdmin:
```sql
SHOW COLUMNS FROM koth_players;
```

### Solution 2: Add the missing column
If `current_team` column is missing, add it:
```sql
ALTER TABLE koth_players ADD COLUMN current_team varchar(10) DEFAULT NULL;
```

### Solution 3: Check for typos or encoding issues
Sometimes the column might have invisible characters. Try:
```sql
-- Drop and recreate the column
ALTER TABLE koth_players DROP COLUMN current_team;
ALTER TABLE koth_players ADD COLUMN current_team varchar(10) DEFAULT NULL;
```

### Solution 4: Use the debug command
In the server console, run:
```
checkleaderboarddb
```
This will show all available columns in your koth_players table.

## Changes in the Fixed Version

The fixed version (`main_fixed.lua`) includes:

1. **Robust Query Handling**: Instead of filtering by team in SQL, we fetch all players and filter in Lua
2. **Better Error Handling**: Uses pcall to catch database errors
3. **Debug Logging**: More detailed console output to help diagnose issues
4. **Debug Command**: Added `/checkleaderboarddb` command (server console only)

## Testing Steps

1. **Check Database Structure**:
   - Run the debug command in server console
   - Verify all columns exist

2. **Test with a Player**:
   - Join the server
   - Make sure you have a team assigned
   - Press F7 to open leaderboard
   - Check server console for debug messages

3. **Manual Database Check**:
   ```sql
   SELECT txid, player_name, current_team, kills, deaths 
   FROM koth_players 
   WHERE current_team IS NOT NULL;
   ```

## Common Issues

### Issue: Players not showing in leaderboard
- Make sure players have `current_team` set (not NULL)
- Check if TX IDs are properly formatted in database

### Issue: TX IDs showing as 0
- Verify `txid` column has proper format: `license:xxxxxx`
- The system extracts numbers after "license:"

### Issue: Teams showing empty
- Players might not have teams assigned
- Run: `UPDATE koth_players SET current_team = 'red' WHERE current_team IS NULL;`

## Database Requirements

Your `koth_players` table must have these columns:
- `id` (int)
- `txid` (varchar) - Format: "license:123456"
- `player_name` (varchar)
- `current_team` (varchar) - Values: 'red', 'blue', 'green', or NULL
- `kills` (int)
- `deaths` (int)
- `zone_kills` (int)
- `level` (int)
- `money` (int)
- `xp` (int)

## Quick Fix Script

If you need to quickly fix your database, run this:
```sql
-- Ensure current_team column exists
ALTER TABLE koth_players 
ADD COLUMN IF NOT EXISTS current_team varchar(10) DEFAULT NULL;

-- Set some test teams for existing players
UPDATE koth_players 
SET current_team = CASE 
    WHEN id % 3 = 0 THEN 'red'
    WHEN id % 3 = 1 THEN 'blue'
    ELSE 'green'
END
WHERE current_team IS NULL;
