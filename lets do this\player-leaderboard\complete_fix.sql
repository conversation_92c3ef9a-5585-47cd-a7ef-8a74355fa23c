-- COMPLETE FIX FOR PLAYER LEADERBOARD
-- Run these queries one by one in phpMyAdmin

-- Step 1: Check what columns exist in your table
SHOW COLUMNS FROM koth_players;

-- Step 2: If 'current_team' column is missing, add it
-- (Skip this if the column already exists)
ALTER TABLE koth_players 
ADD COLUMN IF NOT EXISTS current_team VARCHAR(10) DEFAULT NULL;

-- Step 3: Assign teams to all players
UPDATE koth_players 
SET current_team = CASE 
    WHEN (id % 3) = 0 THEN 'red'
    WHEN (id % 3) = 1 THEN 'blue'
    ELSE 'green'
END;

-- Step 4: Verify teams are assigned
SELECT current_team, COUNT(*) as player_count 
FROM koth_players 
GROUP BY current_team;

-- Step 5: Check some sample data
SELECT id, player_name, current_team, kills, deaths, zone_kills 
FROM koth_players 
LIMIT 10;

-- If you still have issues, check for NULL or empty player names
SELECT COUNT(*) as empty_names 
FROM koth_players 
WHERE player_name IS NULL OR player_name = '';

-- Update empty player names if needed
UPDATE koth_players 
SET player_name = CONCAT('Player', id) 
WHERE player_name IS NULL OR player_name = '';
