# Vehicle Cleanup System

This resource automatically deletes vehicles after 3 minutes of player inactivity to prevent vehicle clutter on the server.

## Features

- **3-Minute Timer**: Vehicles are deleted 3 minutes after the player exits
- **Timer Reset**: Timer resets every time the player re-enters their vehicle
- **Warning System**: Players receive a warning 30 seconds before deletion
- **Network Sync**: <PERSON><PERSON><PERSON> handles vehicle deletion across the network
- **Configurable**: Easy configuration through `shared/config.lua`

## Installation

1. Copy the `vehicle-cleanup` folder to your resources directory
2. Add `ensure vehicle-cleanup` to your server.cfg
3. Restart your server or use `refresh` and `ensure vehicle-cleanup`

## Configuration

Edit `shared/config.lua` to customize:

```lua
Config.DeleteTime = 180 -- Time in seconds (default: 3 minutes)
Config.Notifications.warningTime = 30 -- Warning time in seconds
Config.Debug = false -- Enable debug messages
```

### Excluding Vehicle Classes

You can exclude certain vehicle types from deletion:

```lua
Config.ExcludedClasses = {
    13, -- Cycles (bicycles)
    14, -- Boats
    15, -- Helicopters
    16, -- Planes
}
```

## How It Works

1. When a player exits a vehicle, a 3-minute countdown starts
2. If the player re-enters the vehicle, the timer is cancelled
3. At 30 seconds remaining, the player receives a warning notification
4. After 3 minutes, the vehicle is deleted and the player is notified
5. The system properly handles network ownership for deletion

## Commands

- `/vehicletimers` - Shows active vehicle timers (only available in debug mode)

## Notes

- Only tracks vehicles that players have entered
- Properly handles multiple vehicles per player
- Cleans up timers when vehicles are deleted by other means
- Server-side fallback for vehicles the client can't delete

## Performance

- Minimal performance impact
- Uses efficient timer system
- Only checks vehicle status 4 times per second
- Timers check every second

## Compatibility

- Works with all vehicle spawn systems
- Compatible with vehicle ownership systems
- Does not interfere with job vehicles or mission vehicles
